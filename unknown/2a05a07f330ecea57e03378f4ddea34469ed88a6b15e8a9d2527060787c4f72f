<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Z-Out Report</title>
    <style>
        :root {
            --primary-color: #5e35b1;
            --secondary-color: #7c4dff;
            --accent-color: #b388ff;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --danger-color: #f44336;
            --info-color: #2196f3;
            --light-gray: #f5f5f5;
            --medium-gray: #e0e0e0;
            --dark-gray: #757575;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 13px;
            margin: 20px;
            color: #333;
            line-height: 1.5;
        }

        .report-header {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
            padding-top: 20px;
            min-height: 120px;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 20px;
        }

        h1 {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            color: var(--primary-color);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }

        .report-details {
            font-size: 16px;
            margin-top: 10px;
            color: #444;
        }

        .report-details p {
            margin: 6px 0;
            font-size: 16px;
        }

        .label {
            font-weight: bold;
            color: var(--dark-gray);
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-top: 40px;
            margin-bottom: 12px;
            color: var(--primary-color);
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 6px;
            page-break-inside: avoid;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 12px;
            page-break-inside: avoid;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        th,
        td {
            padding: 12px;
            border: 1px solid var(--medium-gray);
            text-align: left;
            font-size: 13px;
        }

        th {
            background-color: var(--primary-color);
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 0.5px;
        }

        td.right,
        th.right {
            text-align: right;
        }

        tr:nth-child(even) {
            background-color: var(--light-gray);
        }

        .total-row {
            font-weight: bold;
            background-color: #e8f5e9;
        }

        .positive {
            color: var(--success-color);
            font-weight: bold;
        }

        .negative {
            color: var(--danger-color);
            font-weight: bold;
        }

        .neutral {
            color: var(--info-color);
            font-weight: bold;
        }

        hr {
            margin: 30px 0;
            border: none;
            border-top: 1px dashed var(--medium-gray);
        }

        .footer {
            margin-top: 50px;
            font-size: 10px;
            text-align: center;
            color: var(--dark-gray);
            padding: 10px;
            border-top: 1px solid var(--medium-gray);
            page-break-before: avoid;
        }

        .generated-by {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
            color: var(--primary-color);
        }

        .report-logo {
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            opacity: 0.15;
            z-index: 0;
        }

        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            font-size: 12px;
            color: var(--dark-gray);
        }

        .page-number {
            margin: 0 5px;
        }

        .page-number:before {
            content: "Page " counter(page);
        }

        .page-break {
            page-break-before: always;
            break-before: page;
        }

        @media print {
            body {
                margin: 0;
                padding: 20px;
                font-size: 11px;
            }

            h1,
            h2,
            h3,
            table,
            tr,
            td,
            th {
                page-break-inside: avoid;
                break-inside: avoid;
            }

            hr {
                page-break-before: auto;
                break-before: auto;
            }

            .pagination {
                position: fixed;
                bottom: 30px;
                width: 100%;
            }

            table {
                font-size: 11px;
            }

            th,
            td {
                padding: 8px;
            }
        }

        /* Color coding for specific cells */
        .cash-positive {
            background-color: rgba(76, 175, 80, 0.1);
        }

        .cash-negative {
            background-color: rgba(244, 67, 54, 0.1);
        }

        .payment-method {
            background-color: rgba(33, 150, 243, 0.1);
        }

        .summary-total {
            background-color: rgba(121, 85, 72, 0.1);
        }
    </style>
</head>

<body>

    <div class="report-header">
        <img src="{{image}}" alt="Logo" class="report-logo" />
        <h1>Z-Out Report - {{facilityName}}</h1>

        <div class="report-details">
            <p class="generated-by">Generated By - {{generatedBy}} on {{day}}, {{currentDate}}</p>
            <p><span class="label">From:</span> {{startDate}} | {{startTime}}</p>
            <p><span class="label">To:</span> {{currentDate}} | {{currentTime}}</p>
        </div>
    </div>

    <h3 class="section-title">Cash Section</h3>
    <table>
        <tr>
            <th>Starting Cash</th>
            <td class="right">₹{{cashSectionData.startingAmount}}</td>
        </tr>
        <tr class="cash-positive">
            <th>Cash Sales</th>
            <td class="right">₹{{cashSectionData.cashSales}}</td>
        </tr>
        <tr class="cash-negative">
            <th>Petty Cash Out</th>
            <td class="right">- ₹{{cashSectionData.pettyAmount}}</td>
        </tr>
        <tr class="total-row">
            <th>Total</th>
            <td class="right">₹{{cashSectionData.totalCash}}</td>
        </tr>
        <tr>
            <th>Drawer Cash</th>
            <td class="right">₹{{cashSectionData.drawerAmount}}</td>
        </tr>
        <tr>
            <th>Leave Cash</th>
            <td class="right">₹{{cashSectionData.leaveAmount}}</td>
        </tr>
        <tr class="{{#if cashSectionData.overUnderPositive}}positive{{else}}negative{{/if}}">
            <th>Cash Over/Under</th>
            <td class="right">₹{{cashSectionData.overUnder}}</td>
        </tr>
        <tr class="summary-total">
            <th>Cash Deposit Amount</th>
            <td class="right">₹{{cashSectionData.depositAmount}}</td>
        </tr>
    </table>

    <h3 class="section-title">Other Payments</h3>
    <table>
        <thead>
            <tr>
                <th>Payment Method</th>
                <th class="right">Amount</th>
                <th class="right">Collected</th>
                <th class="right">Over/Under</th>
            </tr>
        </thead>
        <tbody>
            {{#each otherPaymentsData}}
            <tr class="payment-method">
                <td>{{this.label}}</td>
                <td class="right">₹{{this.amount}}</td>
                <td class="right cash-positive">₹{{this.collected}}</td>
                <td class="right {{#if this.overUnderPositive}}positive{{else}}negative{{/if}}">
                    ₹{{this.overUnder}}
                </td>
            </tr>
            {{/each}}
            <tr class="total-row summary-total">
                <td class="right">Total</td>
                <td class="right">₹{{otherPaymentsTotals.amount}}</td>
                <td class="right">₹{{otherPaymentsTotals.collected}}</td>
                <td class="right {{#if otherPaymentsTotals.overUnderPositive}}positive{{else}}negative{{/if}}">
                    ₹{{otherPaymentsTotals.overUnder}}
                </td>
            </tr>
        </tbody>
    </table>

    <!-- PAGE BREAK ADDED -->
    <div class="page-break"></div>

    <h3 class="section-title">Totals</h3>
    <table>
        <tr class="{{#if totalsData.allOverUnderPositive}}positive{{else}}negative{{/if}}">
            <th>Total Over/Under</th>
            <td class="right">₹{{totalsData.allOverUnder}}</td>
        </tr>
        <tr class="summary-total">
            <th>Total Payment Amount</th>
            <td class="right">₹{{totalsData.allPayments}}</td>
        </tr>
    </table>

    <h3 class="section-title">Sales Summary</h3>
    <table>
        <thead>
            <tr>
                <th>Source</th>
                <th>Method</th>
                {{!-- <th class="right">Returns</th> --}}
                <th class="right">Sales</th>
            </tr>
        </thead>
        <tbody>
            {{#each salesData.paymentRows}}
            <tr>
                <td>{{this.source}}</td>
                <td>{{this.method}}</td>
                {{!-- <td class="right negative">₹{{this.returns}}</td> --}}
                <td class="right positive">₹{{this.sales}}</td>
            </tr>
            {{/each}}
            <tr class="total-row summary-total">
                <td colspan="2" class="right">Grand Total</td>
                <td class="right">₹{{salesData.facilitySalesAmount}}</td>
            </tr>
        </tbody>
    </table>

    <h3 class="section-title">Invoice Summary</h3>
    <table>
        <thead>
            <tr>
                <th>Item</th>
                <th class="right">Qty</th>
                <th class="right">Unit Price</th>
                <th class="right">Discount</th>
                <th class="right">Amount Before GST</th>
                <th class="right">GST</th>
                <th class="right">Total</th>
            </tr>
        </thead>
        <tbody>
            {{#each salesData.invoiceRows}}
            <tr>
                <td>{{this.item}}</td>
                <td class="right">{{this.qty}}</td>
                <td class="right">₹{{this.unitPrice}}</td>
                <td class="right negative">₹{{this.discount}}</td>
                <td class="right">₹{{this.amountBeforeGst}}</td>
                <td class="right neutral">₹{{this.gst}}</td>
                <td class="right positive">₹{{this.total}}</td>
            </tr>
            {{/each}}
            <tr class="total-row summary-total">
                <td colspan="6" class="right">Grand Total</td>
                <td class="right">₹{{salesData.invoiceTotalAmount}}</td>
            </tr>
        </tbody>
    </table>

    <div class="footer">
        @Report generated by {{generatedBy}} on {{day}}, {{currentDate}} at {{currentTime}}
    </div>

</body>

</html>