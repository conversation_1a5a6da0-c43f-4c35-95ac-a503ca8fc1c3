import { Controller, Get, Post, Body, Patch, Param, Delete, HttpCode, HttpStatus, Put, BadRequestException } from "@nestjs/common";
import { AnnouncementService } from "../services/announcement.service";
import { CreateAnnouncementDto } from "../dtos/create-announcement.dto";
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from "@nestjs/swagger";
import { Types } from "mongoose";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { GetAnnouncementDto } from "../dtos/get-announcement.dto";
import { UpdateAnnouncementStatusDto } from "../dtos/update-announcement-status.dto";
import { UpdateAnnouncementDto } from "src/announcement/dtos/update-announcement.dto";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { PolicyAbilityProtected } from "src/policy/decorators/policy.decorator";
import { ENUM_PERMISSION_TYPE } from "src/policy/enums/policy.permissions.enum";

@ApiTags("Announcements")
@ApiBearerAuth()
@Controller("announcements")
export class AnnouncementController {
    constructor(private readonly announcementService: AnnouncementService) { }

    @Post("/")
    @PolicyAbilityProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Create a new announcement" })
    @ApiResponse({ status: 201, description: "Announcement created successfully" })
    async create(@Body() createAnnouncementDto: CreateAnnouncementDto, @GetUser() user: any) {
        const data = await this.announcementService.create(createAnnouncementDto, user);
        return {
            message: "Announcement created successfully",
            data,
        };
    }

    @Put("/")
    @HttpCode(HttpStatus.ACCEPTED)
    @PolicyAbilityProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Update announcement" })
    @ApiResponse({ status: HttpStatus.ACCEPTED, description: "Announcement updated successfully" })
    async update(@Body() updateAnnouncementDto: UpdateAnnouncementDto) {
        const data = await this.announcementService.updateAnnouncement(updateAnnouncementDto);
        return {
            message: "Announcement updated successfully",
            data,
        };
    }

    @Patch("/:id/status")
    @HttpCode(HttpStatus.ACCEPTED)
    @PolicyAbilityProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Update announcement status" })
    @ApiResponse({ status: HttpStatus.ACCEPTED, description: "Announcement status updated successfully" })
    async updateStatus(@Param("id") id: string, @Body() updateAnnouncementStatusDto: UpdateAnnouncementStatusDto, @GetUser() user: any) {
        if (typeof updateAnnouncementStatusDto.isActive !== "boolean") {
            throw new BadRequestException("Status must be a active/inactive value");
        }
        const data = await this.announcementService.updateStatusAnnouncement(new Types.ObjectId(id), updateAnnouncementStatusDto, user);
        return {
            message: "Announcement status updated successfully",
            data,
        };
    }

    @Delete(":id/delete")
    @HttpCode(HttpStatus.NO_CONTENT)
    @ApiOperation({ summary: "Delete announcement" })
    @ApiResponse({ status: HttpStatus.NO_CONTENT, description: "Announcement deleted successfully" })
    @PolicyAbilityProtected()
    @AuthJwtAccessProtected()
    async remove(@Param("id") id: string) {
        return await this.announcementService.delete(new Types.ObjectId(id));
    }

    @Post("/get")
    @HttpCode(HttpStatus.OK)
    @ApiOperation({ summary: "Get all announcements" })
    @ApiResponse({ status: HttpStatus.OK, description: "Returns all announcements" })
    async getAnnouncements(@Body() getAnnouncementDto: GetAnnouncementDto) {
        const { announcements, total } = await this.announcementService.getAnnouncements(getAnnouncementDto);
        const totalPages = Math.ceil(total / getAnnouncementDto.pageSize);
        return {
            message: "Announcements fetched successfully",
            total,
            page: getAnnouncementDto.page,
            pageSize: getAnnouncementDto.pageSize,
            totalPages,
            data: announcements,
        };
    }

    @Get(":id/get")
    @ApiOperation({ summary: "Get announcement by id" })
    @ApiResponse({ status: 200, description: "Returns the announcement" })
    async findOne(@Param("id") id: string) {
        const data = await this.announcementService.getAnnouncementById(new Types.ObjectId(id));
        return {
            message: "Announcement fetched successfully",
            data,
        };
    }
}
