import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>otEmpty, IsBoolean, <PERSON><PERSON><PERSON>al, IsMongoId } from 'class-validator';
import { ApiProperty, OmitType } from '@nestjs/swagger';
import { PaginationDTO } from "src/users/dto/pagination.dto";
import { Transform, Type } from 'class-transformer';
import { Types } from 'mongoose';

export class GetAnnouncementDto extends OmitType(PaginationDTO, ['search', 'fetchWithBundled']) {

    @ApiProperty({ description: 'The organization id of the announcement', example: '66b54c326b54c326b54c326b' })
    @IsNotEmpty()
    @Transform(({ value }) => new Types.ObjectId(value))
    organizationId: string;
    

    @ApiProperty({ description: 'The active status of the announcement', example: true })
    @IsBoolean()
    @IsOptional()
    @Type(() => Boolean)
    active?: boolean;

}
