import { IsString, IsNotEmpty, IsDate, IsBoolean, IsO<PERSON>al, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>th, Min<PERSON>ate, IsUrl } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class CreateAnnouncementDto {
    @ApiProperty({ description: 'The title of the announcement', example: 'Announcement Title' })
    @IsString()
    @IsNotEmpty()
    @MaxLength(250)
    @MinLength(1)
    title: string;

    @ApiProperty({ description: 'The subtitle of the announcement', example: 'Subtitle', required: false })
    @IsString()
    @IsOptional()
    @MaxLength(250)
    @MinLength(1)
    subtitle?: string;

    @ApiProperty({ description: 'The description of the announcement', example: 'Announcement Description' })
    @IsString()
    @IsNotEmpty()
    @MaxLength(10000)
    @MinLength(1)
    description: string;

    @ApiProperty({ description: 'The description of the announcement', example: 'Announcement Description' })
    @IsString()
    @IsNotEmpty()
    @IsUrl()
    imageUrl: string;

}
