import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AnnouncementController } from './controllers/announcement.controller';
import { AnnouncementService } from './services/announcement.service';
import { Announcement, AnnouncementSchema } from './schemas/announcement.schema';
import { StaffProfileDetails, StaffSchema } from 'src/staff/schemas/staff.schema';
import { Clients, ClientSchema } from 'src/users/schemas/clients.schema';
import { AuthModule } from 'src/auth/auth.module';
import { DATABASE_PRIMARY_CONNECTION_NAME } from 'src/common/database/constants/database.constant';

@Module({
    imports: [
        AuthModule,
        MongooseModule.forFeature([
            { name: Announcement.name, schema: AnnouncementSchema },
            { name: StaffProfileDetails.name, schema: StaffSchema },
            { name: Clients.name, schema: ClientSchema }
        ], DATABASE_PRIMARY_CONNECTION_NAME)
    ],
    controllers: [AnnouncementController],
    providers: [AnnouncementService],
    exports: [AnnouncementService]
})
export class AnnouncementModule {}
