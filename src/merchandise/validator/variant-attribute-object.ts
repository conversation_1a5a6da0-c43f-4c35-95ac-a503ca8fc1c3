import { ValidatorConstraint, ValidatorConstraintInterface, ValidationArguments } from "class-validator";
import { Types } from "mongoose";
import { VariantAttributes } from "../constant/attributeList.constant";
import { Attributes } from "src/utils/enums/attribute.enum";

@ValidatorConstraint({ async: false })
export class IsVariantAttributeObject implements ValidatorConstraintInterface {
  errors: string[] = [];

  validate(attributes: any, args: ValidationArguments) {
    const TextBoxVariant = [Attributes.Packaging];
    for (const key in attributes) {
      if (!VariantAttributes.includes(key as any)) {
        this.errors.push(`Invalid attribute: ${key}`);
        return false;
      }

      const value = attributes[key];
      if (Array.isArray(value)) {
        if (!value.every(item => Types.ObjectId.isValid(item.replace(/\s/g, "")))) {
          this.errors.push(`Invalid value for attribute ${key}: ${value}`);
          return false;
        }
      } else {
        if (!Types.ObjectId.isValid(value.replace(/\s/g, "")) && !TextBoxVariant.includes(key as any)) {
          this.errors.push(`Invalid value for attribute ${key}: ${value}`);
          return false;
        }
      }
    }

    return true;
  }

  defaultMessage(args: ValidationArguments) {
    return this.errors.join(", ");
  }
}
