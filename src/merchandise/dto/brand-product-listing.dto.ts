import { ApiProperty } from '@nestjs/swagger';
import { IsArray, ArrayNotEmpty, IsMongoId } from 'class-validator';
import { PaginationDTO } from "src/users/dto/pagination.dto";

export class BrandProductListingDto  {
    @ApiProperty({
        description: 'List of brand IDs to fetch products for',
        type: [String],
        example: ['64d12f7a9a6f3e4b9c2f8e12', '64d12f7a9a6f3e4b9c2f8e13'],
    })
    @IsArray()
    @ArrayNotEmpty()
    @IsMongoId({ each: true })
    brandId: string[];
}
