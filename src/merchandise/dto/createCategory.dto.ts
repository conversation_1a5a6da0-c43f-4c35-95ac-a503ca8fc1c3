import { Transform, Type } from "class-transformer";
import { IsBoolean, IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsInt,IsString } from "class-validator";
import { CategoryLevel, CategoryStatus } from "src/merchandise/schema/category.schema";
import { ApiProperty } from '@nestjs/swagger';

export class CreateCategoryDto {
  @ApiProperty({
    description: 'The ID of the Parent Category',
    type: String,
    required: false,
    example: '64f5e8d3b6374d25f0e6f8b2',
  })
  @IsOptional()
  @IsMongoId({ message: "Required Parent category" })
  parentId: string;

  @ApiProperty({
    description: 'The Name of the Category',
    type: String,
    required: true,
    example: 'lakme',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

 

  @ApiProperty({
    description: 'The Level of the Category',
    type: String,
    required: true,
    example: CategoryLevel.SECOND,
  })
  @IsEnum([CategoryLevel.SECOND,CategoryLevel.FIRST], { message: "Invalid category level" })
  @IsString()
  level: string;

  @ApiProperty({
    description: 'The Status of the Category',
    type: String,
    required: true,
    example: CategoryStatus.ACTIVE,
  })

  @IsOptional()
  @IsEnum(CategoryStatus)
  status: string;
   
}
