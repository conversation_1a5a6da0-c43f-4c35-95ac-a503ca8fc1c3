import { Type } from "class-transformer";
import { IsBoolean, IsMongoId, IsOptional, IsString, ValidateNested } from "class-validator";
import { PaginationDto } from "src/utils/dto/pagination.dto";
import { ApiProperty } from '@nestjs/swagger';

export class FilterProductDto extends PaginationDto {
    @ApiProperty({
        description: 'The Product to search',
        type: String,
        required: false,
        example: 'lenovo ',
    })
    @IsOptional()
    @IsString()
    search: string;
    
    @ApiProperty({
        description: 'The status of the Product ',
        type: Boolean,
        required: false,
        example: 'brand ',
    })
    @IsOptional()
    @IsBoolean()
    status: boolean;
}
