import { Type } from "class-transformer";
import {
    ArrayMaxSize,
    ArrayMinSize,
    IsArray,
    IsBoolean,
    IsEnum,
    IsMongoId,
    IsNotEmpty,
    IsNotEmptyObject,
    IsNumber,
    IsObject,
    IsOptional,
    IsString,
    IsUrl,
    Max,
    Min,
    Validate,
    ValidateIf,
    ValidateNested,
} from "class-validator";
import { IsAttributeObject } from "src/merchandise/validator/attribute-object";
import { IsVariantAttributeObject } from "src/merchandise/validator/variant-attribute-object";
import { ApiProperty } from "@nestjs/swagger";
import { ProductType } from "src/merchandise/schema/product.schema";
import { ProductAttribute } from "src/merchandise/schema/product-attribute.schema";
import { Attributes } from "src/utils/enums/attribute.enum";
import { VariantAttributes } from "src/merchandise/constant/attributeList.constant";
export class ProductVariantDto {
    @IsString()
    @IsNotEmpty()
    title: string;

    @IsString()
    @IsNotEmpty()
    sku: string;

    @IsString()
    @IsOptional()
    hsnCode: string;

    @IsString()
    @IsOptional()
    itemCode: string;

    @IsObject()
    @IsNotEmptyObject()
    // @Validate(IsVariantAttributeObject)
    attributes: Record<string, any>;

    @IsOptional()
    @IsBoolean()
    status: boolean;
}

export class CreateProductDto {
    @ApiProperty({ description: "Product name", example: "Laptop", required: true })
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiProperty({ description: "Item code", example: "ITM12345" })
    @IsString()
    @IsNotEmpty()
    itemCode: string;

    @ApiProperty({ description: "SKU (Stock Keeping Unit)", example: "SKU12345" })
    @IsString()
    @IsNotEmpty()
    sku: string;

    @ApiProperty({ description: "Unique slug", example: "laptop-dell-inspiron" })
    @IsString()
    @IsNotEmpty()
    slug: string;

    @ApiProperty({ description: "First Category ID", example: "60d5f5c5b4d3f5a4d4e4f4e4" })
    @IsMongoId()
    @IsNotEmpty()
    firstCategoryId: string;

    @ApiProperty({ description: "Second Category ID", example: "60d5f5c5b4d3f5a4d4e4f4e5" })
    @IsMongoId()
    @IsOptional()
    secondCategoryId: string;

    @ApiProperty({ description: "HSN Code", example: "84713010", required: true })
    @IsString()
    hsn: string;

    @ApiProperty({ description: "Manufacturing Code", example: "MFG12345", required: false })
    @IsString()
    @IsOptional()
    mfgCode?: string;

    @ApiProperty({ description: "GST Percentage", example: 18 ,minimum: 0,required: false })
    @IsNumber()
    @IsOptional()
    gst: number;

    @ApiProperty({ description: "Introduction", example: "High-performance laptop" })
    @IsString()
    @IsOptional()
    introduction?: string;

    @ApiProperty({ description: "Description", example: "This laptop comes with a high-speed processor..." })
    @IsString()
    @IsOptional()
    description?: string;

    @ApiProperty({ description: "Product Type", enum: ProductType, example: ProductType.SIMPLE })
    @IsEnum(ProductType)
    type: ProductType;




    @ApiProperty({ description: "Product attributes", type: [ProductAttribute], required: false })
    @Validate(IsAttributeObject)
    @IsObject()
    @IsNotEmptyObject()
    attributes: Record<Attributes, string | string[]>;


    @ValidateIf(req => req.type === "variable")
    @IsArray()
    variantAttributesList: [];

    @ValidateIf(req => req.type === "variable")
    @IsArray()
    @ArrayMinSize(1)
    @ValidateNested({ each: true })
    @Type(() => ProductVariantDto)
    variants: ProductVariantDto[];

    // @ApiProperty({ description: "Variant IDs", type: [String], required: false })
    // @IsArray()
    // @IsMongoId({ each: true })
    // @IsOptional()
    // variantIds?: string[];

    // @ApiProperty({ description: "Variant Attributes List", type: [String], required: false })
    // @IsArray()
    // @IsOptional()
    // variantAttributesList?: Attributes[];

    @ApiProperty({ description: "Status", example: true })
    @IsBoolean()
    @IsOptional()
    status?: boolean;

}
