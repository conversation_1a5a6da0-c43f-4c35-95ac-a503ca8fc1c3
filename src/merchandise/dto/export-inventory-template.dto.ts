// dto/export-inventory-template.dto.ts
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ExportInventoryTemplateDto {
  @ApiProperty({ enum: ['csv', 'xlsx'] })
  @IsEnum(['csv', 'xlsx'])
  fileType: 'csv' | 'xlsx';

  @ApiProperty({ enum: ['stream', 'json'], required: false, default: 'stream' })
  @IsOptional()
  @IsEnum(['stream', 'json'])
  responseType?: 'stream' | 'json';

  @ApiProperty({ required: false, description: 'If provided, prefill quantity/price/expiry from this store’s inventory' })
  @IsOptional()
  @IsString()
  storeId?: string;
}
