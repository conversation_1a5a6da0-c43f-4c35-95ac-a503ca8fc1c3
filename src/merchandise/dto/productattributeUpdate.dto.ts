import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString,IsBoolean } from 'class-validator';

export class UpdateAttributeDto {
  @ApiProperty({ example: 'Size in Litres', description: 'New attribute name' })
  @IsString()
  @IsNotEmpty()
  name: string;
}

export class UpdateAttributeStatusDto {
    @ApiProperty({ example: true, description: 'Status of the attribute (true = active, false = inactive)' })
    @IsBoolean()
    isActive: boolean;
  }