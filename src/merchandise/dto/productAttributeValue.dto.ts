import {
    IsString,
    IsEnum,
    IsBoolean,
    IsArray,
    ValidateNested,
    ArrayMinSize,
    Matches,
  } from 'class-validator';
  import { Type } from 'class-transformer';
  import { ApiProperty } from '@nestjs/swagger';
  import { AttributeType } from "../schema/attribute.schema";
  import { InputFieldType } from 'src/utils/enums/input-field-type.enum';
  
  
  
  export class ProductCreateAttributeDto {
    @ApiProperty({
      description: 'Display name of the attribute',
      type: String,
      example: 'Size In GM',
    })
    @IsString()
    name: string;
  
    @ApiProperty({
      description: 'Type of the attribute',
      enum: AttributeType,
      example: AttributeType.Variant,
    })
    @IsEnum(AttributeType)
    type: AttributeType;
  
    @ApiProperty({
      description: 'Type of input field for this attribute',
      enum: InputFieldType,
      example: InputFieldType.Dropdown,
    })
    @IsEnum(InputFieldType)
    inputType: InputFieldType;
  
    @ApiProperty({
      description: 'Allow selecting multiple values',
      type: Boolean,
      example: false,
    })
    @IsBoolean()
    multiple: boolean;
  
   
  }
  