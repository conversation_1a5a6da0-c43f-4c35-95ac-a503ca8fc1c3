import { IsEnum, IsHexColor, IsNotEmpty, IsOptional, IsString, ValidateIf } from "class-validator";
import { Attributes } from "src/utils/enums/attribute.enum";
import { ApiProperty } from '@nestjs/swagger';

export class CreateAttributeValueDto {
    @ApiProperty({
        description: 'The attribute key or slug',
        type: String,
        required: true,
        example: "shade",
    })
    @IsString()
    @IsNotEmpty()
    attribute: string;

    @ApiProperty({
        description: 'The value of the attribute',
        type: String,
        required: true,
        example: "Red",
    })
    @IsNotEmpty()
    value: string;

    @ApiProperty({
        description: 'Hex code for the color if attribute is "shade"',
        type: String,
        required: false,
        example: '#ffffff',
    })
    @ValidateIf(o => o.attribute === Attributes.Shade)
    @IsHexColor()
    @IsOptional()
    hexCode: string;
}
