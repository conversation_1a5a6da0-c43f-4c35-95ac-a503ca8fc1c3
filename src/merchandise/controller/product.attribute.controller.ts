import { BadRequestException, Body, Controller, Post, UseGuards, Get, Param, NotFoundException, Patch, Delete } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { ProductCreateAttributeDto } from "../dto/productAttributeValue.dto";
import { ProductAttributeService } from "../services/productAttribute.service";
import { UpdateAttributeDto, UpdateAttributeStatusDto } from "../dto/productattributeUpdate.dto";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";
@ApiTags("Product-attribute")
@ApiBearerAuth()
@Controller("product-attribute")
export class ProductAttributeController {
    constructor(private productAttributeService: ProductAttributeService) { }
    @Post("/create")
    @ApiOperation({ summary: "Create Attribute of the variant Product" })
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async createProductAttribute(
        @Body() createAttributeValueDto: ProductCreateAttributeDto, @GetUser() user: any,) {
        const data = await this.productAttributeService.createProductAttribute(createAttributeValueDto, user);
        return {
            message: "Product Attribute created successfully",
            data: data
        }
    }
    @Get('/')
    @ApiOperation({ summary: 'Get all attributes as a mapped object' })
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async getAttributeListAsMap(@GetUser() user: any,): Promise<any> {
        return {
            message: "Attribute List",
            data: await this.productAttributeService.getAttributeListMap(user)
        };
    }
    @Delete(':slug')
    @ApiOperation({ summary: 'Delete Attribute' })
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async deleteAttribute(@Param('slug') slug: string, @GetUser() user: any): Promise<any> {
        return this.productAttributeService.deleteAttribute(slug, user);
    }

    @Get(':slug')
    @ApiOperation({ summary: 'Get attribute detail by slug' })
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async getAttributeDetail(
        @Param('slug') slug: string,
        @GetUser() user: any
    ) {
        const data = await this.productAttributeService.getAttributeBySlug(slug, user);
        return {
            message: 'Attribute detail',
            data,
        };
    }
    @Patch(':slug')
    @ApiOperation({ summary: 'Update attribute name' })
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async updateAttribute(@Param('slug') slug: string, @Body() updateAttributeDto: UpdateAttributeDto, @GetUser() user: any) {
        return this.productAttributeService.updateAttribute(slug, updateAttributeDto, user);
    }
    @Patch('status/:slug')
    @ApiOperation({ summary: 'Update status of an attribute (active/inactive)' })
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async updateAttributeStatus(
        @Param('slug') slug: string,
        @Body() updateAttributeStatus: UpdateAttributeStatusDto,
        @GetUser() user: any
    ) {
        return this.productAttributeService.updateAttributeStatus(slug, updateAttributeStatus, user);
    }
    @Get('/active-attribute/list')
    @ApiOperation({ summary: 'Get all active attributes (default + org specific)' })
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async getActiveAttributes(@GetUser() user: any,): Promise<any> {
        const data = await this.productAttributeService.getActiveAttributes(user);
        return {
            message: 'Active attributes',
            data
        };
    }


}