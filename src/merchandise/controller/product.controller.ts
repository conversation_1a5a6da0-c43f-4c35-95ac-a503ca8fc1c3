import { BadRequestException, Body, Controller, Post, UseGuards, Get, Param, NotFoundException, StreamableFile, Patch, Delete, UseInterceptors, UploadedFile, ParseFilePipe, FileTypeValidator, HttpCode, Headers } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { CreateProductDto } from "../dto/createProduct.dto";
import { ProductService } from "../services/product.service";
import { UpdateProductStatusDto } from "../dto/productupdateStatus.dto";
import { FileInterceptor } from "@nestjs/platform-express";
import { CsvParserService } from "src/utils/services/csv-parser.service";
import { ExportProductsDto } from "../dto/productExport.dto";
import { ExportService } from "src/utils/services/export.service";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";
import { BrandProductListingDto } from "../dto/brand-product-listing.dto";

@ApiTags("product")
@ApiBearerAuth()
@Controller("product")
export class ProductController {
  constructor(
    private productService: ProductService,
    private readonly csvParserService: CsvParserService,
    private readonly exportService: ExportService,
  ) { }
  @Post("/create")
  @ApiOperation({ summary: "Create a new Product" })
  @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
  @AuthJwtAccessProtected()
  async createProduct(@Body() createProductDto: CreateProductDto, @GetUser() user: any,) {
    const productCreateResponse = await this.productService.createProduct(createProductDto, user)
    //   return {
    //     message: "Product Created",
    //     data: await this.productService.createProduct(createProductDto),
    //   };
  }
  @Post("/list")
  @ApiOperation({ summary: "list of  Product" })
  @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
  @AuthJwtAccessProtected()
  async ProductListing(@Body() body: any, @GetUser() user: any,) {
    const productListing = await this.productService.getProductsList(body, user);
    return {
      message: "Product List",
      data: productListing
    }
  }
  @Get("/:id")
  @ApiOperation({ summary: "Get the product Detail" })
  @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
  @AuthJwtAccessProtected()
  async getProductDetails(@Param("id") id: string) {
    const productDetail = await this.productService.getProductDetails(id);
    return {
      message: "Product Details",
      data: productDetail,
    };
  }


  @Patch("/:id")
  @ApiOperation({ summary: "Update product Detail" })
  @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
  @AuthJwtAccessProtected()
  async updateProduct(@Param("id") id: string, @Body() createProductDto: CreateProductDto, @GetUser() user: any) {
    const updateProductResponse = await this.productService.updateProduct(id, createProductDto, user)
    return {
      message: "Product Updated",
      data: updateProductResponse,
    };
  }
  @Patch("/status/:id")
  @ApiOperation({ summary: "Update product Status " })
  @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
  @AuthJwtAccessProtected()
  async updateProductStatus(@Param("id") id: string, @Body() UpdateProductStatusDto: any): Promise<any> {
    return {
      message: "Product status updated",
      data: await this.productService.updateProductStatus(id, UpdateProductStatusDto),
    };
  }
  @Delete("/:id")
  @ApiOperation({ summary: "Update Inventory Details" })
  @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
  @AuthJwtAccessProtected()
  async deleteProductDetails(@Param("id") id: string): Promise<{ message: string; data: any }> {
    const deleteReposne = await this.productService.deleteProduct(id);
    return {
      message: "Product Delete Successfully",
      data: deleteReposne
    }
  }
  @Post("/bulkUpload")
  @UseInterceptors(FileInterceptor("productFile"))
  @ApiOperation({ summary: "Update Inventory Details" })
  @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
  @AuthJwtAccessProtected()
  async bulkProductUpload(
    @Body() body: any,
    @UploadedFile(
      new ParseFilePipe({
        validators: [new FileTypeValidator({ fileType: ".(csv)" })],
      }),
    )
    productFile: Express.Multer.File,
    @GetUser() user: any,
  ): Promise<{ message: string; uploadReport?: any }> {

    const fileBuffer = productFile.buffer;
    const jsonData = await this.csvParserService.parseCsv(fileBuffer);
    if (!jsonData?.length) {
      throw new BadRequestException("Unable to upload products / Empty file uploaded");
    }
    const uploadReport = await this.productService.bulkUploadProducts(jsonData, user, false);
    return {
      message: "Bulk product upload is complete",
      uploadReport: uploadReport,
    };
  }
  @Post('/export')
  @HttpCode(200)
  @ApiOperation({ summary: 'Export Products' })
  @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
  @AuthJwtAccessProtected()
  async exportProductList(
    @Headers('X-Timezone') userTimezone: string,
    @GetUser() user: any,
    @Body() body: ExportProductsDto,
  ): Promise<any> {
    userTimezone =
      userTimezone || Intl.DateTimeFormat().resolvedOptions().timeZone;
    const products = await this.productService.getProductsForExport(
      user,
      body.productType,
      body.startDate,
      body.endDate,
    );

    if (!products.length) {
      return {
        message: 'No products found to export',
        data: [],
      };
    }

    const buffer = await this.exportService.generateExportFile(products, body.fileType);

    if (body.responseType === 'stream') {
      const contentType = this.exportService.getMimeType(body.fileType);
      const extension = body.fileType;
      const fileName = `products-${Date.now()}.${extension}`;
      return new StreamableFile(new Uint8Array(buffer), {
        type: contentType,
        disposition: `attachment; filename=${fileName}`,
      });
    }

    return {
      message: 'Products fetched successfully',
      data: products,
    };
  }

  @Post('/bulkUpdate')
  @UseInterceptors(FileInterceptor('productFile'))
  @HttpCode(200)
  @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
  @AuthJwtAccessProtected()
  @ApiOperation({ summary: 'Bulk update products by CSV' })
  async bulkUpdateProducts(
    @UploadedFile(
      new ParseFilePipe({
        validators: [new FileTypeValidator({ fileType: '.(csv)' })],
      }),
    )
    productFile: Express.Multer.File,
    @GetUser() user: any,
  ): Promise<any> {
    const fileBuffer = productFile.buffer;

    const jsonData = await this.csvParserService.parseCsv(fileBuffer);

    if (!jsonData?.length) {
      throw new BadRequestException('Unable to upload products / Empty file uploaded');
    }

    // ✅ Call the unified service in update mode
    const result = await this.productService.bulkUploadProducts(jsonData, user, true);

    return {
      message: 'Bulk update completed',
      uploadReport: result,
    };
  }
  @Post("/brand-product-list")
  @ApiOperation({ summary: "List of Product" })
  @PolicyAbilityRoleProtected(
    ENUM_ROLE_TYPE.ORGANIZATION,
    ENUM_ROLE_TYPE.WEB_MASTER,
    ENUM_ROLE_TYPE.FRONT_DESK_ADMIN,
    ENUM_ROLE_TYPE.TRAINER
  )
  @AuthJwtAccessProtected()
  async BrandProductListing(
    @Body() body: BrandProductListingDto,
    @GetUser() user: any,
  ) {
    const productListing = await this.productService.bulkProductListing(body, user);
    return {
      message: "Product List",
      data: productListing
    };
  }

}
