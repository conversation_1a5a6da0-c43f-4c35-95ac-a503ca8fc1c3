import { BadRequestException, Body, Controller, Post, UseGuards, Get, Param, NotFoundException, Patch, Delete } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { AttributeService } from "../services/attribute.service";
import { AttributeList } from "../constant/attributeList.constant";
import { CreateAttributeValueDto } from "../dto/createAttributeValue.dto";
import { FilterAttributeValueDto } from "../dto/filterAttributeValue.tro";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";

@ApiTags("attribute")
@ApiBearerAuth()
@Controller("attribute")
export class AttributeController {
    constructor(private attributeService: AttributeService) { }
    @Get("/")
    @ApiOperation({ summary: "list of Attribute" })
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async index(): Promise<{ message: string; data: typeof AttributeList }> {
        return {
            message: "Attribute List",
            data: AttributeList,
        };
    }

    @Post("/create")
    @ApiOperation({ summary: "Create a new Attribute" })
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async createAttributeValue(
        @Body() createAttributeValueDto: CreateAttributeValueDto, @GetUser() user: any,) {
        try {
            const payload: any = {
                ...createAttributeValueDto,
                createdBy: user._id,
            };
            const result = await this.attributeService.createAttributeValue(payload, user);
            return {
                message: "Attribute Value created successfully",
                data: result
            };
        } catch (error) {
            console.log(error)
            throw new BadRequestException(error.message || "Attribute creation failed");
        }
    }


    @Post("/")
    @ApiOperation({ summary: "list of Attribute" })
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async attributeList(@Body() filterAttributeValueDto: FilterAttributeValueDto, @GetUser() user: any) {
        try {
            const attributeList = await this.attributeService.getAttributeValues(filterAttributeValueDto, user);
            return {
                message: "Attribute List Fetch Successfully",
                data: attributeList
            }
        } catch (error: any) {
            throw new BadRequestException(error.message || "Could not Fetch Attribute List")
        }
    }

    @Get("/:id")
    @ApiOperation({ summary: "Detail of Attribute" })
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async attributeValueDetails(@Param("id") id: string): Promise<{ message: string; data: any }> {
        return {
            message: "Attribute Value Details",
            data: await this.attributeService.getAttributeValueDetails(id),
        };
    }
    @Patch("/:id")
    @ApiOperation({ summary: "Update Detail of Attribute" })
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async updateAttributeValue(
        @Param("id") id: string,
        @Body() createAttributeValueDto: CreateAttributeValueDto): Promise<{ message: string; data: any }> {
        return {
            message: "Attribute Value updated successfully",
            data: await this.attributeService.updateAttributeValue(id, createAttributeValueDto),
        };
    }
    @Get("/brand/brand-list")
    @ApiOperation({ summary: "List of all the Brand" })
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async getBrandData(@GetUser() user: any) {
        const response = await this.attributeService.getBrandDetails(user)
        return {
            message: "Brand List Fetch successfully",
            data: response
        }
    }
    @Delete("/:id")
    @ApiOperation({ summary: "Delete the attribute" })
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async deleteAttributeValue(@Param("id") id: string): Promise<{ message: string }> {
        await this.attributeService.deleteAttributeValue(id);
        return {
            message: "Attribute Value deleted successfully",
        };
    }
}