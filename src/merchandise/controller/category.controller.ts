import { BadRequestException, Body, Controller, Post, UseGuards, Get, Param, NotFoundException, Patch, Delete } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { CategoryService } from "../services/category.service";
import { CreateCategoryDto } from "../dto/createCategory.dto";
import { FilterCategoryDto } from "../dto/filterCategory.dto";
import { PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";

@ApiTags("category")
@ApiBearerAuth()
@Controller("category")
export class CategoryController {
  constructor(private categoryService: CategoryService) { }
  @Post("/")
  @ApiOperation({ summary: "Create a new Category" })
  @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
  @AuthJwtAccessProtected()
  async index(@Body() filterCategoryDto: FilterCategoryDto, @GetUser() user: any,): Promise<{ message: String; data: any }> {
    const categories = await this.categoryService.categoryListing(filterCategoryDto, user);
    return {
      message: "Category List",
      data: categories,
    };
  }

  /**************************************************************************************************************************** */
  @Post("/create")
  @ApiOperation({ summary: "Create a new Category" })
  @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
  @AuthJwtAccessProtected()
  async createCategory(
    @Body() createCategoryDto: CreateCategoryDto,
    @GetUser() user: any,
  ) {
    try {
      const payload: any = {
        ...createCategoryDto,
        createdBy: user._id,
      };
      const result = await this.categoryService.createCategory(payload, user);
      return result;
    } catch (error) {
      console.log(error)
      throw new BadRequestException(error.message || "Category creation failed");
    }
  }
  /*****************************************Delete Api Controller********************************************************** */

  @Delete("/:id")
  @ApiOperation({ summary: "Delete a  Category" })
  @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
  @AuthJwtAccessProtected()
  async deleteCategory(@Param("id") id: string) {
    const response = await this.categoryService.deleteCategory(id);
    return {
      message: "Category Deleted Successfully",
      data: response
    }

  }
  /**********************************************Get Category by Id**************************************************** */

  @Get("/:id")
  @ApiOperation({ summary: "Delete a  Category" })
  @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
  @AuthJwtAccessProtected()
  async getCategoryDetails(@Param("id") id: string) {
    const response = await this.categoryService.getCategoryDetails(id);
    return {
      message: "Category Details",
      data: response,
    };
  }

  /*************************************************Update Category****************************************************** */
  @Patch("/:id")
  @ApiOperation({ summary: "Update a  Category" })
  @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
  @AuthJwtAccessProtected()
  async updateCategory(
    @Param("id") id: string, @Body() createCategoryDto: CreateCategoryDto): Promise<{ message: String; data: any }> {
    return {
      message: "Category Updated",
      data: await this.categoryService.updateCategory(id, createCategoryDto),
    };
  }

  @Post("/subCategorylist")
  @ApiOperation({ summary: "List of sub Category" })
  @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
  @AuthJwtAccessProtected()
  async secondaryCategoryList(@Body() filterCategoryDto: FilterCategoryDto, @GetUser() user: any,): Promise<{ message: String; data: any }> {
    const categories = await this.categoryService.SecondarycategoryListing(filterCategoryDto, user);
    return {
      message: "Category List",
      data: categories,
    };
  }


}

