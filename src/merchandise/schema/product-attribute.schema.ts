import { SchemaTypes } from "mongoose";
import { Attributes } from "src/utils/enums/attribute.enum";
import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Types } from "mongoose";

@Schema({ _id: false })
export class ProductAttribute {
  @Prop({ required: true, type: String,  })
  key: Attributes;

  @Prop({ required: true, type: SchemaTypes.Mixed })
  value: string | Types.ObjectId | string[] | Types.ObjectId[];
  
}

export const ProductAttributeSchema = SchemaFactory.createForClass(ProductAttribute);

export type ProductAttributeType = {
  key: Attributes;
  value: Types.ObjectId | Types.ObjectId[];
};
