import { <PERSON>p, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, SchemaTypes } from "mongoose";
import { Attributes } from "src/utils/enums/attribute.enum";
import { VariantAttributes } from "../constant/attributeList.constant";
import { ProductVariant } from "./product-variant.schema";
import { ProductAttribute, ProductAttributeSchema } from "./product-attribute.schema";

export type ProductDocument = HydratedDocument<Product>;

export enum ProductType {
    SIMPLE = "simple",
    VARIABLE = "variable",
}

@Schema({ timestamps: true })
export class Product {

    @Prop({ required: true, type: String, index: "text" })
    name: String;

    @Prop({ required: true, type: String, unique: false, })
    itemCode: String;

    @Prop({ required: true, type: String, unique: false })
    sku: String;
    @Prop({ required: true, type: String, unique: false })
    slug: String;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Category", index: true })
    firstCategoryId: String;

    @Prop({ type: SchemaTypes.ObjectId, required: false, ref: "Category", index: true })
    secondCategoryId: String;

    @Prop({ type: String, required: true })
    hsn: String;

    @Prop({ type: String })
    mfgCode: String;

    @Prop({ type: Number, required: true })
    gst: Number;

    @Prop({ type: String })
    introduction: String;

    @Prop({ type: String })
    description: String;

    @Prop({ required: true, type: String, enum: ProductType })
    type: ProductType;

    @Prop({ type: [ProductAttributeSchema] })
    attributes: ProductAttribute[];

    @Prop({ type: [{ type: SchemaTypes.ObjectId, ref: "Variant" }], index: true })
    variantIds: String[];

    @Prop({ type: [{ type: VariantAttributes }] })
    variantAttributesList: Attributes[];

    @Prop({ type: Boolean, default: false })
    status: boolean;
    @Prop({ type: SchemaTypes.ObjectId, ref: "User" })
    createdBy: string;
    @Prop({ type: SchemaTypes.ObjectId, ref: "User" })
    organizationId: string;

    populatedVariants: ProductVariant[];
}

export const ProductSchema = SchemaFactory.createForClass(Product);

ProductSchema.post('save', function (error: any, doc: any, next: any) {
  if (error.name === 'MongoServerError' && error.code === 11000) {
    next(new Error('Item code must be unique. This itemCode already exists.'));
  } else {
    next(error);
  }
});
