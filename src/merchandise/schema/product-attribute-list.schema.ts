// attribute.schema.ts

import { <PERSON>p, <PERSON>hem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, SchemaTypes } from "mongoose";
import { AttributeType } from "../schema/attribute.schema";
import { InputFieldType } from 'src/utils/enums/input-field-type.enum';

export type ProductVariantAttributeDocument = HydratedDocument<ProductVariantAttribute>;

@Schema({ timestamps: true })
export class ProductVariantAttribute {
  @Prop({ required: true, })
  slug: string;

  @Prop({ required: true })
  name: string;

  @Prop({ enum: AttributeType, required: true })
  type: AttributeType;

  @Prop({ enum: InputFieldType, required: true })
  inputType: InputFieldType;

  @Prop({ default: false })
  multiple: boolean;
  @Prop({ default: true })
  isActive: boolean;

  @Prop({ type: SchemaTypes.ObjectId, ref: "User", required: true })
  organizationId: string;

}

export const ProductVariantAttributeSchema = SchemaFactory.createForClass(ProductVariantAttribute);
