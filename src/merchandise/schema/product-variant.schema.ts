import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, SchemaTypes } from "mongoose";
import { ProductAttribute, ProductAttributeSchema } from "./product-attribute.schema";

export type ProductVariantDocument = HydratedDocument<ProductVariant>;


@Schema({ timestamps: true })
export class ProductVariant {
    @Prop({ required: true, type: String, index: "text" })
    title: String;

    @Prop({ required: false, type: String })
    itemCode: String;

    @Prop({ required: true, type: String, unique: false, index: "text" })
    sku: String;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Product", index: true })
    productId: String;

    @Prop({ type: String, required: false })
    hsnCode: String;

    @Prop({ type: [ProductAttributeSchema] })
    attributes: ProductAttribute[];

    @Prop({ type: Boolean, default: true })
    status: boolean;

    @Prop({ type: SchemaTypes.ObjectId, ref: "User" })
    createdBy: string;
    @Prop({ type: SchemaTypes.ObjectId, ref: "User" })
    organizationId: string;
}

export const ProductVariantSchema = SchemaFactory.createForClass(ProductVariant);
ProductVariantSchema.post('save', function (error: any, doc: any, next: any) {
  if (error.name === 'MongoServerError' && error.code === 11000) {
    next(new Error('SKU code must be unique for Variant.'));
  } else {
    next(error);
  }
});