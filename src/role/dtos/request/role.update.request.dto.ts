import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { faker } from '@faker-js/faker';
import {
    IsArray,
    IsEnum,
    IsNotEmpty,
    IsOptional,
    IsString,
    ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { RolePermissionDto } from 'src/role/dtos/role.permission.dto';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';

export class RoleUpdateRequestDto {
    @ApiProperty({
        description: 'Description of role',
        example: faker.lorem.sentence(),
        required: false,
    })
    @IsString()
    @IsOptional()
    description?: string;

    @ApiProperty({
        description: 'Representative for role type',
        example: ENUM_ROLE_TYPE.ORGANIZATION,
        required: true,
        enum: ENUM_ROLE_TYPE,
    })
    @IsEnum(ENUM_ROLE_TYPE)
    @IsNotEmpty()
    type: ENUM_ROLE_TYPE;

    @ApiProperty({
        required: true,
        description: 'Permission list of policy',
        isArray: true,
        type: [String],
        example: ['603d2149e773f2a3990b47f5', '603d2149e773f2a3990b47f6'],
    })
    @IsNotEmpty()
    @ValidateNested()
    @IsArray()
    @Transform(({ value }) => value.map((v) => new Types.ObjectId(v)))
    policies?: Types.ObjectId[];
}
