import { ApiHideProperty, OmitType } from '@nestjs/swagger';
import { Exclude, Expose, Transform } from 'class-transformer';
import { RoleListResponseDto } from 'src/role/dtos/response/role.list.response.dto';

export class RoleShortResponseDto extends OmitType(RoleListResponseDto, [
    'permissions',
    'isActive',
    'createdAt',
    'updatedAt',
] as const) {

    @Expose()
    @Transform(({ obj }) => obj._id.toString())
    _id: string;

    @ApiHideProperty()
    @Exclude()
    permissions: number;

    @ApiHideProperty()
    @Exclude()
    isActive: boolean;

    @ApiHideProperty()
    @Exclude()
    createdAt: Date;

    @ApiHideProperty()
    @Exclude()
    updatedAt: Date;
}
