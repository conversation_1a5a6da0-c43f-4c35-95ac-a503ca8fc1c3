import { Body, Controller, Get, Headers, Param, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import {
    PaginationQuery,
    PaginationQueryFilterInEnum,
} from 'src/common/pagination/decorators/pagination.decorator';
import { PaginationListDto } from 'src/common/pagination/dtos/pagination.list.dto';
import { PaginationService } from 'src/common/pagination/services/pagination.service';
import { ENUM_ROLE_TYPE, ENUM_STAFF_ROLE_TYPE } from 'src/role/enums/role.enum';
import { ResponsePaging } from 'src/common/response/decorators/response.decorator';
import { IResponsePaging } from 'src/common/response/interfaces/response.interface';
import {
    ROLE_DEFAULT_AVAILABLE_SEARCH,
    ROLE_DEFAULT_POLICY_ROLE_TYPE,
} from 'src/role/constants/role.list.constant';
import { RoleShortResponseDto } from 'src/role/dtos/response/role.short.response.dto';
import { RoleDocument } from 'src/role/repository/entities/role.entity';
import { RoleService } from 'src/role/services/role.service';
import { RoleSystemListDoc, RoleSystemUserListDoc } from 'src/role/docs/role.system.controller';
import { UserService } from 'src/users/services/user.service';
import { RoleParsePipe } from '../pipes/role.parse.pipe';
import { MongoIdPipeTransform } from 'src/common/database/pipes/mongo-id.pipe';
import { RoleSearchDto } from '../dtos/role.search.dto';
import { PolicyAbilityProtected, PolicySuperAdminProtect } from 'src/policy/decorators/policy.decorator';
import { AuthJwtAccessProtected } from 'src/auth/decorators/auth.jwt.decorator';
import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';

@ApiTags("modules.role")
@Controller({
    version: "1",
    path: "/role",
})
export class RoleController {
    constructor(private readonly paginationService: PaginationService, private readonly roleService: RoleService, private readonly userService: UserService) { }

    @RoleSystemListDoc()
    @ResponsePaging("role.list")
    @AuthJwtAccessProtected()
    @Get("/list")
    async list(
        @PaginationQuery({ availableSearch: ROLE_DEFAULT_AVAILABLE_SEARCH })
        { _search, _limit, _offset, _order, page }: PaginationListDto,
    ): // @PaginationQueryFilterInEnum(
        //     'type',
        //     ROLE_DEFAULT_POLICY_ROLE_TYPE,
        //     ENUM_ROLE_TYPE
        // )
        // type: Record<string, any>
        Promise<IResponsePaging<RoleShortResponseDto>> {
        const find: Record<string, any> = {
            ..._search,
            type: { $in: Object.values(ENUM_STAFF_ROLE_TYPE) },
        };

        const roles: RoleDocument[] = await this.roleService.findAll(find, {
            paging: {
                limit: _limit,
                offset: _offset,
            },
            order: _order,
        });

        const total: number = await this.roleService.getTotal(find);
        const totalPage: number = this.paginationService.totalPage(total, _limit);
        // const mapRoles: RoleShortResponseDto[] =
        //     this.roleService.mapShort(roles);

        return {
            _pagination: {
                total, totalPage,
                page: page,
                pageSize: _limit,
            },
            data: roles as unknown as RoleShortResponseDto[],
        };
    }

    @RoleSystemUserListDoc()
    @ResponsePaging("role.users")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.ROLE_READ)
    @AuthJwtAccessProtected()
    @Post("/:role/users")
    async roleUserList(
        @Body() { search, limit, page }: RoleSearchDto,
        @Param("role", MongoIdPipeTransform, RoleParsePipe) role: RoleDocument,
        @Headers("x-organization") organizationId: string,
    ) {
        const _limit = limit || 10;
        const _page = page || 1;
        const _offset = this.paginationService.offset(_page, _limit);

        const { users, total } = await this.userService.findUsersByRole(
            role._id,
            {
                name: search,
                email: search,
                mobile: search,
                organizationId,
            },
            {
                paging: {
                    limit: _limit,
                    offset: _offset,
                },
                order: { name: 1 },
            },
        );

        const totalPage: number = this.paginationService.totalPage(total, _limit);

        return {
            _pagination: { total, totalPage },
            data: users,
        };
    }
}
