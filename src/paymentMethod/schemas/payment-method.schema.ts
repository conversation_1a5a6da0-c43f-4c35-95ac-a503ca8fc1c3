import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import { SchemaTypes, Document, Types } from "mongoose";

@Schema({ timestamps: true })
export class PaymentMethod extends Document {
    @Prop({ type: String, required: true })
    name: string;
    @Prop({ type: String, required: true })
    shortId: string;
    @Prop({ required: false, trim: true })
    imageUrl: string;
    @Prop({ type: Boolean, default: true })
    isActive: boolean;
    @Prop({ type: Boolean, default: false })
    isDeleted: boolean;
    @Prop({ type: Boolean, default: false })
    isDefault: boolean;
    @Prop({ type: SchemaTypes.ObjectId, ref: "User" })
    createdBy: string;
}

export const PaymentMethodSchema = SchemaFactory.createForClass(PaymentMethod);
