import { BadRequestException, Body, Controller, Post, UseGuards, Get, Param, NotFoundException, Patch, Delete } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { AuthGuard } from "@nestjs/passport";
import { RolesGuard } from "src/auth/roles.guard";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { Roles } from "src/auth/decorators/roles.decorator";
import { PaymentMethodService } from "../service/payment-method.service";
import { AddedPaymentMethodListDto, AddPaymentMethodDto, CreatePaymentMethodDto, PaymentMethodListDto, UpdatePaymentMethodStatusDto } from "../dto/payment-method.dto";

@ApiTags("PaymentMethods")
@ApiBearerAuth()
@Controller("payment-method")
export class PaymentMethodController {
    constructor(private readonly paymentMethodService: PaymentMethodService) { }

    @Post("/create")
    @ApiOperation({ summary: "Create a new payment method" })
    @UseGuards(AuthGuard(), RolesGuard) // Ensure only authenticated and authorized users can access
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN)
    async createPaymentMethod(
        @Body() createPaymentMethodDto: CreatePaymentMethodDto,
        @GetUser() user: any, // Extract the authenticated user
    ) {
        try {
            const payload: any = {
                ...createPaymentMethodDto,
                createdBy: user._id,
            };
            const result = await this.paymentMethodService.createPaymentMethod(payload, user);
            return result;
        } catch (error) {
            console.log(error);
            throw new BadRequestException(error.message || "Payment method creation failed");
        }
    }

    @Post("/add")
    @ApiOperation({ summary: "Add a new payment method" })
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER)
    async addPaymentMethod(
        @Body() addPaymentMethodDto: AddPaymentMethodDto,
        @GetUser() user: any, // Extract the authenticated user
    ) {
        try {
            const result = await this.paymentMethodService.addPaymentMethod(addPaymentMethodDto, user);
            return result;
        } catch (error) {
            console.log(error);
            throw new BadRequestException(error.message || "Add new payment method failed");
        }
    }

    /* get list of the payment method according to the user */
    @Post("/list")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN, ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER)
    @ApiOperation({ summary: "Get all the payment method of the User" })
    async paymentMethodList(@GetUser() user, @Body() paymentMethodListDto: PaymentMethodListDto): Promise<any> {
        const paymentMethodListResult = await this.paymentMethodService.paymentMethodList(paymentMethodListDto, user);
        return paymentMethodListResult;
    }

    @Post("/addedPaymentMethodList")
    @ApiOperation({ summary: "Get all the payment method of the User" })
    async addedPaymentMethodList(@Body() addedPaymentMethodList: AddedPaymentMethodListDto): Promise<any> {
        const paymentMethodListResult = await this.paymentMethodService.addedPaymentMethodList(addedPaymentMethodList);
        return paymentMethodListResult;
    }

    @Get(":paymentMethodId")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN)
    @ApiOperation({ summary: "Get Payment Method  by ID" })
    async getPaymentMethodById(@Param("paymentMethodId") paymentMethodId: string, @GetUser() user) {
        const paymentMethod = await this.paymentMethodService.getPaymentMethodById(paymentMethodId, user);
        if (!paymentMethod) throw new NotFoundException("Payment Method not found");

        return paymentMethod;
    }

    @Patch("/update/:paymentMethodId")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN)
    @ApiOperation({ summary: "Payment Method Update" })
    async paymentMethodUpdate(
        @Param("paymentMethodId") paymentMethodId: string,
        @GetUser() user,
        @Body() updatePaymentMethodDto: CreatePaymentMethodDto,
    ): Promise<{ message: String; data: any }> {
        let output = await this.paymentMethodService.paymentMethodUpdate(updatePaymentMethodDto, paymentMethodId, user);
        return {
            message: "Payment Method update Successfully",
            data: output,
        };
    }
    @Patch("/remove")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER)
    @ApiOperation({ summary: "Remove the Payment Method" })
    async removePaymentMethod(@Body() addPaymentMethodDto: AddPaymentMethodDto): Promise<{ message: String; data: any }> {
        let output = await this.paymentMethodService.removePaymentMethod(addPaymentMethodDto);
        return output;
    }

    @Delete(":paymentMethodId")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN)
    @ApiOperation({ summary: "Delete the Payment Method" })
    async deletePaymentMethod(@Param("paymentMethodId") paymentMethodId: string) {
        return await this.paymentMethodService.deletePaymentMethod(paymentMethodId);
    }

    @Patch("/updateStatus")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN, ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER)
    @ApiOperation({ summary: "Update the Payment Method Status" })
    async updatePaymentMethodStatus(@Body() updatePaymentMethodStatusDto: UpdatePaymentMethodStatusDto, @GetUser() user) {
        return await this.paymentMethodService.updatePaymentMethodStatus(updatePaymentMethodStatusDto, user);
    }
}
