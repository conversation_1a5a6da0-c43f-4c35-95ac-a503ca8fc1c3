import { Types } from 'mongoose';
import { ENUM_PRODUCT_ITEM_TYPE } from './promotions/enums/item-type.enum';
import { DiscountType } from './utils/enums/discount.enum';
import { PaymentStatus } from './utils/enums/payment.enum';
import { DurationUnit } from './utils/enums/duration-unit.enum';
import { SessionType } from './utils/enums/session-type.enum';
import * as numberToWords from 'number-to-words';

export interface InvoiceGeneratorOptions {
  userId: string;
  organizationId: string;
  facilityId: string;
  createdBy: string;
  paymentBy?: string;
  invoiceNumber: number;
  orderId: number;
  platform: string;
  date?: Date;
  isInclusiveOfGst?: boolean;
  billingAddressId: string;
  clientDetails: any;
  clientBillingDetails: any;
  billingDetails: any;
  isForBusiness?: boolean;
}

export interface ServiceItemData {
  document: any; // Service/Pricing document
  quantity: number;
  discountType?: DiscountType;
  discountValue?: number;
  discountedBy?: string;
  discountAmount?: number;
  cartDiscountAmount?: number;
  taxRate?: number;
  taxAmount?: number;
  hsnOrSacCode?: string;
  promotionLabel?: string;
  promotionLabelKey?: string;
}

export interface ProductItemData {
  inventoryDocument: any; // Inventory document
  productDocument: any; // Product document
  quantity: number;
  variantId?: string;
  discountType?: DiscountType;
  discountValue?: number;
  discountedBy?: string;
  discountAmount?: number;
  cartDiscountAmount?: number;
  taxRate?: number;
  taxAmount?: number;
  hsnOrSacCode?: string;
  salePrice?: number;
  mrp?: number;
  finalPrice?: number;
}

export interface CustomPackageItemData {
  document: any; // Custom package document
  quantity: number;
  discountType?: DiscountType;
  discountValue?: number;
  discountedBy?: string;
  discountAmount?: number;
  cartDiscountAmount?: number;
  taxRate?: number;
  taxAmount?: number;
  hsnOrSacCode?: string;
  unitPrice?: number;
}

export interface VoucherItemData {
  document: any; // Voucher document
  quantity: number;
  discountType?: DiscountType;
  discountValue?: number;
  discountedBy?: string;
  discountAmount?: number;
  cartDiscountAmount?: number;
  taxRate?: number;
  taxAmount?: number;
  hsnOrSacCode?: string;
  promotionLabel?: string;
  promotionLabelKey?: string;
}

export interface PaymentDetailsData {
  paymentMethod: string;
  paymentMethodId?: string;
  transactionId?: string;
  amount: number;
  paymentDate: Date;
  paymentStatus: string;
  paymentGateway?: string;
  description?: string;
  denominations?: Record<number, number>;
}

export interface CartTotals {
  subTotal: number;
  itemDiscount: number;
  cartDiscount: number;
  discount: number;
  cartDiscountType?: DiscountType;
  cartDiscountAmount: number;
  returnDiscount?: number;
  voucherDiscount?: number;
  totalGstValue: number;
  totalAmountAfterGst: number;
  roundOff: number;
  grandTotal: number;
  discountedBy?: string;
}

export interface VoucherDetails {
  voucherIds: string[];
  totalVoucherAmount: number;
  usedVoucherAmount: number;
  remainingVoucherAmount: number;
  appliedOn?: Date;
  vouchers?: Array<{
    _id: string;
    name: string;
    amount: number;
  }>;
}

export class InvoiceGenerator {
  public invoice: any = {};
  public purchaseItems: any[] = [];
  public productItems: any[] = [];
  public customPackageItems: any[] = [];

  private options: InvoiceGeneratorOptions;
  private paymentDetails: PaymentDetailsData[] = [];
  private cartTotals: CartTotals;
  private voucherDetails?: VoucherDetails;
  private returnDetails?: any;
  private isSplittedPayment: boolean = false;
  private amountPaid: number = 0;

  constructor(options: InvoiceGeneratorOptions) {
    this.options = options;
    this.initializeInvoice();
  }

  private initializeInvoice(): void {
    this.invoice = {
      _id: new Types.ObjectId(),
      createdBy: this.options.createdBy,
      invoiceNumber: this.options.invoiceNumber,
      orderId: this.options.orderId,
      userId: this.options.userId,
      organizationId: this.options.organizationId,
      facilityId: this.options.facilityId,
      paymentBy: this.options.paymentBy ?? this.options.createdBy,
      invoiceDate: this.options.date || new Date(),
      platform: this.options.platform,
      isInclusiveofGst: this.options.isInclusiveOfGst || false,
      clientDetails: this.options.clientDetails,
      clientBillingDetails: this.options.clientBillingDetails,
      billingDetails: this.options.billingDetails,
      isForBusiness: this.options.isForBusiness || false,
      purchaseItems: [],
      productItem: [],
      customPackageItems: [],
    };
  }

  addServiceItem(itemData: ServiceItemData): void {
    const { document: service, quantity, discountType, discountValue, discountedBy,
      discountAmount = 0, cartDiscountAmount = 0, taxRate = 0, taxAmount = 0,
      hsnOrSacCode, promotionLabel, promotionLabelKey } = itemData;

    const startDate = this.options.date ? new Date(this.options.date) : new Date();
    const endDate = this.calculateEndDate(startDate, service.expiredInDays, service.durationUnit);

    // Calculate unit price (price after item-level discount but before cart/voucher discounts)
    const unitPrice = (service.price || 0) - (discountAmount / quantity);

    const purchaseItem = {
      packageId: service._id,
      purchaseIds: [], // Will be populated after purchase documents are created
      packageName: service.name,
      quantity,
      isBundledPricing: service.isBundledPricing || false,
      expireIn: service.expiredInDays,
      durationUnit: service.durationUnit,
      startDate,
      endDate,
      unitPrice: unitPrice.toFixed(2),
      discountType: discountType ? this.mapPromotionTypeToDiscountType(discountType) : undefined,
      discountValue: discountValue || 0,
      discountedBy,
      discountExcludeCart: (discountAmount || 0).toFixed(2),
      discountIncludeCart: (cartDiscountAmount || 0).toFixed(2),
      hsnOrSacCode: hsnOrSacCode || "",
      tax: taxRate || 0,
      gstAmount: (taxAmount || 0).toFixed(2),
      promotionLabel,
      promotionLabelKey,
    };

    this.purchaseItems.push(purchaseItem);
  }

  addVoucherItem(itemData: VoucherItemData): void {
    const { document: voucher, quantity, discountType, discountValue, discountedBy,
      discountAmount = 0, cartDiscountAmount = 0, taxRate = 0, taxAmount = 0,
      hsnOrSacCode, promotionLabel, promotionLabelKey } = itemData;

    const startDate = this.options.date ? new Date(this.options.date) : new Date();
    const endDate = this.calculateEndDate(startDate, voucher.expiredInDays, DurationUnit.DAYS);

    // Calculate unit price (price after item-level discount but before cart/voucher discounts)
    const unitPrice = (voucher.price || 0) - (discountAmount / quantity);

    const purchaseItem = {
      packageId: voucher._id,
      purchaseIds: [], // Will be populated after purchase documents are created
      packageName: voucher.name,
      quantity,
      isBundledPricing: voucher.isBundledPricing || false,
      expireIn: voucher.expiredInDays,
      durationUnit: voucher.durationUnit || DurationUnit.DAYS,
      startDate,
      endDate,
      unitPrice: unitPrice.toFixed(2),
      discountType: discountType ? this.mapPromotionTypeToDiscountType(discountType) : undefined,
      discountValue: discountValue || 0,
      discountedBy,
      discountExcludeCart: (discountAmount || 0).toFixed(2),
      discountIncludeCart: (cartDiscountAmount || 0).toFixed(2),
      hsnOrSacCode: hsnOrSacCode || "",
      tax: taxRate || 0,
      gstAmount: (taxAmount || 0).toFixed(2),
      promotionLabel,
      promotionLabelKey,
    };

    this.purchaseItems.push(purchaseItem);
  }

  addProductItem(itemData: ProductItemData): void {
    const { inventoryDocument: inventory, productDocument: product, quantity, variantId,
      discountType, discountValue, discountedBy, discountAmount = 0, cartDiscountAmount = 0,
      taxRate = 0, taxAmount = 0, hsnOrSacCode, salePrice, mrp, finalPrice } = itemData;

    const productItem = {
      inventoryId: inventory._id,
      purchaseIds: [], // Will be populated after purchase documents are created
      productId: product._id,
      productVariantId: variantId || inventory.productVariantId,
      productName: product.name,
      quantity,
      salePrice: salePrice || inventory.salePrice || 0,
      mrp: mrp || inventory.mrp || 0,
      finalPrice: finalPrice ? (finalPrice / quantity).toFixed(2) : "0.00",
      discountType: discountType ? this.mapPromotionTypeToDiscountType(discountType) : undefined,
      discountedBy,
      discountValue: discountValue || 0,
      discountExcludeCart: (discountAmount || 0).toFixed(2),
      discountIncludeCart: (cartDiscountAmount || 0).toFixed(2),
      hsnOrSacCode: hsnOrSacCode || "",
      tax: taxRate || 0,
      gstAmount: (taxAmount || 0).toFixed(2),
    };

    this.productItems.push(productItem);
  }

  addCustomPackageItem(itemData: CustomPackageItemData): void {
    const { document: customPackage, quantity, discountType, discountValue, discountedBy,
      discountAmount = 0, cartDiscountAmount = 0, taxRate = 0, taxAmount = 0,
      hsnOrSacCode, unitPrice } = itemData;

    // Calculate unit price (price after item-level discount but before cart/voucher discounts)
    const calculatedUnitPrice = (unitPrice || customPackage.unitPrice || customPackage.price || 0) - (discountAmount / quantity);

    const customPackageItem = {
      customPackageId: customPackage._id,
      purchaseIds: [], // Will be populated after purchase documents are created
      packageName: customPackage.name,
      quantity,
      unitPrice: calculatedUnitPrice.toFixed(2),
      discountType: discountType ? this.mapPromotionTypeToDiscountType(discountType) : undefined,
      discountValue: discountValue || 0,
      discountedBy,
      discountExcludeCart: (discountAmount || 0).toFixed(2),
      discountIncludeCart: (cartDiscountAmount || 0).toFixed(2),
      hsnOrSacCode: hsnOrSacCode || "",
      tax: taxRate || 0,
      gstAmount: (taxAmount || 0).toFixed(2),
    };

    this.customPackageItems.push(customPackageItem);
  }

  setPaymentDetails(paymentDetails: PaymentDetailsData[]): void {
    this.paymentDetails = paymentDetails.map(detail => ({
      paymentMethod: detail.paymentMethod,
      paymentMethodId: detail.paymentMethodId || "",
      transactionId: detail.transactionId || "",
      amount: detail.amount,
      paymentDate: detail.paymentDate,
      paymentStatus: detail.paymentStatus,
      paymentGateway: detail.paymentGateway || "",
      description: detail.description || "",
      denominations: detail.denominations || {},
    }));
  }

  setCartTotals(totals: CartTotals): void {
    this.cartTotals = totals;
  }

  setVoucherDetails(voucherDetails: VoucherDetails): void {
    this.voucherDetails = {
      ...voucherDetails,
      appliedOn: new Date(),
    };
  }

  setReturnDetails(returnDetails: any): void {
    this.returnDetails = returnDetails;
  }

  setSplitPayment(isSplitted: boolean, amountPaid: number): void {
    this.isSplittedPayment = isSplitted;
    this.amountPaid = amountPaid;
  }

  private calculateEndDate(startDate: Date, expireIn: number, durationUnit: string): Date {
    const endDate = new Date(startDate);

    switch (durationUnit) {
      case DurationUnit.DAYS:
        endDate.setDate(endDate.getDate() + expireIn);
        break;
      case DurationUnit.MONTHS:
        endDate.setMonth(endDate.getMonth() + expireIn);
        break;
      case DurationUnit.YEARS:
        endDate.setFullYear(endDate.getFullYear() + expireIn);
        break;
      default:
        throw new Error(`Invalid duration unit: ${durationUnit}`);
    }

    return endDate;
  }

  private mapPromotionTypeToDiscountType(promotionType: DiscountType): string {
    switch (promotionType) {
      case DiscountType.PERCENTAGE:
        return DiscountType.PERCENTAGE;
      case DiscountType.FLAT:
        return DiscountType.FLAT;
      default:
        return DiscountType.FLAT;
    }
  }

  private convertAmountToWords(amount: number): string {
    try {
      const amountInWords = numberToWords.toWords(amount);
      return amountInWords.charAt(0).toUpperCase() + amountInWords.slice(1) + " Rupees Only";
    } catch (error) {
      return "Amount conversion error";
    }
  }

  /**
   * Generate QR code data for purchase items
   * This should be called after purchase documents are created
   */
  generateQRCodeData(purchaseDocuments: any[]): { [purchaseId: string]: string } {
    const qrCodeData: { [purchaseId: string]: string } = {};

    purchaseDocuments.forEach(purchase => {
      // Generate QR code data similar to the original implementation
      const qrData = {
        purchaseId: purchase._id.toString(),
        userId: purchase.userId.toString(),
        packageId: purchase.packageId.toString(),
        organizationId: purchase.organizationId.toString(),
        facilityId: purchase.facilityId.toString(),
        startDate: purchase.startDate,
        endDate: purchase.endDate,
      };

      qrCodeData[purchase._id.toString()] = JSON.stringify(qrData);
    });

    return qrCodeData;
  }

  /**
   * Calculate item pricing similar to cart service
   */
  calculateItemPricing(params: {
    quantity: number;
    price: number;
    taxRate: number;
    isInclusiveOfGst: boolean;
    itemDiscount?: { type: DiscountType; value: number };
  }) {
    const { quantity, price, taxRate, isInclusiveOfGst, itemDiscount } = params;

    // Calculate total price
    const totalPrice = price * quantity;

    // Calculate item discount
    let itemDiscountAmount = 0;
    if (itemDiscount) {
      if (itemDiscount.type === DiscountType.PERCENTAGE) {
        itemDiscountAmount = (itemDiscount.value / 100) * totalPrice;
      } else if (itemDiscount.type === DiscountType.FLAT) {
        itemDiscountAmount = Math.min(itemDiscount.value, totalPrice);
      }
    }

    // Calculate tax amount (on price after discount)
    const taxableAmount = totalPrice - itemDiscountAmount;
    let taxAmount = 0;
    let finalPrice = 0;
    let discountedPrice = 0;

    if (isInclusiveOfGst) {
      // For inclusive GST, tax is already included in the price
      taxAmount = Number(((taxRate / 100) * taxableAmount));
      finalPrice = taxableAmount;
      discountedPrice = taxableAmount - taxAmount;
    } else {
      // For exclusive GST, tax is added to the price
      taxAmount = Number(((taxRate / 100) * taxableAmount));
      finalPrice = taxableAmount + taxAmount;
      discountedPrice = taxableAmount;
    }

    return {
      price,
      totalPrice,
      discountAmount: itemDiscountAmount,
      taxRate,
      taxAmount,
      discountedPrice,
      finalPrice,
    };
  }

  /**
   * Get invoice summary for response
   */
  getInvoiceSummary() {
    return {
      invoiceId: this.invoice._id?.toString(),
      invoiceNumber: this.invoice.invoiceNumber,
      orderId: this.invoice.orderId,
      grandTotal: this.cartTotals?.grandTotal || 0,
      amountPaid: this.amountPaid || this.cartTotals?.totalAmountAfterGst || 0,
      purchaseItemsCount: this.purchaseItems.length,
      productItemsCount: this.productItems.length,
      customPackageItemsCount: this.customPackageItems.length,
    };
  }

  /**
   * Generate Purchase documents for service items (packages and vouchers)
   * This method should be called after saving the invoice to get the invoiceId
   */
  generatePurchaseDocuments(invoiceId: string, clientUser: any): any[] {
    const purchaseDocuments: any[] = [];

    // Generate purchase documents for service items (packages and vouchers)
    for (let i = 0; i < this.purchaseItems.length; i++) {
      const item = this.purchaseItems[i];

      for (let j = 0; j < item.quantity; j++) {
        const purchaseDoc = {
          _id: new Types.ObjectId(),
          invoiceId: new Types.ObjectId(invoiceId),
          packageId: new Types.ObjectId(item.packageId),
          userId: new Types.ObjectId(this.options.userId),
          sponsorUser: this.options.userId === this.options.paymentBy ? null : new Types.ObjectId(this.options.paymentBy),
          organizationId: new Types.ObjectId(this.options.organizationId),
          facilityId: new Types.ObjectId(this.options.facilityId),
          purchasedBy: new Types.ObjectId(clientUser._id),
          membershipId: null, // Will be set based on package details
          purchaseDate: new Date(),
          paymentStatus: this.paymentDetails[0].paymentStatus,
          isExpired: false,
          sessionType: null, // Will be set based on package details
          totalSessions: null, // Will be set based on package details
          sessionConsumed: 0,
          startDate: item.startDate,
          endDate: item.endDate,
          isActive: true,
          // Additional fields will be set based on package type and session configuration
        };

        purchaseDocuments.push(purchaseDoc);

        // Add the purchase ID to the invoice item for reference
        if (!this.purchaseItems[i].purchaseIds) {
          this.purchaseItems[i].purchaseIds = [];
        }
        this.purchaseItems[i].purchaseIds.push(purchaseDoc._id.toString());
      }
    }

    return purchaseDocuments;
  }

  /**
   * Update purchase items with session details from package documents
   */
  updatePurchaseItemsWithPackageDetails(packageDetails: any[]): void {
    for (let i = 0; i < this.purchaseItems.length; i++) {
      const item = this.purchaseItems[i];
      const packageDetail = packageDetails.find(pkg => pkg._id.toString() === item.packageId.toString());

      if (packageDetail && packageDetail.services) {
        // Update purchase item with package-specific details
        item.sessionType = packageDetail.services.sessionType;
        item.membershipId = packageDetail.membershipId || null;

        // Set session-related fields based on session type
        if ([SessionType.UNLIMITED, SessionType.DAY_PASS].includes(packageDetail.services.sessionType)) {
          item.sessionPerDay = packageDetail.services.sessionPerDay;
        }

        if (packageDetail.services.sessionType === SessionType.DAY_PASS) {
          item.dayPassLimit = packageDetail.services.dayPassLimit;
        }

        item.totalSessions = packageDetail.services.sessionType === SessionType.SINGLE ? 1 : packageDetail.services.sessionCount;
      }
    }
  }

  execute(): void {
    if (!this.cartTotals) {
      throw new Error('Cart totals must be set before executing invoice generation');
    }

    if (!this.paymentDetails || this.paymentDetails.length === 0) {
      throw new Error('Payment details must be set before executing invoice generation');
    }

    // Update invoice with all collected data
    this.invoice = {
      ...this.invoice,
      discountedBy: this.cartTotals.discountedBy || null,
      purchaseItems: this.purchaseItems,
      productItem: this.productItems,
      customPackageItems: this.customPackageItems,
      subTotal: this.cartTotals.subTotal.toFixed(2),
      itemDiscount: this.cartTotals.itemDiscount.toFixed(2),
      cartDiscount: this.cartTotals.cartDiscount,
      discount: this.cartTotals.discount.toFixed(2),
      cartDiscountType: this.cartTotals.cartDiscountType || (this.cartTotals.cartDiscount > 0 ? DiscountType.FLAT : undefined),
      cartDiscountAmount: this.cartTotals.cartDiscountAmount.toString(),
      returnDiscount: this.cartTotals.returnDiscount || 0,
      voucherDiscount: this.cartTotals.voucherDiscount || 0,
      voucherDetails: this.voucherDetails,
      returnDetails: this.returnDetails,
      totalGstValue: this.cartTotals.totalGstValue.toFixed(2),
      totalAmountAfterGst: this.cartTotals.totalAmountAfterGst.toFixed(2),
      roundOff: this.cartTotals.roundOff.toFixed(2),
      grandTotal: this.cartTotals.grandTotal.toFixed(2),
      amountInWords: this.convertAmountToWords(Math.floor(this.cartTotals.grandTotal)),
      paymentStatus: this.paymentDetails[0].paymentStatus,
      paymentDetails: this.paymentDetails,
      isSplittedPayment: this.isSplittedPayment,
      amountPaid: this.amountPaid || this.cartTotals.totalAmountAfterGst,
    };
  }
}
