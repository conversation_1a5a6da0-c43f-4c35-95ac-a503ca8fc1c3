import { Types } from 'mongoose';
import { ENUM_PRODUCT_ITEM_TYPE } from './promotions/enums/item-type.enum';
import { DiscountType } from './utils/enums/discount.enum';
import { PaymentStatus } from './utils/enums/payment.enum';
import { DurationUnit } from './utils/enums/duration-unit.enum';
import * as numberToWords from 'number-to-words';

export interface InvoiceGeneratorOptions {
  userId: string;
  organizationId: string;
  facilityId: string;
  createdBy: string;
  paymentBy?: string;
  invoiceNumber: number;
  orderId: number;
  platform: string;
  date?: Date;
  isInclusiveOfGst?: boolean;
  billingAddressId: string;
  clientDetails: any;
  clientBillingDetails: any;
  billingDetails: any;
  isForBusiness?: boolean;
}

export interface ServiceItemData {
  document: any; // Service/Pricing document
  quantity: number;
  discountType?: DiscountType;
  discountValue?: number;
  discountedBy?: string;
  discountAmount?: number;
  cartDiscountAmount?: number;
  taxRate?: number;
  taxAmount?: number;
  hsnOrSacCode?: string;
  promotionLabel?: string;
  promotionLabelKey?: string;
}

export interface ProductItemData {
  inventoryDocument: any; // Inventory document
  productDocument: any; // Product document
  quantity: number;
  variantId?: string;
  discountType?: DiscountType;
  discountValue?: number;
  discountedBy?: string;
  discountAmount?: number;
  cartDiscountAmount?: number;
  taxRate?: number;
  taxAmount?: number;
  hsnOrSacCode?: string;
  salePrice?: number;
  mrp?: number;
  finalPrice?: number;
}

export interface CustomPackageItemData {
  document: any; // Custom package document
  quantity: number;
  discountType?: DiscountType;
  discountValue?: number;
  discountedBy?: string;
  discountAmount?: number;
  cartDiscountAmount?: number;
  taxRate?: number;
  taxAmount?: number;
  hsnOrSacCode?: string;
  unitPrice?: number;
}

export interface VoucherItemData {
  document: any; // Voucher document
  quantity: number;
  discountType?: DiscountType;
  discountValue?: number;
  discountedBy?: string;
  discountAmount?: number;
  cartDiscountAmount?: number;
  taxRate?: number;
  taxAmount?: number;
  hsnOrSacCode?: string;
  promotionLabel?: string;
  promotionLabelKey?: string;
}

export interface PaymentDetailsData {
  paymentMethod: string;
  paymentMethodId?: string;
  transactionId?: string;
  amount: number;
  paymentDate: Date;
  paymentStatus: string;
  paymentGateway?: string;
  description?: string;
  denominations?: Record<number, number>;
}

export interface CartTotals {
  subTotal: number;
  itemDiscount: number;
  cartDiscount: number;
  discount: number;
  cartDiscountType?: DiscountType;
  cartDiscountAmount: number;
  returnDiscount?: number;
  voucherDiscount?: number;
  totalGstValue: number;
  totalAmountAfterGst: number;
  roundOff: number;
  grandTotal: number;
  discountedBy?: string;
}

export interface VoucherDetails {
  voucherIds: string[];
  totalVoucherAmount: number;
  usedVoucherAmount: number;
  remainingVoucherAmount: number;
  vouchers?: Array<{
    _id: string;
    name: string;
    amount: number;
  }>;
}

export class InvoiceGenerator {
  public invoice: any = {};
  public purchaseItems: any[] = [];
  public productItems: any[] = [];
  public customPackageItems: any[] = [];
  
  private options: InvoiceGeneratorOptions;
  private paymentDetails: PaymentDetailsData[] = [];
  private cartTotals: CartTotals;
  private voucherDetails?: VoucherDetails;
  private returnDetails?: any;
  private isSplittedPayment: boolean = false;
  private amountPaid: number = 0;

  constructor(options: InvoiceGeneratorOptions) {
    this.options = options;
    this.initializeInvoice();
  }

  private initializeInvoice(): void {
    this.invoice = {
      _id: new Types.ObjectId(),
      createdBy: this.options.createdBy,
      invoiceNumber: this.options.invoiceNumber,
      orderId: this.options.orderId,
      userId: this.options.userId,
      organizationId: this.options.organizationId,
      facilityId: this.options.facilityId,
      paymentBy: this.options.paymentBy ?? this.options.createdBy,
      invoiceDate: this.options.date || new Date(),
      platform: this.options.platform,
      isInclusiveofGst: this.options.isInclusiveOfGst || false,
      clientDetails: this.options.clientDetails,
      clientBillingDetails: this.options.clientBillingDetails,
      billingDetails: this.options.billingDetails,
      isForBusiness: this.options.isForBusiness || false,
      purchaseItems: [],
      productItem: [],
      customPackageItems: [],
    };
  }

  addServiceItem(itemData: ServiceItemData): void {
    const { document: service, quantity, discountType, discountValue, discountedBy, 
            discountAmount = 0, cartDiscountAmount = 0, taxRate = 0, taxAmount = 0, 
            hsnOrSacCode, promotionLabel, promotionLabelKey } = itemData;

    const startDate = this.options.date ? new Date(this.options.date) : new Date();
    const endDate = this.calculateEndDate(startDate, service.expiredInDays, service.durationUnit);

    const purchaseItem = {
      packageId: service._id,
      purchaseIds: [],
      packageName: service.name,
      quantity,
      isBundledPricing: service.isBundledPricing || false,
      expireIn: service.expiredInDays,
      durationUnit: service.durationUnit,
      startDate,
      endDate,
      unitPrice: service.price || 0,
      discountType: discountType ? this.mapPromotionTypeToDiscountType(discountType) : undefined,
      discountValue: discountValue || 0,
      discountedBy,
      discountExcludeCart: discountAmount.toFixed(2),
      discountIncludeCart: cartDiscountAmount || 0,
      hsnOrSacCode,
      tax: taxRate,
      gstAmount: taxAmount?.toFixed(2),
      promotionLabel,
      promotionLabelKey,
    };

    this.purchaseItems.push(purchaseItem);
  }

  addVoucherItem(itemData: VoucherItemData): void {
    const { document: voucher, quantity, discountType, discountValue, discountedBy,
            discountAmount = 0, cartDiscountAmount = 0, taxRate = 0, taxAmount = 0,
            hsnOrSacCode, promotionLabel, promotionLabelKey } = itemData;

    const startDate = this.options.date ? new Date(this.options.date) : new Date();
    const endDate = this.calculateEndDate(startDate, voucher.expiredInDays, DurationUnit.DAYS);

    const purchaseItem = {
      packageId: voucher._id,
      purchaseIds: [],
      packageName: voucher.name,
      quantity,
      isBundledPricing: voucher.isBundledPricing || false,
      expireIn: voucher.expiredInDays,
      durationUnit: voucher.durationUnit,
      startDate,
      endDate,
      unitPrice: voucher.price || 0,
      discountType: discountType ? this.mapPromotionTypeToDiscountType(discountType) : undefined,
      discountValue: discountValue || 0,
      discountedBy,
      discountExcludeCart: discountAmount.toFixed(2),
      discountIncludeCart: cartDiscountAmount || 0,
      hsnOrSacCode,
      tax: taxRate,
      gstAmount: taxAmount?.toFixed(2),
      promotionLabel,
      promotionLabelKey,
    };

    this.purchaseItems.push(purchaseItem);
  }

  addProductItem(itemData: ProductItemData): void {
    const { inventoryDocument: inventory, productDocument: product, quantity, variantId,
            discountType, discountValue, discountedBy, discountAmount = 0, cartDiscountAmount = 0,
            taxRate = 0, taxAmount = 0, hsnOrSacCode, salePrice, mrp, finalPrice } = itemData;

    const productItem = {
      inventoryId: inventory._id,
      purchaseIds: [],
      productId: product._id,
      productVariantId: variantId || inventory.productVariantId,
      productName: product.name,
      quantity,
      salePrice: salePrice || inventory.salePrice || 0,
      mrp: mrp || inventory.mrp || 0,
      finalPrice: finalPrice ? finalPrice / quantity : 0,
      discountType: discountType ? this.mapPromotionTypeToDiscountType(discountType) : undefined,
      discountedBy,
      discountValue: discountValue || 0,
      discountExcludeCart: discountAmount || 0,
      discountIncludeCart: cartDiscountAmount || 0,
      hsnOrSacCode,
      tax: taxRate,
      gstAmount: taxAmount,
    };

    this.productItems.push(productItem);
  }

  addCustomPackageItem(itemData: CustomPackageItemData): void {
    const { document: customPackage, quantity, discountType, discountValue, discountedBy,
            discountAmount = 0, cartDiscountAmount = 0, taxRate = 0, taxAmount = 0,
            hsnOrSacCode, unitPrice } = itemData;

    const customPackageItem = {
      customPackageId: customPackage._id,
      purchaseIds: [],
      packageName: customPackage.name,
      quantity,
      unitPrice: unitPrice || customPackage.price || 0,
      discountType: discountType ? this.mapPromotionTypeToDiscountType(discountType) : undefined,
      discountValue: discountValue || 0,
      discountedBy,
      discountExcludeCart: discountAmount || 0,
      discountIncludeCart: cartDiscountAmount || 0,
      hsnOrSacCode,
      tax: taxRate,
      gstAmount: taxAmount,
    };

    this.customPackageItems.push(customPackageItem);
  }

  setPaymentDetails(paymentDetails: PaymentDetailsData[]): void {
    this.paymentDetails = paymentDetails.map(detail => ({
      paymentMethod: detail.paymentMethod,
      paymentMethodId: detail.paymentMethodId || "",
      transactionId: detail.transactionId || "",
      amount: detail.amount,
      paymentDate: detail.paymentDate,
      paymentStatus: detail.paymentStatus,
      paymentGateway: detail.paymentGateway || "",
      description: detail.description || "",
      denominations: detail.denominations || {},
    }));
  }

  setCartTotals(totals: CartTotals): void {
    this.cartTotals = totals;
  }

  setVoucherDetails(voucherDetails: VoucherDetails): void {
    this.voucherDetails = {
      ...voucherDetails,
      appliedOn: new Date(),
    };
  }

  setReturnDetails(returnDetails: any): void {
    this.returnDetails = returnDetails;
  }

  setSplitPayment(isSplitted: boolean, amountPaid: number): void {
    this.isSplittedPayment = isSplitted;
    this.amountPaid = amountPaid;
  }

  private calculateEndDate(startDate: Date, expireIn: number, durationUnit: string): Date {
    const endDate = new Date(startDate);

    switch (durationUnit) {
      case DurationUnit.DAYS:
        endDate.setDate(endDate.getDate() + expireIn);
        break;
      case DurationUnit.MONTHS:
        endDate.setMonth(endDate.getMonth() + expireIn);
        break;
      case DurationUnit.YEARS:
        endDate.setFullYear(endDate.getFullYear() + expireIn);
        break;
      default:
        throw new Error(`Invalid duration unit: ${durationUnit}`);
    }

    return endDate;
  }

  private mapPromotionTypeToDiscountType(promotionType: DiscountType): string {
    switch (promotionType) {
      case DiscountType.PERCENTAGE:
        return DiscountType.PERCENTAGE;
      case DiscountType.FLAT:
        return DiscountType.FLAT;
      default:
        return DiscountType.FLAT;
    }
  }

  private convertAmountToWords(amount: number): string {
    const amountInWords = numberToWords.toWords(amount);
    return amountInWords.charAt(0).toUpperCase() + amountInWords.slice(1) + " Rupees Only";
  }

  execute(): void {
    if (!this.cartTotals) {
      throw new Error('Cart totals must be set before executing invoice generation');
    }

    if (!this.paymentDetails || this.paymentDetails.length === 0) {
      throw new Error('Payment details must be set before executing invoice generation');
    }

    // Update invoice with all collected data
    this.invoice = {
      ...this.invoice,
      discountedBy: this.cartTotals.discountedBy || null,
      purchaseItems: this.purchaseItems,
      productItem: this.productItems,
      customPackageItems: this.customPackageItems,
      subTotal: this.cartTotals.subTotal.toFixed(2),
      itemDiscount: this.cartTotals.itemDiscount.toFixed(2),
      cartDiscount: this.cartTotals.cartDiscount,
      discount: this.cartTotals.discount.toFixed(2),
      cartDiscountType: this.cartTotals.cartDiscount > 0 ? DiscountType.FLAT : undefined,
      cartDiscountAmount: this.cartTotals.cartDiscountAmount.toString(),
      returnDiscount: this.cartTotals.returnDiscount || 0,
      voucherDiscount: this.cartTotals.voucherDiscount || 0,
      voucherDetails: this.voucherDetails,
      totalGstValue: this.cartTotals.totalGstValue.toFixed(2),
      totalAmountAfterGst: this.cartTotals.totalAmountAfterGst.toFixed(2),
      roundOff: this.cartTotals.roundOff.toFixed(2),
      grandTotal: this.cartTotals.grandTotal.toFixed(2),
      amountInWords: this.convertAmountToWords(Math.floor(this.cartTotals.grandTotal)),
      paymentStatus: this.paymentDetails[0].paymentStatus,
      paymentDetails: this.paymentDetails,
      isSplittedPayment: this.isSplittedPayment,
      amountPaid: this.amountPaid,
      returnDetails: this.returnDetails,
    };
  }
}
