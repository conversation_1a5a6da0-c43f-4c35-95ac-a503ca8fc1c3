import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsMongoId, IsNotEmpty, IsOptional, IsString, ValidateIf, ValidateNested } from 'class-validator';
import { CartItemDto } from './cart-item.dto';
import { DiscountDto } from 'src/organization/dto/create-pricing.dto';

export class CartRequestDto {

  @ApiHideProperty()
  @IsMongoId()
  @IsOptional()
  organizationId?: string;

  @ApiProperty({
    description: 'The facility ID',
    example: '60d21b4667d0d8992e610c86',
  })
  @IsMongoId()
  @IsNotEmpty()
  facilityId: string;

  @ApiProperty({
    description: 'The user ID (client)',
    example: '60d21b4667d0d8992e610c87',
  })
  @IsMongoId()
  @IsOptional()
  userId?: string;

  @ApiProperty({
    description: 'The items in the cart',
    type: [CartItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CartItemDto)
  @IsNotEmpty()
  items: CartItemDto[];

  @ApiProperty({
    description: 'The promotion code (optional)',
    example: 'SUMMER2023',
    required: false,
  })
  @IsString()
  @IsOptional()
  promotionCode?: string;

  @ApiProperty({
    description: 'Additional manual discount to apply to the cart',
    required: false,
    type: DiscountDto,
  })
  @IsOptional()
  @Type(() => DiscountDto)
  discount?: DiscountDto;

  @ApiProperty({
    description: 'Array of returned purchase IDs for exchange/refund',
    example: [
      "677f78dbc6a56b77d8ae0743",
      "67823eddab0a9e0f1c733eae"
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsMongoId({ each: true })
  returnItems?: string[];

  @ApiProperty({
    description: 'Array of returned purchase IDs for exchange/refund',
    example: [
      "677f78dbc6a56b77d8ae0743",
      "67823eddab0a9e0f1c733eae"
    ],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsMongoId({ each: true })
  returnCustomItems?: string[];


  @ApiProperty({
    description: 'Array of voucher IDs to apply to the cart',
    example: [
      "677f78dbc6a56b77d8ae0743",
      "67823eddab0a9e0f1c733eae"
    ],
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => o.userId)
  @IsArray()
  @IsMongoId({ each: true })
  vouchers?: string[];
}
