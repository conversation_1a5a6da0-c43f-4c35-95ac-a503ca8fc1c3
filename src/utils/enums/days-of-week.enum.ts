/**
 * Enum for days of the week
 */
export enum DaysOfWeek {
    SUNDAY = 'sunday',
    MONDAY = 'monday',
    TUESDAY = 'tuesday',
    WEDNESDAY = 'wednesday',
    THURSDAY = 'thursday',
    FRIDAY = 'friday',
    SATURDAY = 'saturday'
}

/**
 * Map of day names to their numeric values (0 = Sunday, 1 = Monday, etc.)
 */
export const DaysOfWeekMap = {
    [DaysOfWeek.SUNDAY]: 0,
    [DaysOfWeek.MONDAY]: 1,
    [DaysOfWeek.TUESDAY]: 2,
    [DaysOfWeek.WEDNESDAY]: 3,
    [DaysOfWeek.THURSDAY]: 4,
    [DaysOfWeek.FRIDAY]: 5,
    [DaysOfWeek.SATURDAY]: 6
};

export const DayIndexToWeekMap = {
    0: DaysOfWeek.SUNDAY,
    1: DaysOfWeek.MONDAY,
    2: DaysOfWeek.TUESDAY,
    3: DaysOfWeek.WEDNESDAY,
    4: DaysOfWeek.THURSDAY,
    5: DaysOfWeek.FRIDAY,
    6: DaysOfWeek.SATURDAY
};
