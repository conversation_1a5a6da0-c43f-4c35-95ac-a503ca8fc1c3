export enum SessionType {
    SINGLE = "single",
    MULTIPLE = "multiple",
    UNLIMITED = "unlimited",
    DAY_PASS = 'day_pass',
    ONCEADAY = "once_a_day",

}

export const SessionTypeAbbreviation = {
    [SessionType.SINGLE]: "Single",
    [SessionType.MULTIPLE]: "Sessions",
    [SessionType.UNLIMITED]: "Unlimited",
    [SessionType.DAY_PASS]: "Day Pass",
    [SessionType.ONCEADAY]: "Unlimited"
}