// decorators/pagination-query.decorator.ts
import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { PaginationDto } from '../dto/pagination.dto';

export enum ENUM_PAGINATION_ORDER_DIRECTION_TYPE {
    ASC = 'asc',
    DESC = 'desc',
}

interface PaginationQueryOptions {
    defaultOrderBy?: string;
    defaultOrderDirection?: ENUM_PAGINATION_ORDER_DIRECTION_TYPE;
    availableOrderBy?: string[];
    availableSearch?: string[];
    maxLimit?: number;
}

export interface IDatabaseQueryContainOptions {
    fullWord: boolean;
}

export const PaginationQuery = (options: PaginationQueryOptions = {}) =>
    createParamDecorator((data: unknown, ctx: ExecutionContext): PaginationDto => {
        const request = ctx.switchToHttp().getRequest();
        const query = request.query;

        const pageSize = parseInt(query.pageSize) || 10;
        const page = parseInt(query.page) || 1;
        const orderBy = query.orderBy || options.defaultOrderBy || 'createdAt';

        const orderDirectionRaw = query.orderDirection?.toLowerCase() as ENUM_PAGINATION_ORDER_DIRECTION_TYPE;
        const orderDirection = Object.values(ENUM_PAGINATION_ORDER_DIRECTION_TYPE).includes(orderDirectionRaw)
            ? orderDirectionRaw
            : options.defaultOrderDirection || ENUM_PAGINATION_ORDER_DIRECTION_TYPE.DESC;

        const validatedOrderBy = options.availableOrderBy?.includes(orderBy)
            ? orderBy
            : options.defaultOrderBy || 'createdAt';

        const limit = Math.min(pageSize, options.maxLimit || 100);
        const offset = (page - 1) * limit;

        const mongoSort: { [key: string]: 1 | -1 } = {
            [validatedOrderBy]: orderDirection === ENUM_PAGINATION_ORDER_DIRECTION_TYPE.ASC ? 1 : -1,
        };

        const searchTerm = (query.search || '').trim();
        const mongoSearch = buildSearchQuery(searchTerm, options.availableSearch || []) || {};

        return {
            _limit: limit,
            _offset: offset,
            _order: mongoSort,
            _search: mongoSearch,
        };
    })();

// helpers/search.helper.ts
export function buildSearchQuery(
    searchValue: string,
    availableSearch: string[]
): Record<string, any> | undefined {
    if (
        !searchValue ||
        searchValue === '' ||
        availableSearch.length === 0
    ) {
        return;
    }

    return {
        $or: availableSearch.map(val =>
            DatabaseHelperQueryContain(val, searchValue)
        ),
    };
}


export function DatabaseHelperQueryContain(
    field: string,
    value: string,
    options?: IDatabaseQueryContainOptions
) {
    if (options?.fullWord) {
        return {
            [field]: {
                $regex: new RegExp(`\\b${value}\\b`),
                $options: 'i',
            },
        };
    }

    return {
        [field]: {
            $regex: new RegExp(value),
            $options: 'i',
        },
    };
}
