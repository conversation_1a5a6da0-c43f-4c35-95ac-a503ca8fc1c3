import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import * as bcrypt from "bcrypt";
import { randomBytes } from "crypto";
import { State } from "../schemas/state.schema";
import { Model, Types } from "mongoose";
import { PaginationDTO } from "src/users/dto/pagination.dto";
import { Cities } from "../schemas/cities.schema";
import { StatePaginationDTO } from "src/users/dto/state-pagination.dto";
import { GetCityNameDto } from "../dto/get-city.dto";

@Injectable()
export class GeneralService {
    constructor(@InjectModel("State") private readonly stateModel: Model<State>, @InjectModel(Cities.name) private readonly cityModel: Model<Cities>) { }
    async generatePassword() {
        const length = 8; // minimum length of the password
        const specialChars = "!@#$%^&*()_+[]{}|;:,.<>?";
        const numbers = "0123456789";
        const lowerCaseLetters = "abcdefghijklmnopqrstuvwxyz";
        const upperCaseLetters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

        function getRandomChar(str) {
            return str[Math.floor(Math.random() * str.length)];
        }

        let password = "";
        password += getRandomChar(specialChars);
        password += getRandomChar(numbers);
        password += getRandomChar(lowerCaseLetters);
        password += getRandomChar(upperCaseLetters);

        const allChars = specialChars + numbers + lowerCaseLetters + upperCaseLetters;
        for (let i = password.length; i < length; i++) {
            password += getRandomChar(allChars);
        }

        password = password
            .split("")
            .sort(() => 0.5 - Math.random())
            .join("");

        return password;
    }

    async encryptPassword(password: string) {
        const salt = await bcrypt.genSalt();
        const encryptedPassword = await this.hashPassword(password, salt);
        return {
            salt: salt,
            password: encryptedPassword,
        };
    }

    async hashPassword(password: string, salt: string): Promise<string> {
        try {
            return await bcrypt.hash(password, salt);
        } catch (error) {
            throw new Error(error.message);
        }
    }
    generateRandomPassword(length = 12) {
        const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const lowercase = "abcdefghijklmnopqrstuvwxyz";
        const digits = "0123456789";
        const specialChars = "!@#$%^&*()_+[]{}|;:,.<>?";

        const allChars = uppercase + lowercase + digits + specialChars;
        let password = "";

        password += uppercase[Math.floor(Math.random() * uppercase.length)];
        password += lowercase[Math.floor(Math.random() * lowercase.length)];
        password += digits[Math.floor(Math.random() * digits.length)];
        password += specialChars[Math.floor(Math.random() * specialChars.length)];

        for (let i = 4; i < length; i++) {
            password += allChars[Math.floor(Math.random() * allChars.length)];
        }

        password = password
            .split("")
            .sort(() => Math.random() - 0.5)
            .join("");

        return password;
    }

    randomOTP() {
        return Math.floor(100000 + Math.random() * 900000);
    }

    generateGymId(): string {
        const randomId = randomBytes(4).toString("hex").slice(0, 7);
        return `G-${randomId}`;
    }

    generateStaffId(): string {
        const randomId = randomBytes(4).toString("hex").slice(0, 7);
        return `S-${randomId}`;
    }

    generateClientId(): string {
        const randomId = randomBytes(4).toString("hex").slice(0, 7);
        return `C-${randomId}`;
    }

    async getStates(paginationDTO: PaginationDTO): Promise<any> {
        let { page, pageSize, search } = paginationDTO;
        if (isNaN(page) || page < 1) {
            page = 1;
        }
        if (isNaN(pageSize) || pageSize < 1) {
            pageSize = 10;
        }
        const skip = (page - 1) * pageSize;
        const query = search ? { name: { $regex: search, $options: "i" } } : {};

        const states = await this.stateModel.find(query).sort({ name: 1 }).skip(skip).limit(pageSize).select({ _id: 1, name: 1 }).exec();

        const totalStates = await this.stateModel.countDocuments(query).exec();
        const totalPages = Math.ceil(totalStates / pageSize);
        return {
            message: states?.length > 0 ? "States list fetched successfully" : "State list not found",
            data: states,
            pagination: {
                currentPage: page,
                currentPageSize: pageSize,
                totalPages: totalPages,
                totalCount: totalStates,
            },
        };
    }

    async getCitiesByStateId(paginationDTO: StatePaginationDTO): Promise<any> {
        let { page, pageSize, search } = paginationDTO;
        if (isNaN(page) || page < 1) {
            page = 1;
        }
        if (isNaN(pageSize) || pageSize < 1) {
            pageSize = 10;
        }
        const skip = (page - 1) * pageSize;
        const query = search ? { name: { $regex: search, $options: "i" } } : {};
        if (paginationDTO?.stateId) {
            query["stateId"] = new Types.ObjectId(paginationDTO.stateId);
        }
        if (paginationDTO?.cityId) {
            query["_id"] = new Types.ObjectId(paginationDTO.cityId);
        }

        const cities = await this.cityModel.find(query).sort({ name: 1 }).skip(skip).limit(pageSize).select({ _id: 1, name: 1 }).exec();


        const totalStates = await this.cityModel.countDocuments(query).exec();
        const totalPages = Math.ceil(totalStates / pageSize);
        return {
            message: cities?.length > 0 ? "Cities list fetched successfully" : "Cities list not found",
            data: cities,
            pagination: {
                currentPage: page,
                currentPageSize: pageSize,
                totalPages: totalPages,
                totalCount: totalStates,
            },
        };
    }

    async getCityNameById(data: GetCityNameDto): Promise<any> {
        if (!data.cityIds|| data.cityIds.length === 0) {
            throw new Error("Please provide cityId")
        }
        let cityIds=data.cityIds.map(e=>new Types.ObjectId(e))
        const city = await this.cityModel.distinct("name",{_id:{$in:cityIds}}).exec();
        if (city?.length==0) {
            throw new NotFoundException('City not found');
        }
        return city;
    }
}
