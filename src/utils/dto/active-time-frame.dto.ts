import { ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsDate, IsEnum, IsNotEmpty, IsOptional, IsString, Matches, Validate, ValidateIf } from "class-validator";
import { DaysOfWeek } from "../enums/days-of-week.enum";

export class ActiveTimeFrameDto {
    @ApiProperty({
        description: "Specific date for this time frame (optional)",
        example: "2023-08-15",
        required: false,
    })
    @IsOptional()
    @IsDate()
    @ValidateIf((o) => !o.dayOfWeek)
    @Transform(({ value }) => (value ? new Date(value) : null), { toClassOnly: true })
    date?: Date;

    @ApiProperty({
        description: "Day of the week this time frame applies to (if no specific date)",
        enum: DaysOfWeek,
        example: DaysOfWeek.MONDAY,
        required: false,
    })
    @IsOptional()
    @IsEnum(DaysOfWeek)
    @ValidateIf((o) => !o.date)
    dayOfWeek?: DaysOfWeek;

    @ApiProperty({
        description: "Start time for this time frame (HH:MM format)",
        example: "11:00",
        required: true,
    })
    @IsNotEmpty()
    @IsString()
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "Start time must be in the format HH:MM" })
    startTime: string;

    @ApiProperty({
        description: "End time for this time frame (HH:MM format)",
        example: "14:00",
        required: true,
    })
    @IsNotEmpty()
    @IsString()
    @Validate((o) => o.startTime < o.endTime)
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "End time must be in the format HH:MM" })
    endTime: string;
}
