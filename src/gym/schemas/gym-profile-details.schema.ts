import { <PERSON>p, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, SchemaTypes, Types } from "mongoose";

export class GymAddress {
    @Prop({ type: SchemaTypes.ObjectId, required: true })
    stateId: Types.ObjectId;

    @Prop({ type: SchemaTypes.ObjectId, required: true })
    cityId: Types.ObjectId;

    @Prop({ type: String, required: true })
    businessAddress: string;

    @Prop({ type: String, required: false })
    addressLine2: string;

    @Prop({ type: Number, required: true })
    postalCode: number;
}

export type GymProfileDocument = HydratedDocument<GymProfileDetails>;
@Schema({ timestamps: true })
export class GymProfileDetails {
    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    userId: string;

    @Prop({ type: String, required: true, unique: true })
    gymId: string;

    @Prop({ type: GymAddress, required: true })
    address: GymAddress;

    @Prop({ type: String, required: false })
    contactName: string;

    @Prop({ type: String, required: true })
    profilePicture: string;

    @Prop({ type: [String], required: false, default: [] })
    gallery: Array<string>;

    @Prop({ type: String, required: false })
    description: string;

    @Prop({ type: [{ type: SchemaTypes.ObjectId, ref: "Amenities" }], required: false, default: [] })
    amenities: Array<string>;
}

export const GymProfileSchema = SchemaFactory.createForClass(GymProfileDetails);
