import { ApiProperty } from "@nestjs/swagger";
import { IsString, Length, IsNotEmpty, IsMongoId, <PERSON>O<PERSON><PERSON>, <PERSON>, <PERSON> } from "class-validator";
import { Types } from "mongoose";

export class GymAddressDto {
    @ApiProperty({
        description: "The ID of the state where the gym is located.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsMongoId({ message: "State ID must be a valid ObjectId" })
    @IsNotEmpty({ message: "State ID is required" })
    stateId: Types.ObjectId;

    @ApiProperty({
        description: "The ID of the city where the gym is located.",
        example: "659e8032293375c3166a99a0",
        required: true,
    })
    @IsMongoId({ message: "City ID must be a valid ObjectId" })
    @IsNotEmpty({ message: "City ID is required" })
    cityId: Types.ObjectId;

    @ApiProperty({
        description: "The specific Business Address or address of the gym.",
        example: "Damar West Opposite to SBI Bank",
        minLength: 2,
        maxLength: 1000,
        required: true,
    })
    @IsString({ message: "Business Address must be a string" })
    @Length(2, 1000, { message: "Business Address must be between 2 and 1000 characters" })
    @IsNotEmpty({ message: "Business Address is required" })
    businessAddress: string;

    @ApiProperty({
        description: "The specific Address line 2 of the gym.",
        example: "Opposite SBI",
        minLength: 2,
        maxLength: 1000,
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Address line 2 must be a string" })
    @Length(2, 1000, { message: "Address line 2 must be between 2 and 1000 characters" })
    @IsNotEmpty({ message: "Address line 2 is required" })
    addressLine2: string;

    @ApiProperty({
        description: "Please specify postal code of the city. || Must be a number",
        example: 122016,
        minLength: 6,
        maxLength: 6,
        required: true,
    })
    @Min(100000, { message: "Postal code must be a number" })
    @Max(999999, { message: "Invalid postal code" })
    postalCode: number;
}
