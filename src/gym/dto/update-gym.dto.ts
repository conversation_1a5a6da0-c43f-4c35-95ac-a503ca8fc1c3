import { ApiProperty } from "@nestjs/swagger";
import { ValidateNested, IsEmail, Length, IsString, IsOptional, IsNotEmpty, MaxLength, IsArray, ArrayMinSize, IsUrl, IsMongoId } from "class-validator";
import { Type, Transform } from "class-transformer";
import { GymAddressDto } from "./gym-address.dto";

class UrlDto {
    @IsUrl({}, { message: "url must be a valid URL" })
    @IsString({ message: "url must be a string" })
    url: string;
}

export class UpdateGymDto {
    @ApiProperty({
        description: "MongoId of Gym || Required in case of request by gym",
        example: "66b356206b37a50962c56381",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Gym id must be a valid Mongo Id" })
    userId: string;

    @ApiProperty({
        description: "The name of the gym.",
        example: "<PERSON>'s Gym",
        minLength: 2,
        maxLength: 50,
        required: true,
    })
    @IsString({ message: "Gym Name must be a string" })
    @Length(2, 50, { message: "Gym Name must be between 2 and 50 characters" })
    @IsNotEmpty({ message: "Gym Name is required" })
    gymName: string;

    @ApiProperty({
        description: "The mobile number for the gym.",
        example: "9876543212",
        // minLength: 10,
        // maxLength: 10,
        required: true,
    })
    @IsString({ message: "Mobile No. must be a string" })
    //@Length(10, 10, { message: "Mobile No. must be exactly 10 digits" })
    @IsNotEmpty({ message: "Mobile No. is required" })
    mobile: string;

    @ApiProperty({
        description: "The email address for the gym.",
        example: "<EMAIL>",
        maxLength: 255,
        required: true,
    })
    @Transform(({ value }) => value.toLowerCase())
    @IsEmail({}, { message: "Invalid Email Format" })
    @IsNotEmpty({ message: "Email is required" })
    @MaxLength(255, { message: "Email must be less than 255 characters" })
    email: string;

    @ApiProperty({
        description: "The address of the gym.",
        example: GymAddressDto,
        required: true,
    })
    @ValidateNested({ message: "Address is invalid" })
    @IsNotEmpty({ message: "Address is required" })
    @Type(() => GymAddressDto)
    address: GymAddressDto;

    @ApiProperty({
        description: "The name of the contacted person of gym.",
        example: "HKS Abhay",
        minLength: 2,
        maxLength: 50,
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Contact Name must be a string" })
    @Length(2, 50, { message: "Contact Name must be between 2 and 50 characters" })
    @IsNotEmpty({ message: "Contact Name is required" })
    contactName: string;

    @ApiProperty({
        description: "The profile picture URL for the gym.",
        example: "https://my-bucket.s3.us-west-2.amazonaws.com/uploads/images/example.jpg",
        required: true,
    })
    @IsUrl({}, { message: "Profile Picture must be a valid URL" })
    @IsString({ message: "Profile Picture must be a string" })
    profilePicture: string;

    @ApiProperty({
        description: "The gallery URLs for the gym.",
        example: ["https://my-bucket.s3.us-west-2.amazonaws.com/uploads/images/example1.jpg", "https://my-bucket.s3.us-west-2.amazonaws.com/uploads/images/example2.jpg"],
        required: false,
        type: [String], // Indicate that it's an array of strings
    })
    @IsOptional()
    @IsArray({ message: "Gym gallery must be an array of URLs" })
    @ValidateNested({ each: true })
    @Type(() => UrlDto)
    gallery: UrlDto[];

    @ApiProperty({
        description: "A description of the gym.",
        example: "A gym is a facility equipped with fitness machines and spaces for exercise, promoting health, strength, and overall well-being.",
        minLength: 2,
        maxLength: 1000,
        required: false,
    })
    @IsOptional()
    @Length(2, 1000, { message: "Description must be between 2 and 1000 characters" })
    @IsString({ message: "Description must be a string" })
    description: string;

    @ApiProperty({
        description: "Amenities of the gym. || Must be an array of mongo ids",
        example: ["wifi", "steam bath", "locker"],
        required: false,
    })
    @IsOptional()
    @IsArray({ message: "Amenities must be an array of strings" })
    @IsMongoId({
        each: true,
        message: "Invalid amenities",
    })
    @ArrayMinSize(1, { message: "Amenities array must contain at least one item" })
    amenities: Array<string>;
}
