import { Modu<PERSON> } from "@nestjs/common";
import { AttributeController as AdminAttributeController } from "./controllers/admin/attribute.controller";
import { AttributeService } from "./services/attribute.service";
import { AuthModule } from "src/auth/auth.module";
import { UtilsModule } from "src/utils/utils.module";
import { MongooseModule } from "@nestjs/mongoose";
import { Attributes, AttributeSchema } from "./schema/attribute.schema";
import { AmenitiesController } from "./controllers/admin/amenities.controller";
import { AmenitiesService } from "./services/amenities.service";
import { Amenities, AmenitiesSchema } from "./schema/amenities.schema";
import { Services, ServiceSchema } from "src/organization/schemas/services.schema";
import { DATABASE_PRIMARY_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { StaffProfileDetails, StaffSchema } from "src/staff/schemas/staff.schema";

@Module({
    imports: [
        AuthModule,
        UtilsModule,
        MongooseModule.forFeature([
            { name: Attributes.name, schema: AttributeSchema },
            { name: Services.name, schema: ServiceSchema },
            { name: Amenities.name, schema: AmenitiesSchema },
            { name: StaffProfileDetails.name, schema: StaffSchema },
            { name: Amenities.name, schema: AmenitiesSchema },
        ], DATABASE_PRIMARY_CONNECTION_NAME),
    ],
    controllers: [AdminAttributeController, AmenitiesController],
    providers: [AttributeService, AmenitiesService],
})
export class AttributesModule {}
