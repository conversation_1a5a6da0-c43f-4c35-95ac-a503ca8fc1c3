import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, SchemaTypes } from "mongoose";

export type AmenitiesDocument = HydratedDocument<Amenities>;

@Schema({ timestamps: true })
export class Amenities {
    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    organizationId: string;

    @Prop({ type: String, required: true })
    amenityType: String;

    @Prop({ type: String, required: true })
    name: String;

    @Prop({ type: Boolean, required: true, default: true })
    isActive: Boolean;

    @Prop()
    createdAt: Date;

    @Prop()
    updatedAt: Date;
}

export const AmenitiesSchema = SchemaFactory.createForClass(Amenities);
