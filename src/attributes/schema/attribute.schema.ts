import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, SchemaTypes } from "mongoose";
import { AttributeType } from "src/utils/enums/attribute-type.enum";

export type AttributesDocument = HydratedDocument<Attributes>;

@Schema({ timestamps: true })
export class Attributes {
    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    organizationId: string;

    @Prop({ enum: AttributeType, required: true, index: true })
    attributeType: AttributeType;

    @Prop({ type: String, required: true })
    name: String;

    @Prop({ type: String, required: false })
    image?: String;

    @Prop({ type: Boolean, required: true, default: true })
    isActive: Boolean;

    @Prop({ type: Boolean, required: false, default: false })
    isFeatured?: Boolean;

    @Prop()
    createdAt: Date;

    @Prop()
    updatedAt: Date;
}

export const AttributeSchema = SchemaFactory.createForClass(Attributes);
