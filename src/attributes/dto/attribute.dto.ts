import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { AttributeType } from "src/utils/enums/attribute-type.enum";

export class AttributeDto {
    @ApiProperty({
        description: "Attribute type",
        enum: AttributeType,
        example: AttributeType.CATEGORY,
    })
    @IsEnum(AttributeType, { message: "Attribute type is not valid" })
    attributeType: AttributeType;

    @ApiProperty({
        description: "Name must be a string",
        example: "Yoga",
    })
    @IsString({ message: "Name must be a string" })
    @IsNotEmpty({ message: "Name must not be empty" })
    name: string;

    @ApiProperty({
        description: "Image must be a string",
        example: "https://example.com/image",
    })
    @IsOptional()
    @IsString({ message: "Image must be a valid string" })
    @IsNotEmpty({ message: "Image cannot be empty" })
    image: string;
}
