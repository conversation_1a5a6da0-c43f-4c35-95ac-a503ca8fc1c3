import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, IsString } from "class-validator";
import { AmenityType } from "src/utils/enums/amenity-type.enum";

export class AmenitiesDto {
    @ApiProperty({
        description: "Amenity type",
        enum: AmenityType,
        example: AmenityType.PARKING_AND_TRANSPORT,
    })
    @IsEnum(AmenityType, { message: "Amenity type is not valid" })
    amenityType: AmenityType;

    @ApiProperty({
        description: "Name must be a string",
        example: "Parking",
    })
    @IsString({ message: "Name must be a string" })
    @IsNotEmpty({ message: "Name must not be empty" })
    name: string;
}
