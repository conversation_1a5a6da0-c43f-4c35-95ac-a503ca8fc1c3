import { ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsEnum, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { AttributeType } from "src/utils/enums/attribute-type.enum";

export class AttributeStatusDto {
    @ApiProperty({
        description: "isActive must be a boolean",
        example: true,
    })
    @IsBoolean({ message: "isActive must be a boolean" })
    isActive: boolean;

    @ApiProperty({
        description: "Attribute type is an enum",
        enum: AttributeType,
        example: AttributeType.CATEGORY || AttributeType.PLAN_TYPE || AttributeType.TIER,
    })
    @IsEnum(AttributeType, { message: "Attribute type is not valid" })
    attributeType: AttributeType;
}
