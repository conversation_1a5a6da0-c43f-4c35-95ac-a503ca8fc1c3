import { applyDecorators, createParamDecorator, ExecutionContext, SetMetadata, UseGuards } from '@nestjs/common';
import { SessionActivePrefix } from '../constants/session.constant';
import { SessionActiveGuard } from '../guards/session.active.guard';
import { IRequestSession } from 'src/common/request/interfaces/request.session.interface';

export function SessionActiveProtected(): MethodDecorator {
    return applyDecorators(
        SetMetadata(SessionActivePrefix, true),
        UseGuards(SessionActiveGuard)
    );
}

export const GetSessionId = createParamDecorator(
    (data: unknown, ctx: ExecutionContext): string => {
        const request = ctx.switchToHttp().getRequest();
        return request.__sessionId;
    }
);

export const IsSessionActive = createParamDecorator(
    (data: unknown, ctx: ExecutionContext): boolean => {
        const request = ctx.switchToHttp().getRequest();
        return request.__isSessionActive === true;
    }
);


export const Session = createParamDecorator(
    (data: string, ctx: ExecutionContext) => {
        const request = ctx.switchToHttp().getRequest();
        return data ? request.session?.[data] : request.session;
    },
);
