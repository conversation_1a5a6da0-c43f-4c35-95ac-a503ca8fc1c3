import {
    Inject,
    Injectable,
    NotFoundException,
    PipeTransform,
    Scope,
} from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Types } from 'mongoose';
import { IRequestApp } from 'src/common/request/interfaces/request.interface';
import { ENUM_SESSION_STATUS_CODE_ERROR } from 'src/session/enums/session.status-code.enum';
import { SessionDoc } from 'src/session/repository/entities/session.entity';
import { SessionService } from 'src/session/services/session.service';

@Injectable()
export class SessionActiveParsePipe implements PipeTransform {
    constructor(
        @Inject(REQUEST) private readonly request: IRequestApp,
        private readonly sessionService: SessionService
    ) {}

    async transform(value: string): Promise<SessionDoc> {
        const session = await this.sessionService.findOneActiveById(new Types.ObjectId(value));
        if (!session) {
            throw new NotFoundException({
                statusCode: ENUM_SESSION_STATUS_CODE_ERROR.NOT_FOUND,
                message: 'session.error.notFound',
            });
        }

        return session;
    }
}

@Injectable({ scope: Scope.REQUEST })
export class SessionActiveByUserParsePipe implements PipeTransform {
    constructor(
        @Inject(REQUEST) protected readonly request: IRequestApp,
        private readonly sessionService: SessionService
    ) {}

    async transform(value: string): Promise<SessionDoc> {
        const { user } = this.request;

        const session = await this.sessionService.findOneActiveByIdAndUser(
            new Types.ObjectId(value),
            user.user
        );
        if (!session) {
            throw new NotFoundException({
                statusCode: ENUM_SESSION_STATUS_CODE_ERROR.NOT_FOUND,
                message: 'session.error.notFound',
            });
        }

        return session;
    }
}
