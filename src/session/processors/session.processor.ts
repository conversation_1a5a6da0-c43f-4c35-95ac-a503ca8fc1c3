import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { Types } from 'mongoose';
import { MessageService } from 'src/common/message/services/message.service';
import { SessionWorkerDto } from 'src/session/dtos/session.worker.dto';
import { ENUM_SESSION_PROCESS } from 'src/session/enums/session.enum';
import { ISessionProcessor } from 'src/session/interfaces/session.processor.interface';
import { SessionService } from 'src/session/services/session.service';
import { ENUM_WORKER_QUEUES } from 'src/worker/enums/worker.enum';

@Processor(ENUM_WORKER_QUEUES.SESSION_QUEUE)
export class SessionProcessor extends WorkerHost {
    private readonly logger = new Logger(SessionProcessor.name);

    constructor(
        private readonly sessionService: SessionService,
        private readonly messageService: MessageService
    ) {
        super();
    }

    async process(job: Job<SessionWorkerDto, any, string>): Promise<void> {
        try {
            const jobName = job.name;
            switch (jobName) {
                case ENUM_SESSION_PROCESS.REVOKE:
                    await this.processDeleteLoginSession((job.data.sessionId as unknown as Types.ObjectId));
                    break;
                case ENUM_SESSION_PROCESS.REFRESH:
                    await this.processRefreshSession((job.data.sessionId as unknown as Types.ObjectId));
                    break;
                default:
                    this.logger.warn(`Unknown job name: ${jobName}`);
                    break;
            }
        } catch (error: any) {
            this.logger.error(error);
        }

        return;
    }

    async processDeleteLoginSession(session: Types.ObjectId): Promise<void> {
        const checkSession = await this.sessionService.findOneById(session);

        if (!checkSession) {
            throw new Error(
                this.messageService.setMessage('session.error.notFound')
            );
        }

        await this.sessionService.updateRevoke(checkSession);

        return;
    }

    async processRefreshSession(session: Types.ObjectId): Promise<void> {
        await this.sessionService.refreshSession(session);
        return;
    }
}
