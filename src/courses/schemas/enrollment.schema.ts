import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { ObjectId, SchemaTypes, Document } from "mongoose";
import { ScheduleStatusType } from "src/scheduling/enums/schedule-status.enum";

@Schema({ timestamps: true })
export class Enrollment extends Document {
    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Scheduling" })
    schedulingId: ObjectId;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    userId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Pricing" })
    packageId: ObjectId;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Purchase" })
    purchaseId: ObjectId;

    @Prop({ type: SchemaTypes.Boolean, default: false })
    isCheckedIn: Boolean;

    @Prop({ type: Date })
    checkedInDate: Date;

    @Prop({ type: String, required: true, enum: ScheduleStatusType, default: ScheduleStatusType.BOOKED })
    enrollmentStatus: ScheduleStatusType;
}

export const EnrollmentSchema = SchemaFactory.createForClass(Enrollment);
