import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsEnum, IsMongoId, IsNotEmpty, IsNumber, IsOptional, IsString, Matches, Min, ValidateIf } from "class-validator";
import { ScheduleStatusType } from "src/scheduling/enums/schedule-status.enum";
import { PaginationDTO } from "src/users/dto/pagination.dto";
import { ClassType } from "src/utils/enums/class-type.enum";
import { DateRange } from "src/utils/enums/date-range-enum";

export class SchedulingListDto extends PaginationDTO {
    @ApiProperty({
        description: "Facility ID for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsOptional()
    @IsMongoId({ message: "FAcility details is Invalid" })
    facilityId: string;

    @ApiProperty({
        description: "Room ids",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsOptional()
    @IsMongoId({ message: "Room details is Invalid" })
    roomId?: string;

    @ApiProperty({
        description: "The ID of the course.",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Course details is Invalid" })
    courseId: string;

    @ApiProperty({
        description: "Start sate range",
        example: new Date(),
    })
    // @IsOptional()
    @ValidateIf((req) => !!req.endDate)
    @Type(() => Date)
    startDate?: Date;

    @ApiProperty({
        description: "End sate range",
        example: new Date(),
    })
    // @IsOptional()
    @ValidateIf((req) => !!req.startDate)
    @Type(() => Date)
    endDate?: Date;

    @ApiProperty({
        description: "Filter by schedule status",
        type: String,
        example: ScheduleStatusType.BOOKED,
        required: false,
    })
    @IsEnum(ScheduleStatusType, { message: "Schedule status must be a valid status" })
    @IsOptional()
    status?: string;
}

export class CreateSchedulingDataDto {
    @ApiProperty({
        description: "Organization ID for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    organizationId: string;

    @ApiProperty({
        description: "Facility ID for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    facilityId: string;

    @ApiProperty({
        description: "Trainer ID assigned for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    trainerId: string;

    @ApiProperty({
        description: "Course ID assigned for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    courseId: string;

    @ApiProperty({
        description: "Type of the Class.",
        enum: ClassType,
        example: ClassType.COURSES,
    })
    @IsEnum(ClassType, { message: "Invalid class type" })
    classType: ClassType;

    @ApiProperty({
        description: "Service ID if any service is selected for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    subType?: string;

    @ApiProperty({
        description: "Appointment Id any package is selected for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    serviceCategory: string;

    @ApiProperty({
        description: "Duration of the Session",
        example: 60,
    })
    @IsNotEmpty()
    @Type(() => Number)
    @IsNumber()
    @Min(15)
    duration: number;

    @ApiProperty({
        description: "Room Id for the course",
        type: String,
        required: true,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    roomId: string;

    @ApiProperty({
        description: "Date range type",
        enum: DateRange,
        default: DateRange.SINGLE,
        example: DateRange.SINGLE,
    })
    @IsEnum(DateRange, { message: "Date range must be a valid enum value" })
    @IsNotEmpty({ message: "Date range is required" })
    dateRange: DateRange;

    @ApiProperty({
        description: "Date of the course",
        type: Date,
        example: new Date().setHours(0, 0, 0, 0),
    })
    @IsNotEmpty()
    // @Type(() => Date)
    date: string;

    @ApiProperty({
        description: "Start time for availability on specified days (HH:mm)",
        example: "08:00",
    })
    @IsOptional()
    @IsString({ message: "From time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "From time must be in the format HH:mm" })
    from: string;

    @ApiProperty({
        description: "End time for availability on specified days (HH:mm)",
        example: "17:00",
    })
    @IsOptional()
    @IsString({ message: "To time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "To time must be in the format HH:mm" })
    to: string;

    @ApiProperty({
        description: "Note for course",
        example: 60,
    })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({
        description: "Capacity of the class",
        example: 20,
    })
    @IsNotEmpty()
    @IsNumber()
    classCapacity: number;
}

export class UpdateSchedulingDataDto {
    @ApiProperty({
        description: "Scheduling ID for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    schedulingId: string;

    @ApiProperty({
        description: "Organization ID for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    organizationId: string;

    @ApiProperty({
        description: "Facility ID for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    facilityId: string;

    @ApiProperty({
        description: "Trainer ID assigned for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    trainerId: string;

    @ApiProperty({
        description: "Course ID assigned for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    courseId: string;

    @ApiProperty({
        description: "Type of the Class.",
        enum: ClassType,
        example: ClassType.COURSES,
    })
    @IsEnum(ClassType, { message: "Invalid class type" })
    classType: ClassType;

    @ApiProperty({
        description: "Service ID if any service is selected for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    subType?: string;

    @ApiProperty({
        description: "Appointment Id any package is selected for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    serviceCategory: string;

    @ApiProperty({
        description: "Duration of the Session",
        example: 60,
    })
    @IsNotEmpty()
    @Type(() => Number)
    @IsNumber()
    @Min(15)
    duration: number;

    @ApiProperty({
        description: "Room Id for the course",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
        required: true,
    })
    @IsNotEmpty()
    roomId: string;

    @ApiProperty({
        description: "Date range type",
        enum: DateRange,
        default: DateRange.SINGLE,
        example: DateRange.SINGLE,
    })
    @IsEnum(DateRange, { message: "Date range must be a valid enum value" })
    @IsNotEmpty({ message: "Date range is required" })
    dateRange: DateRange;

    @ApiProperty({
        description: "Date of the course",
        type: Date,
        example: new Date().setHours(0, 0, 0, 0),
    })
    @IsNotEmpty()
    // @Type(() => Date)
    date: string;

    @ApiProperty({
        description: "Start time for availability on specified days (HH:mm)",
        example: "08:00",
    })
    @IsOptional()
    @IsString({ message: "From time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "From time must be in the format HH:mm" })
    from: string;

    @ApiProperty({
        description: "End time for availability on specified days (HH:mm)",
        example: "17:00",
    })
    @IsOptional()
    @IsString({ message: "To time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "To time must be in the format HH:mm" })
    to: string;

    @ApiProperty({
        description: "Note for course",
        example: 60,
    })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({
        description: "Capacity of the class",
        example: 20,
    })
    @IsNotEmpty()
    @IsNumber()
    classCapacity: number;
}
