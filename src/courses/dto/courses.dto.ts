import { ApiProperty, OmitType, PickType } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { ArrayNotEmpty, IsArray, IsBoolean, IsMongoId, IsNotEmpty, IsOptional, IsString } from "class-validator";
import exp from "constants";
import { PaginationDTO } from "src/users/dto/pagination.dto";

export class UpdateCourseStatusDto {
    @ApiProperty({
        description: "The ID of Course",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsNotEmpty({ message: "courseId is required" })
    @IsMongoId({ message: "Required valid courseId" })
    courseId: string;

    @ApiProperty({
        description: "Updated status of the appointment type",
        example: true,
        required: false,
    })
    @IsOptional()
    @IsBoolean({ message: "Required valid isActive" })
    isActive?: boolean;

    @ApiProperty({
        description: "Active status of the course",
        example: true,
        required: false,
    })
    @IsOptional()
    @IsBoolean({ message: "Required valid isFeatured" })
    isFeatured?: boolean;

    @ApiProperty({
        description: "Course description",
        example: "This is a sample course description.",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "description must be a string" })
    description?: string;

    @ApiProperty({
        description: "Course image URL",
        example: "https://example.com/image.jpg",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "imageUrl must be a string" })
    image?: string;

    @ApiProperty({
        description: "Name of the Course",
        example: "Yoga Course",
        required: false,
    })
    @IsOptional()
    name?: string;
}

export class CustomerListDto extends PaginationDTO {
    @ApiProperty({
        description: "The ID of the course.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsNotEmpty({ message: "courseId is required" })
    @IsMongoId({ message: "Course details is Invalid" })
    courseId: string;

    @ApiProperty({
        description: "If ScheduleId exist you can get enrolled and checkedIn status",
        example: ["659d268dee4b6081dacd41fd"],
    })
    @IsOptional()
    @IsArray({ message: "schedulingIds must be an array" })
    @IsMongoId({ each: true, message: "Each schedulingId must be a valid ObjectId" })
    schedulingIds: string[];
}

export class CourseListDto extends PaginationDTO {
    @ApiProperty({
        description: "Start Date Filter",
        type: Date,
        example: new Date().setHours(0, 0, 0, 0),
    })
    @IsOptional()
    @Type(() => Date)
    startDate: Date;

    @ApiProperty({
        description: "End Date Filter",
        type: Date,
        example: new Date().setHours(0, 0, 0, 0),
    })
    @IsOptional()
    @Type(() => Date)
    endDate: Date;

    @ApiProperty({
        description: "Featured status of the course",
        example: true,
        required: false,
        default: false,
    })
    @IsOptional()
    @IsBoolean({ message: "Required valid isFeatured" })
    isFeatured?: boolean;
}

export class CourseListPublicDto extends CourseListDto {
    @ApiProperty({
        description: "The ID of the Schedule.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsNotEmpty({ message: "SchedulingId is required" })
    @IsMongoId({ message: "Schedule details is Invalid" })
    organizationId: string;
}

export class CustomerListForSchedulingDto extends PaginationDTO {
    @ApiProperty({
        description: "The ID of the Schedule.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsNotEmpty({ message: "SchedulingId is required" })
    @IsMongoId({ message: "Schedule details is Invalid" })
    schedulingId: string;
}

export class EnrollSchedulingDto {
    @ApiProperty({
        description: "Ids of selected schedules",
        example: ["66cecb432351713ae4447a6b", "66cecb432351713ae4447a6b"],
        required: true,
    })
    @IsNotEmpty({ message: "ScheduleIds is required." })
    @IsArray({ message: "scheduleIds must be an array of scheduleId." })
    @ArrayNotEmpty({ message: "At least one schedule should be selected." })
    @IsString({ each: true })
    scheduleIds: string[];

    @ApiProperty({
        description: "Ids of selected customers",
        example: ["66cecb432351713ae4447a6b", "66cecb432351713ae4447a6b"],
        required: true,
    })
    @IsNotEmpty({ message: "customerIds is required." })
    @IsArray({ message: "customerIds must be an array of scheduleId." })
    @ArrayNotEmpty({ message: "At least one customer should be selected." })
    @IsString({ each: true })
    customerIds: string[];

    @ApiProperty({
        description: "The ID of the course.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsNotEmpty({ message: "courseId is required" })
    @IsMongoId({ message: "Course details is Invalid" })
    courseId: string;
}

export class CheckedInDto {
    @ApiProperty({
        description: "The ID of the Enrollment.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsNotEmpty({ message: "Enrollment Id is required" })
    @IsMongoId({ message: "Enrollment details is Invalid" })
    enrollmentId: string;

    @ApiProperty({
        description: "Updated checked in status",
        example: true,
        required: true,
    })
    @IsNotEmpty({ message: "isCheckedIn is required." })
    @IsBoolean({ message: "Required valid isCheckedIn" })
    isCheckedIn: boolean;
}

export class CheckedInPublicDto {

    @ApiProperty({
        description: "Schedule Id",
        example: true,
        required: true,
    })
    @IsNotEmpty({ message: "Schedule is required." })
    schedulingId: string;
}
