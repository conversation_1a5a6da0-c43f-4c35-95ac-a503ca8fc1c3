import { Module } from "@nestjs/common";
import { CourseController } from "./controllers/course.controller";
import { CourseService } from "./services/course.service";
import { UtilsModule } from "src/utils/utils.module";
import { MongooseModule } from "@nestjs/mongoose";
import { User, UserSchema } from "src/users/schemas/user.schema";
import { Facility, FacilitySchema } from "src/facility/schemas/facility.schema";
import { FacilityAvailability, FacilityAvailabilitySchema } from "src/facility/schemas/facility-availability.schema";
import { StaffProfileDetails, StaffSchema } from "src/staff/schemas/staff.schema";
import { AuthModule } from "src/auth/auth.module";
import { Enrollment, EnrollmentSchema } from "./schemas/enrollment.schema";
import { Pricing, PricingSchema } from "src/organization/schemas/pricing.schema";
import { Services, ServiceSchema } from "src/organization/schemas/services.schema";
import { Purchase, PurchaseSchema } from "src/users/schemas/purchased-packages.schema";
import { Room, RoomSchema } from "src/room/schema/room.schema";
import { StaffAvailability, StaffAvailabilitySchema } from "src/staff/schemas/staff-availability";
import { MailModule } from "src/mail/mail.module";

import { PayRate, PayRateSchema } from "src/staff/schemas/pay-rate.schema";
import { Clients, ClientSchema } from "src/users/schemas/clients.schema";
import { WaitTimeModule } from "src/wait-time/wait-time.module";
import { Scheduling, SchedulingSchema } from "src/scheduling/schemas/scheduling.schema";
import { OrganizationSubSettings, OrganizationSubSettingsSchema } from "src/organizationSettings/schemas/organization-sub-settings.schema";
import { CoursePublicController } from "./controllers/course.public.controller";

@Module({
    imports: [
        WaitTimeModule,
        UtilsModule,
        AuthModule,
        MailModule,
        MongooseModule.forFeature([
            { name: User.name, schema: UserSchema },
            { name: Facility.name, schema: FacilitySchema },
            { name: FacilityAvailability.name, schema: FacilityAvailabilitySchema },
            { name: StaffAvailability.name, schema: StaffAvailabilitySchema },
            { name: StaffProfileDetails.name, schema: StaffSchema },
            { name: Services.name, schema: ServiceSchema },
            { name: Pricing.name, schema: PricingSchema },
            { name: Purchase.name, schema: PurchaseSchema },
            { name: Room.name, schema: RoomSchema },
            { name: PayRate.name, schema: PayRateSchema },
            { name: Clients.name, schema: ClientSchema },
            { name: Enrollment.name, schema: EnrollmentSchema },
            { name: Scheduling.name, schema: SchedulingSchema },
            { name: OrganizationSubSettings.name, schema: OrganizationSubSettingsSchema },
        ]),
    ],
    controllers: [CourseController, CoursePublicController],
    providers: [CourseService],
})
export class CourseModule { }
