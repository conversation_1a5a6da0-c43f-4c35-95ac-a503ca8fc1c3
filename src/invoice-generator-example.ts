import { InvoiceGenerator, InvoiceGeneratorOptions } from './invoice-generator';
import { DiscountType } from './utils/enums/discount.enum';
import { PaymentStatus } from './utils/enums/payment.enum';

/**
 * Example usage of the InvoiceGenerator class
 * This shows how to use the class to generate invoices with all the complex calculations
 */

// Example: How to use the InvoiceGenerator
export async function generateInvoiceExample() {
  // 1. Initialize the generator with basic invoice options
  const invoiceOptions: InvoiceGeneratorOptions = {
    userId: "507f1f77bcf86cd799439011",
    organizationId: "507f1f77bcf86cd799439012",
    facilityId: "507f1f77bcf86cd799439013",
    createdBy: "507f1f77bcf86cd799439014",
    paymentBy: "507f1f77bcf86cd799439014", // or different user for sponsored purchases
    invoiceNumber: 1001,
    orderId: 2001,
    platform: "Web",
    date: new Date(),
    isInclusiveOfGst: false, // Set based on organization settings
    billingAddressId: "507f1f77bcf86cd799439015",
    clientDetails: {
      name: "John Doe",
      email: "<EMAIL>",
      phone: "+1234567890"
    },
    clientBillingDetails: {
      address: "123 Main St",
      city: "City",
      state: "State",
      zipCode: "12345"
    },
    billingDetails: {
      facilityName: "Wellness Center",
      address: "456 Center St",
      gstNumber: "GST123456789"
    },
    isForBusiness: false
  };

  const generator = new InvoiceGenerator(invoiceOptions);

  // 2. Add service items (packages)
  generator.addServiceItem({
    document: {
      _id: "507f1f77bcf86cd799439016",
      name: "Yoga Package",
      price: 1000,
      expiredInDays: 30,
      durationUnit: "days",
      isBundledPricing: false,
      tax: 18,
      services: {
        sessionType: "multiple",
        sessionCount: 10,
        sessionPerDay: 1
      }
    },
    quantity: 1,
    discountType: DiscountType.FLAT,
    discountValue: 100,
    discountedBy: "507f1f77bcf86cd799439014",
    discountAmount: 100,
    cartDiscountAmount: 50,
    taxRate: 18,
    taxAmount: 153, // Calculated from cart validation
    hsnOrSacCode: "998314",
    promotionLabel: "New Member Discount"
  });

  // 3. Add product items
  generator.addProductItem({
    inventoryDocument: {
      _id: "507f1f77bcf86cd799439017",
      productVariantId: "507f1f77bcf86cd799439018",
      salePrice: 500,
      mrp: 600,
      quantity: 10
    },
    productDocument: {
      _id: "507f1f77bcf86cd799439019",
      name: "Yoga Mat",
      gst: 12
    },
    quantity: 2,
    variantId: "507f1f77bcf86cd799439018",
    discountAmount: 50,
    cartDiscountAmount: 25,
    taxRate: 12,
    taxAmount: 108,
    hsnOrSacCode: "950390",
    salePrice: 500,
    mrp: 600,
    finalPrice: 1083
  });

  // 4. Add custom package items
  generator.addCustomPackageItem({
    document: {
      _id: "507f1f77bcf86cd799439020",
      name: "Custom Wellness Package",
      unitPrice: 2000,
      tax: 18
    },
    quantity: 1,
    discountAmount: 200,
    cartDiscountAmount: 100,
    taxRate: 18,
    taxAmount: 306,
    hsnOrSacCode: "998314",
    unitPrice: 2000
  });

  // 5. Set payment details
  generator.setPaymentDetails([
    {
      paymentMethod: "cash",
      amount: 3500,
      paymentDate: new Date(),
      paymentStatus: PaymentStatus.COMPLETED,
      paymentGateway: "",
      description: "Cash payment",
      denominations: { 500: 7 }
    }
  ]);

  // 6. Set cart totals (from cart validation)
  generator.setCartTotals({
    subTotal: 3500,
    itemDiscount: 350,
    cartDiscount: 175,
    discount: 525,
    cartDiscountType: DiscountType.FLAT,
    cartDiscountAmount: 175,
    returnDiscount: 0,
    voucherDiscount: 0,
    totalGstValue: 567,
    totalAmountAfterGst: 3542,
    roundOff: 0.42,
    grandTotal: 3542,
    discountedBy: "staff123"
  });

  // 7. Set voucher details (if any)
  generator.setVoucherDetails({
    voucherIds: ["507f1f77bcf86cd799439022"],
    totalVoucherAmount: 500,
    usedVoucherAmount: 200,
    remainingVoucherAmount: 300,
    vouchers: [{
      _id: "507f1f77bcf86cd799439022",
      name: "Welcome Voucher",
      amount: 500
    }]
  });

  // 8. Set split payment info
  generator.setSplitPayment(false, 3542);

  // 9. Execute to build the final invoice
  generator.execute();

  // 10. Access the generated data
  const invoiceToSave = generator.invoice;
  const purchaseItemsData = generator.purchaseItems;
  const productItemsData = generator.productItems;
  const customPackageItemsData = generator.customPackageItems;

  // 11. Generate purchase documents after saving invoice
  const clientUser = { _id: "507f1f77bcf86cd799439021" };
  const purchaseDocuments = generator.generatePurchaseDocuments(
    invoiceToSave._id.toString(),
    clientUser
  );

  // 12. Generate QR codes for purchases
  const qrCodeData = generator.generateQRCodeData(purchaseDocuments);

  // 13. Get summary for response
  const summary = generator.getInvoiceSummary();

  return {
    invoice: invoiceToSave,
    purchaseItems: purchaseItemsData,
    productItems: productItemsData,
    customPackageItems: customPackageItemsData,
    purchaseDocuments,
    qrCodeData,
    summary
  };
}

/**
 * Example: How to handle different scenarios
 */

// Scenario 1: Service item with bundled pricing
export function addBundledServiceItem(generator: InvoiceGenerator) {
  generator.addServiceItem({
    document: {
      _id: "bundled123",
      name: "Premium Package",
      price: 5000,
      expiredInDays: 90,
      durationUnit: "days",
      isBundledPricing: true,
      bundledPricingId: "bundle123"
    },
    quantity: 1,
    discountAmount: 500,
    taxRate: 18,
    taxAmount: 810
  });
}

// Scenario 2: Voucher item
export function addVoucherItem(generator: InvoiceGenerator) {
  generator.addVoucherItem({
    document: {
      _id: "voucher456",
      name: "Gift Voucher",
      price: 1000,
      expiredInDays: 365,
      durationUnit: "days"
    },
    quantity: 1,
    discountAmount: 0,
    taxRate: 0,
    taxAmount: 0
  });
}

// Scenario 3: Product with variant
export function addProductWithVariant(generator: InvoiceGenerator) {
  generator.addProductItem({
    inventoryDocument: {
      _id: "inv456",
      productVariantId: "var456",
      salePrice: 299,
      mrp: 399,
      quantity: 50
    },
    productDocument: {
      _id: "prod456",
      name: "Protein Powder",
      gst: 5
    },
    quantity: 3,
    variantId: "var456",
    discountAmount: 30,
    taxRate: 5,
    taxAmount: 40.43,
    finalPrice: 897.43
  });
}
