import { registerAs } from '@nestjs/config';

export default registerAs(
    'redis',
    (): Record<string, any> => ({
        cached: {
            host: process.env.REDIS_HOST,
            port: Number.parseInt(process.env.REDIS_PORT),
            password: process.env.REDIS_PASSWORD,
            username: process.env.REDIS_USERNAME,
            database: process.env.REDIS_DATABASE ?? 0,
            ttl: 5 * 1000, // 5 mins
            max: 10,
            connectTimeout: 5000, // 5 seconds
            commandTimeout: 5000, // 5 seconds
            retryStrategy: (times: number) => {
                const delay = Math.min(times * 50, 2000);
                return delay;
            },
            maxRetriesPerRequest: 3,
            enableReadyCheck: true,
            reconnectOnError: (err: Error) => {
                const targetError = 'READONLY';
                if (err.message.includes(targetError)) {
                    return true;
                }
                return false;
            },
        },
        queue: {
            host: process.env.REDIS_HOST,
            port: Number.parseInt(process.env.REDIS_PORT),
            password: process.env.REDIS_PASSWORD,
            username: process.env.REDIS_USERNAME,
            connectTimeout: 5000,
            commandTimeout: 5000,
            retryStrategy: (times: number) => {
                const delay = Math.min(times * 50, 2000);
                return delay;
            },
            maxRetriesPerRequest: 3,
        },
    })
);
