import { registerAs } from '@nestjs/config';
import { version } from 'package.json';

export default registerAs(
    'app',
    (): Record<string, any> => ({
        name: process.env.APP_NAME || "Hope wellness",
        nameSlug: (process.env.APP_NAME_SLUG ?? "").toLowerCase().replace(/ /g, '_') || "hopeWellness",
        env: process.env.NODE_ENV,
        timezone: process.env.APP_TIMEZONE || Intl.DateTimeFormat().resolvedOptions().timeZone,
        version,
        globalPrefix: '',

        http: {
            host: process.env.HTTP_HOST,
            port: Number.parseInt(process.env.HTTP_PORT),
        },
        urlVersion: {
            enable: process.env.URL_VERSIONING_ENABLE === 'true',
            prefix: 'v',
            version: process.env.URL_VERSION || '1',
        },
    })
);
