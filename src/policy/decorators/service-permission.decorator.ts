import { SetMetadata, createParamDecorator, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { ENUM_PERMISSION_TYPE } from '../enums/policy.permissions.enum';
import { ENUM_POLICY_STATUS_CODE_ERROR } from '../enums/policy.status-code.enum';

export const SERVICE_PERMISSIONS_META_KEY = 'service_permissions';

/**
 * Decorator to mark required permissions for service methods
 * @param permissions List of permissions required to execute the method
 */
export const RequirePermissions = (...permissions: ENUM_PERMISSION_TYPE[]) => 
  SetMetadata(SERVICE_PERMISSIONS_META_KEY, permissions);

/**
 * Method decorator that checks if user has required permissions before executing the method
 * @param target The class prototype
 * @param propertyKey The method name
 * @param descriptor The method descriptor
 */
export function CheckPermissions() {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      // Get the user from the method arguments (assuming it's passed as the first argument)
      const user = args[0]?.__user || args[0];
      
      if (!user) {
        throw new ForbiddenException({
          statusCode: ENUM_POLICY_STATUS_CODE_ERROR.ABILITY_FORBIDDEN,
          message: 'policy.error.abilityForbidden',
        });
      }

      // Get required permissions from metadata
      const requiredPermissions: ENUM_PERMISSION_TYPE[] = Reflect.getMetadata(
        SERVICE_PERMISSIONS_META_KEY,
        target,
        propertyKey
      ) || [];

      // If no permissions required, proceed with the method
      if (requiredPermissions.length === 0) {
        return originalMethod.apply(this, args);
      }

      // Check if user has super admin role (bypass all checks)
      if (user.role?.type === 'superAdmin' || user.role?.type === 'organization') {
        return originalMethod.apply(this, args);
      }

      // Check if user has the required permissions
      const userPermissions = user.role?.permissions?.map(p => p.name) || [];
      const hasPermission = requiredPermissions.every(permission => 
        userPermissions.includes(permission)
      );

      if (!hasPermission) {
        throw new ForbiddenException({
          statusCode: ENUM_POLICY_STATUS_CODE_ERROR.ABILITY_FORBIDDEN,
          message: 'policy.error.abilityForbidden',
        });
      }

      // If all checks pass, proceed with the method
      return originalMethod.apply(this, args);
    };

    return descriptor;
  };
}