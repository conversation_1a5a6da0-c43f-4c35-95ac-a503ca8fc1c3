import { createParamDecorator, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Types } from 'mongoose';
import { ENUM_PERMISSION_TYPE } from '../enums/policy.permissions.enum';
import { IRequestApp } from 'src/common/request/interfaces/request.interface';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';

/**
 * This decorator is used to check if the user is self or has the required permissions.
 * @param field - The field to check for self
 * @param permission - The permissions to check
 * @param data - The data to return
 * @returns 
 */
export const PolicyAllowIfSelfOrHasPermission = (
    permission: (ENUM_PERMISSION_TYPE | ENUM_ROLE_TYPE)[],
    fields?: string | string[],
    data?: 'self' | 'hasPermissions' | undefined
) =>
    createParamDecorator((_, ctx: ExecutionContext) => {
        const request: IRequestApp = ctx.switchToHttp().getRequest();
        const field = Array.isArray(fields) ? fields[0] : fields;
        const user = request.__user;

        if (!user) {
            throw new ForbiddenException('User not authenticated.');
        }

        if (user.role.type === ENUM_ROLE_TYPE.SUPER_ADMIN) {
            throw new ForbiddenException("Super admin not allowed to change organization data")
        }

        if (user.role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            return true;
        }

        const fromBody = request.body?.[field];
        const fromQuery = request.query?.[field];
        const fromParams = request.params?.[field];

        const value = ((Array.isArray(fromBody) && fromBody.length) ? fromBody : (Array.isArray(fromBody) ? null : fromBody))
            || ((Array.isArray(fromQuery) && fromQuery.length) ? fromQuery : (Array.isArray(fromQuery) ? null : fromQuery))
            || fromParams
            || user._id;

        let isSelf = false
        if (value && Array.isArray(value)) {
            isSelf = value.length == 1 && Types.ObjectId.isValid(value[0]) && new Types.ObjectId(user._id).equals(value[0])
        } else if (value && Types.ObjectId.isValid(value)) {
            isSelf = new Types.ObjectId(user._id).equals(new Types.ObjectId(value));
        } else {
            isSelf = true;
        }

        const hasPermission = permission.every((permission) => {
            return request.__permissionSet.has(permission)
        });

        const hasRolePermissions = permission.includes(user.role.type)

        // Check it it has permissions or it is self
        if (!isSelf && !hasPermission && !hasRolePermissions) {
            throw new ForbiddenException(
                `You are not authorized to perform this action for another user.`
            );
        }

        // Replace the selected user with the self user
        if (isSelf && !hasPermission && !hasRolePermissions) {
            // if (isSelf && !hasRolePermissions) {
            if (request.body?.[field] !== undefined) {
                request.body[field] = Array.isArray(fields) ? [user._id.toString()] : user._id.toString();
            }
            if (request.query?.[field] !== undefined) {
                request.query[field] = Array.isArray(fields) ? [user._id.toString()] : user._id.toString();
            }
            if (request.params?.[field] !== undefined) {
                request.params[field] = Array.isArray(fields) ? user._id.toString() : user._id.toString();
            }
        }


        if (data == 'self') {
            return isSelf;
        }
        if (data === 'hasPermissions') {
            return hasPermission;
        }
        return true;
    })();


/**
 * This decorator is used to check if the user has the required permissions.
 * @param permission 
 * @returns 
 **/
export const PolicyHasPermission = (
    ...permission: ENUM_PERMISSION_TYPE[]
) =>
    createParamDecorator((_, ctx: ExecutionContext) => {
        const request: IRequestApp = ctx.switchToHttp().getRequest();

        const user = request.__user;

        if (!user) {
            throw new ForbiddenException('User not authenticated.');
        }

        if (user.role.type === ENUM_ROLE_TYPE.SUPER_ADMIN || user.role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            return true;
        }

        const hasPermission = permission.every((permission) => {
            return request.__permissionSet.has(permission)
        });

        return hasPermission;
    })();
