import { DynamicModule, Global, Module } from '@nestjs/common';
import { PolicyRepositoryModule } from 'src/policy/repository/policy.repository.module';
import { PermissionService } from 'src/policy/services/permission.service';
import { PolicyService } from './services/policy.service';
import { AuthModule } from 'src/auth/auth.module';
import { PolicyController } from './controllers/policy.controller';
import { PolicyAdminController } from './controllers/policy.admin.controller';

@Global()
@Module({})
export class PolicyModule {
    static forRoot(): DynamicModule {
        return {
            module: PolicyModule,
            providers: [PermissionService, PolicyService],
            exports: [PermissionService, PolicyService],
            imports: [PolicyRepositoryModule],
            controllers: [
                PolicyController,
                PolicyAdminController
            ],
        };
    }
}
