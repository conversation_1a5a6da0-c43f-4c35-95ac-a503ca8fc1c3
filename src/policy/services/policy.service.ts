import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { Document, InsertManyResult, PipelineStage, Types } from 'mongoose';
import {
    IDatabaseCreateOptions,
    IDatabaseFindAllOptions,
    IDatabaseGetTotalOptions,
    IDatabaseCreateManyOptions,
    IDatabaseSaveOptions,
    IDatabaseDeleteManyOptions,
    IDatabaseFindOneOptions,
    IDatabaseOptions,
} from 'src/common/database/interfaces/database.interface';
import { PolicyCreateRequestDto } from 'src/policy/dtos/request/policy.create.request.dto';
import { PolicyUpdateRequestDto } from 'src/policy/dtos/request/policy.update.request.dto';
import { PolicyGetResponseDto } from 'src/policy/dtos/response/policy.get.response.dto';
import { PolicyListResponseDto } from 'src/policy/dtos/response/policy.list.response.dto';
import {
    PolicyDocument,
    PolicyEntity,
} from 'src/policy/repository/entities/policy.entity';
import { PolicyRepository } from 'src/policy/repository/repositories/policy.repository';
import { PermissionService } from './permission.service';
import { UserService } from 'src/users/services/user.service';
import { IDatabaseObjectId } from 'src/common/database/interfaces/database.objectid.interface';
import { ENUM_POLICY_ACTION, ENUM_POLICY_TYPE } from '../enums/policy.enum';
import { PolicyAssignRequestDto } from '../dtos/request/policy.assign.request.dto';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';
import { ENUM_PERMISSION_TYPE } from '../enums/policy.permissions.enum';

@Injectable()
export class PolicyService {
    constructor(
        private readonly policyRepository: PolicyRepository,
        private readonly permissionService: PermissionService,
        private readonly userService: UserService
    ) { }

    async findAll(
        find?: Record<string, any>,
        options?: IDatabaseFindAllOptions
    ): Promise<PolicyDocument[]> {
        return this.policyRepository.findAll(find, options);
    }

    async getTotal(
        find?: Record<string, any>,
        options?: IDatabaseGetTotalOptions
    ): Promise<number> {
        return this.policyRepository.getTotal(find, options);
    }

    async findOneById(
        _id: Types.ObjectId,
        options?: IDatabaseFindOneOptions
    ): Promise<PolicyDocument> {
        return this.policyRepository.findOneById(_id, options);
    }

    async findOne(
        find: Record<string, any>,
        options?: IDatabaseFindOneOptions
    ): Promise<PolicyDocument> {
        return this.policyRepository.findOne(find, options);
    }

    async create(
        { subject, description, module, action, type, permissions, isDelegated }: PolicyCreateRequestDto,
        options?: IDatabaseCreateOptions
    ): Promise<PolicyDocument> {
        const create: PolicyEntity = new PolicyEntity();
        create.subject = subject;
        create.module = module;
        create.action = action;
        create.description = description;
        create.type = type;
        create.permissions = permissions ? permissions.map((id) => new Types.ObjectId(id)) : [];
        create.isDelegated = isDelegated;
        create.isActive = true;

        return this.policyRepository.create<PolicyEntity>(create, options);
    }

    async createMany(
        body: PolicyCreateRequestDto[],
        options?: IDatabaseCreateOptions
    ): Promise<InsertManyResult<PolicyEntity>> {
        const createList = body.map(({ subject: name, description, type, permissions, isDelegated }) => {
            const create: PolicyEntity = new PolicyEntity();
            create.subject = name;
            create.description = description;
            create.type = type;
            create.permissions = permissions ? permissions.map((id) => new Types.ObjectId(id)) : [];
            create.isDelegated = isDelegated;
            create.isActive = true;
            return create;
        }) as PolicyEntity[];

        return this.policyRepository.createMany<PolicyEntity>(createList, options);
    }

    async update(
        repository: PolicyDocument,
        { permissions, type, description, isDelegated, subject }: PolicyUpdateRequestDto,
        options?: IDatabaseSaveOptions
    ): Promise<PolicyDocument> {
        repository.subject = subject ?? repository.subject;
        repository.description = description ?? repository.description;
        repository.type = type ?? repository.type;
        repository.permissions = permissions?.map((id) => new Types.ObjectId(id)) ?? repository.permissions;
        repository.isDelegated = isDelegated ?? repository.isDelegated;

        return this.policyRepository.save(repository, options);
    }

    async updateOne(
        filter: Record<string, any>,
        update: Record<string, any>,
        options?: { upsert?: boolean }
    ): Promise<PolicyDocument> {
        return this.policyRepository.update(
            filter,
            update,
        );
    }

    async deleteMany(
        find: Record<string, any>,
        options?: IDatabaseDeleteManyOptions
    ): Promise<boolean> {
        await this.policyRepository.deleteMany(find, options);
        return true;
    }

    async aggregate<T>(
        pipelines: PipelineStage[],
        options?: IDatabaseOptions
    ): Promise<T[]> {
        return this.policyRepository.aggregate(pipelines, options);
    }

    async exists(find: Record<string, any>, option?: IDatabaseOptions): Promise<boolean> {
        return this.policyRepository.exists(find, option)
    }

    mapList(policies: PolicyDocument[] | PolicyEntity[]): PolicyListResponseDto[] {
        return plainToInstance(
            PolicyListResponseDto,
            policies.map((e: PolicyDocument | PolicyEntity) =>
                e instanceof Document ? e.toObject() : e
            )
        );
    }

    mapGet(policy: PolicyDocument | PolicyEntity): PolicyGetResponseDto {
        return plainToInstance(
            PolicyGetResponseDto,
            policy instanceof Document ? policy.toObject() : policy
        );
    }

    // async createNewPolicy(policy: PolicyCreateRequestDto): Promise<PolicyDocument> {
    //     // check if policy already exists
    //     const permission = await this.permissionService.findAll({ _id: {$in: policy.permissions} });
    //     if (!permission.length || permission.length !== policy.permissions.length) {
    //         throw new Error('Invalid permissions');
    //     }

    //     return this.create(policy);
    // }

    async listActions(): Promise<any[]> {
        return [ENUM_POLICY_ACTION.VIEW, ENUM_POLICY_ACTION.CREATE, ENUM_POLICY_ACTION.UPDATE, ENUM_POLICY_ACTION.FULL_ACCESS];
    }

    async getAllForSettingPolicies(find?: Record<string, any>, options?: Record<string, any>): Promise<{ data: any[], total: number }> {
        const pipeline: PipelineStage[] = [
            // Stage 0: Match the documents
            {
                $match: find
            },
            // Stage 1: Group by module and subject
            {
                $group: {
                    _id: {
                        module: "$module",
                        subject: "$subject"
                    },
                    type: { $first: "$type" },
                    actions: {
                        $push: {
                            _id: "$_id",
                            isActive: "$isActive",
                            subject: "$subject",
                            module: "$module",
                            action: "$action",
                            description: "$description"
                        }
                    }
                }
            },

            // Stage 2: Group by module to merge all subjects
            {
                $group: {
                    _id: "$_id.module",
                    subjects: {
                        $push: {
                            type: "$type",
                            subject: "$_id.subject",
                            actions: "$actions"
                        }
                    }
                }
            },

            // Stage 3: Format the final result
            {
                $project: {
                    _id: 0,
                    module: "$_id",
                    type: 1,
                    subjects: 1
                }
            },

            // Stage 4: Add pagination if needed
            {
                $facet: {
                    data: [
                        { $sort: { module: 1 } },
                        { $skip: options?.paging?.offset || 0 },
                        { $limit: options?.paging?.limit || 20 },
                    ],
                    total: [{ $count: "total" }],
                },
            }
        ];

        const res = await this.policyRepository.aggregate(pipeline);
        const data = res[0].data;
        const total = res[0].total[0].total;
        return {
            data,
            total
        }
    }

    async getAllUserSettingPolicies(find?: Record<string, any>, options?: Record<string, any>): Promise<{ data: any[], total: number }> {
        const { userId, ...match } = find;
        const user = await this.userService.findOneWithRoleAndPermissions({ _id: userId });
        // Only include assigned policies, excluding role policies
        const rolePolicyIds = user.role?.policies?.map((policy) => new Types.ObjectId(policy._id)) || [];
        const policyIds = user.assignedPolicies?.map((policy) => new Types.ObjectId(policy._id.toString())) || [];
        const restrictedPolicyIds = new Set(user?.restrictedPolicies?.map((policy) => policy._id.toString()) || []);

        const permittedPolicies = [...policyIds, ...rolePolicyIds].filter((id) => !restrictedPolicyIds.has(id.toString()));
        // if (!policyIds.length) {
        //     match['_id'] = {
        //         $nin: policyIds
        //     };
        // }

        const pipeline: PipelineStage[] = [
            // Stage 0: Match the documents
            {
                $match: match
            },
            // Stage 1: Group by module, type, and subject
            {
                $group: {
                    _id: {
                        module: "$module",
                        subject: "$subject"
                    },
                    type: { $first: "$type" },
                    actions: {
                        $push: {
                            _id: "$_id",
                            isActive: "$isActive",
                            subject: "$subject",
                            module: "$module",
                            action: "$action",
                            permitted: {
                                $cond: {
                                    if: { $in: ["$_id", permittedPolicies] },
                                    then: true,
                                    else: false
                                }
                            },
                            description: "$description",
                        }
                    }
                }
            },

            {
                $sort: { "_id.subject": 1 }
            },

            // Stage 2: Group by module to create the subjects array
            {
                $group: {
                    _id: "$_id.module",
                    subjects: {
                        $push: {
                            type: "$type",
                            subject: "$_id.subject",
                            actions: "$actions"
                        }
                    }
                }
            },

            // Stage 3: Format the final result
            {
                $project: {
                    _id: 0,
                    module: "$_id",
                    type: 1,
                    subjects: 1
                }
            },

            // Stage 4: Add pagination if needed
            {
                $facet: {
                    data: [
                        { $sort: { module: 1 } },
                        { $skip: options?.paging?.offset || 0 },
                        { $limit: options?.paging?.limit || 20 },
                    ],
                    total: [{ $count: "total" }],
                },
            }
        ];

        const res = await this.policyRepository.aggregate(pipeline);
        const data = res[0].data;
        const total = res[0].total[0].total;
        return {
            data,
            total
        }
    }

    async getUsersAllPolicyV1(userId: IDatabaseObjectId) {
        const user = await this.userService.findOneWithRoleAndPermissions({ _id: userId });
        if (!user) {
            throw new NotFoundException('User not found');
        }

        if (user.role && user.role.type === ENUM_ROLE_TYPE.SUPER_ADMIN) {
            throw new BadRequestException('Super admin has all policies');
        }
        if (user.role && user.role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            throw new BadRequestException('Organization has all policies');
        }

        if (!user.role && !user.assignedPolicies) {
            throw new NotFoundException('User has no policies');
        }

        const policies = [...(user.role?.policies || []), ...(user.assignedPolicies || [])];
        // const restrictedPolicies = user.restrictedPolicies || [];

        return {
            policies,
            // restrictedPolicies
        }
    }

    async getUsersAllPolicy(userId: IDatabaseObjectId) {
        const user = await this.userService.findOneWithRoleAndPermissions({ _id: userId });
        if (!user) {
            throw new NotFoundException('User not found');
        }

        if (user.role?.type === ENUM_ROLE_TYPE.SUPER_ADMIN) {
            throw new BadRequestException('Super admin has all policies');
        }

        if (user.role?.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            throw new BadRequestException('Organization has all policies');
        }

        if (!user.role && !user.assignedPolicies) {
            return []
        }

        // Combine policies from role and assigned policies
        const rolePolicy = user.role?.policies || [];
        const assignedPolicy = user.assignedPolicies || [];
        const restrictedPolicyIds = new Set(user.restrictedPolicies?.map((policy) => policy._id.toString()) || []);

        const allPolicies = [...rolePolicy, ...assignedPolicy].filter((policy) => !restrictedPolicyIds.has(policy._id.toString()));

        // Group policies by module and subject
        const groupedPolicies = allPolicies.reduce((acc, policy) => {
            const module = policy.module;
            const subject = policy.subject;

            // Initialize module if it doesn't exist
            if (!acc[module]) {
                acc[module] = {
                    module,
                    subjects: {}
                };
            }

            // Initialize subject if it doesn't exist
            if (!acc[module].subjects[subject]) {
                acc[module].subjects[subject] = {
                    subject,
                    type: policy.type, // Moved type here
                    actions: []
                };
            }

            // Add action with permissions
            acc[module].subjects[subject].actions.push({
                _id: policy._id,
                isActive: policy.isActive,
                subject: policy.subject,
                module: policy.module,
                action: policy.action,
                description: policy.description,
                permissions: policy.permissions.map(permission => ({
                    _id: permission._id,
                    name: permission.name,
                    type: permission.type,
                    description: permission.description,
                    isDelegated: permission.isDelegated
                }))
            });

            return acc;
        }, {});

        // Transform the grouped data into array format
        const formattedData = Object.values(groupedPolicies).map((moduleData: any) => ({
            module: moduleData.module,
            subjects: Object.values(moduleData.subjects).map((subjectData: any) => ({
                subject: subjectData.subject,
                type: subjectData.type, // Type is now at subject level
                actions: subjectData.actions
            }))
        }));

        return {
            data: formattedData,
            total: allPolicies.length
        };
    }

    async setUsersPolicies(userId: IDatabaseObjectId, body: PolicyAssignRequestDto) {
        const iUser = await this.userService.findOneWithRoleAndPermissions({ _id: userId });
        if (!iUser) {
            throw new NotFoundException('User not found');
        }

        if (iUser.role && (iUser.role.type === ENUM_ROLE_TYPE.SUPER_ADMIN || iUser.role.type === ENUM_ROLE_TYPE.ORGANIZATION)) {
            throw new BadRequestException('Admin has all policies');
        }

        const defaultPolicyIds = new Set(iUser.role?.policies.map((policy) => policy._id.toString()) || []);

        const removePolicyIds = body.remove && body.remove.length
            ? (await this.policyRepository.findAll({ $or: body.remove.map(policy => ({ module: policy.module, subject: policy.subject })) }, { select: '_id' }))
                .map(policy => policy._id.toString())
            : [];

        const assignPolicyIds = new Set(body.assign.map(policy => policy.actionId));
        const newAssignPolicyIds = [...assignPolicyIds].filter(id => !defaultPolicyIds.has(id));

        // Validate policy IDs
        const policyCounts = await this.policyRepository.getTotal({ _id: { $in: newAssignPolicyIds.map(id => new Types.ObjectId(id)) } });
        if (policyCounts !== newAssignPolicyIds.length) {
            throw new NotFoundException('Invalid policies');
        }

        // Check for conflicting policies
        const conflictingPoliciesIds = []
        for (const item of body.assign) {
            const conflictingPolicies = await this.policyRepository.findAll(
                { module: item.module, subject: item.subject, _id: { $ne: new Types.ObjectId(item.actionId) } },
                { select: '_id' }
            );
            conflictingPolicies.forEach(policy => conflictingPoliciesIds.push(policy._id.toString()));
        }

        // Only Remove policies that are assigned by default (from Role)
        const removeIds = [...new Set([...removePolicyIds, ...conflictingPoliciesIds])]
            .filter(id => !newAssignPolicyIds.includes(id))
            .filter(id => defaultPolicyIds.has(id));

        await this.userService.updatePolicies(
            { _id: userId },
            newAssignPolicyIds.map(id => new Types.ObjectId(id)),
            removeIds.map(id => new Types.ObjectId(id.toString()))
        );

        return true;
    }

    async changeStatus(policyId: IDatabaseObjectId) {
        const policy = await this.policyRepository.findOneById(policyId);
        if (!policy) {
            throw new NotFoundException('Policy not found');
        }

        policy.isActive = !policy.isActive;
        await policy.save();
        return true;
    }

    async assignPermissions(
        policyId: IDatabaseObjectId,
        permissions: string[]
    ): Promise<boolean> {
        const policy = await this.policyRepository.findOneById(policyId);

        if (!policy) {
            throw new NotFoundException('Policy not found');
        }

        const permissionsDocs = await this.permissionService.findAll({ _id: { $in: permissions } }, { select: '_id' });
        if (permissionsDocs.length !== permissions.length) {
            throw new NotFoundException('Invalid permissions');
        }

        // Update the permissions array of the policy
        policy.permissions = permissionsDocs.map((permission) => permission._id);
        await policy.save();

        return true;
    }

    async getAllPermissionsForPolicySetting(
        find: Record<string, any>,
        options?: IDatabaseFindAllOptions
    ): Promise<{ data: any[], total: number }> {
        const { id, ...match } = find;
        const policy = await this.findOneById(id, { select: '_id' });
        if (!policy) {
            throw new NotFoundException('Policy not found');
        }

        const pipeline: PipelineStage[] = [
            // Stage 1: Match active permissions
            {
                $match: match
            },

            // Stage 2: Lookup policy permissions to check if permitted
            {
                $lookup: {
                    from: 'policies',
                    let: { permissionId: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$_id', policy._id] },
                                        {
                                            $in: [
                                                '$$permissionId',
                                                { $ifNull: ['$permissions', []] }
                                            ]
                                        }
                                    ]
                                }
                            }
                        }
                    ],
                    as: 'policyPermissions'
                }
            },

            // Stage 3: Add permitted field
            {
                $addFields: {
                    permitted: { $gt: [{ $size: '$policyPermissions' }, 0] }
                }
            },

            // Stage 4: Remove lookup results and unnecessary fields
            {
                $project: {
                    policyPermissions: 0,
                    createdAt: 0,
                    updatedAt: 0,
                    __v: 0
                }
            },

            // Stage 5: Sort by permitted (true first) and then by name
            {
                $sort: {
                    permitted: -1,
                    name: 1
                }
            },

            // Stage 6: Pagination facet
            {
                $facet: {
                    data: [
                        { $skip: options?.paging?.offset || 0 },
                        { $limit: options?.paging?.limit || 2000 }
                    ],
                    total: [{ $count: 'count' }]
                }
            }
        ];

        const result: any = await this.permissionService.aggregate(pipeline);

        return {
            data: result[0].data,
            total: result[0].total[0]?.count || 0
        };
    }

    async listPermissions(
        find: Record<string, any>,
        options?: IDatabaseFindAllOptions
    ): Promise<{ data: any[], total: number }> {
        const pipeline: PipelineStage[] = [
            // Stage 1: Match criteria
            {
                $match: find
            },

            // Stage 2: Project needed fields
            {
                $project: {
                    _id: 1,
                    name: 1,
                    description: 1,
                    type: 1,
                    isActive: 1
                }
            },

            // Stage 3: Sort by name
            {
                $sort: {
                    name: 1
                }
            },

            // Stage 4: Pagination facet
            {
                $facet: {
                    data: [
                        { $sort: { name: 1 } },
                        { $skip: options?.paging?.offset },
                        { $limit: options?.paging?.limit }
                    ],
                    total: [{ $count: 'count' }]
                }
            }
        ];

        const result: any = await this.permissionService.aggregate(pipeline);

        return {
            data: result[0]?.data || [],
            total: result[0]?.total[0]?.count || 0
        };
    }

    async isHasAbility(userId: IDatabaseObjectId | string, type: ENUM_POLICY_TYPE, action: ENUM_POLICY_ACTION) {
        const user = await this.userService.findOneWithRoleAndPermissions({
            _id: new Types.ObjectId(userId)
        })

        if (user.role.type === ENUM_ROLE_TYPE.ORGANIZATION) {
            return true;
        }

        const policy = await this.exists({
            type: type,
            action: action
        });

        if (!policy || !user) {
            return false
        }
        const restrictedPoliciesIdSet = new Set(user?.restrictedPolicies.map((p) => `${p.type}.${p.action}`))
        if (restrictedPoliciesIdSet.has(`${type}.${action}`)) {
            return false
        }
        const policiesSet = new Set([...user?.role?.policies || [], ...user?.assignedPolicies || []].map(p => `${p.type}.${p.action}`))

        return policiesSet.has(`${type}.${action}`)
    }

}
