
import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { Types } from 'mongoose';
import { IDatabaseObjectId } from 'src/common/database/interfaces/database.objectid.interface';
import { Type } from 'class-transformer';
import { ValidateNested } from 'class-validator';
import { ENUM_POLICY_ACTION } from 'src/policy/enums/policy.enum';

export class PolicyAssignDto {
    @ApiProperty({
        description: 'Name of the module',
        example: "Authentication",
        required: true,
    })
    @IsString()
    @IsNotEmpty()
    module: string;

    @ApiProperty({
        description: 'Name of the subject',
        example: "Client onboarding",
        required: true,
    })
    @IsString()
    @IsNotEmpty()
    subject: string;

    @ApiProperty({
        description: 'Id of the action',
        example: '603d2149e773f2a3990b47f5',
        required: true,
    })
    @IsMongoId()
    @IsNotEmpty()
    // @Transform(({ value }) => {
    //     return new Types.ObjectId(value)
    // })
    actionId: Types.ObjectId;

}
export class PolicyRestrictDto extends OmitType(PolicyAssignDto, ['actionId']) {

    // @ApiProperty({
    //     description: 'Id of the action',
    //     example: ENUM_POLICY_ACTION.RESTRICTED,
    //     required: true,
    //     enum: ENUM_POLICY_ACTION,
    // })
    // @IsNotEmpty()
    // @IsEnum(ENUM_POLICY_ACTION)
    // @IsOptional()
    // // @Transform(({ value }) => {
    // //     return new Types.ObjectId(value)
    // // })
    // actionId? : ENUM_POLICY_ACTION.RESTRICTED = ENUM_POLICY_ACTION.RESTRICTED;

}

export class PolicyAssignRequestDto {
    @ApiProperty({
        description: 'List of policy assignments',
        type: [PolicyAssignDto],
        required: false,
    })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => PolicyAssignDto)
    assign?: PolicyAssignDto[] = [];

    @ApiProperty({
        description: 'List of policy removals',
        type: [PolicyRestrictDto],
        required: false,
    })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => PolicyRestrictDto)
    remove?: PolicyRestrictDto[] = [];
}

