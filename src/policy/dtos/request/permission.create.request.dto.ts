import { faker } from '@faker-js/faker';
import {
    ApiProperty,
    IntersectionType,
    OmitType,
    PickType,
} from '@nestjs/swagger';
import { IsString, IsNotEmpty, MaxLength, MinLength } from 'class-validator';
import { RoleUpdateRequestDto } from 'src/role/dtos/request/role.update.request.dto';
import { PermissionUpdateRequestDto } from './permission.update.request.dto';

export class PermissionCreateRequestDto extends IntersectionType(
    OmitType(PermissionUpdateRequestDto, ['description', 'isDelegated'] as const),
    PickType(PermissionUpdateRequestDto, ['description', 'isDelegated'] as const)
) {
    @ApiProperty({
        description: 'Name of role',
        example: faker.person.jobTitle(),
        required: true,
    })
    @IsString()
    @IsNotEmpty()
    @MinLength(3)
    @MaxLength(30)
    name: string;
}
