import { ApiProperty } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { <PERSON><PERSON>rray, IsEnum, IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';
import { Transform, Type } from 'class-transformer';
import { ENUM_POLICY_ACTION } from 'src/policy/enums/policy.enum';

export class PolicyCreateRequestDto {
    
    @ApiProperty({
        description: 'Name of the policy',
        example: 'User',
        required: true,
    })
    @IsString()
    @IsNotEmpty()
    @MaxLength(100, { message: 'Policy name must be less than 100 characters' })
    subject: string;

    @ApiProperty({
        description: 'Name of the policy',
        example: 'User',
        required: true,
    })
    @IsString()
    @IsNotEmpty()
    @MaxLength(100, { message: 'Policy name must be less than 100 characters' })
    module: string;

    @ApiProperty({
        description: 'Name of the policy',
        example: 'read',
        required: true,
    })
    @IsString()
    @IsNotEmpty()
    @MaxLength(30)
    action: ENUM_POLICY_ACTION;

    @ApiProperty({
        description: 'Description of the policy',
        example: 'Allows management of user accounts',
        required: false,
    })
    @IsString()
    @IsOptional()
    description?: string;

    @ApiProperty({
        description: 'Policy type',
        example: "class-setup-service-categorysubtype",
        required: false,
        type: String,
    })
    @IsNotEmpty()
    type?: string;

    @ApiProperty({
        description: 'List of permission IDs',
        example: ['603d2149e773f2a3990b47f5', '603d2149e773f2a3990b47f6'],
        required: false,
    })
    @IsArray()
    @IsOptional()
    permissions?: string[];

    @ApiProperty({
        description: 'Indicates if the policy is active',
        example: true,
        required: false,
    })
    @IsOptional()
    @Type(() => Boolean)
    isActive?: boolean;

    @ApiProperty({
        description: 'Indicates if the policy is delegated',
        example: false,
        required: true,
    })
    @IsOptional()
    @Type(() => Boolean)
    isDelegated?: boolean;
}
