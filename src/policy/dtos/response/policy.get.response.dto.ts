import { faker } from '@faker-js/faker';
import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { DatabaseDto } from 'src/common/database/dtos/database.dto';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';
import { PermissionGetResponseDto } from 'src/policy/dtos/response/permission.get.response.dto';

export class PolicyGetResponseDto extends DatabaseDto {
    @ApiProperty({
        description: 'Name of policy',
        example: faker.word.noun(),
        required: true,
    })
    name: string;

    @ApiProperty({
        description: 'Description of policy',
        example: faker.lorem.sentence(),
        required: false,
    })
    description?: string;

    @ApiProperty({
        description: 'Active flag of policy',
        example: true,
        required: true,
    })
    isActive: boolean;

    @ApiProperty({
        description: 'Type of policy',
        example: ENUM_ROLE_TYPE.ORGANIZATION,
        required: true,
        enum: ENUM_ROLE_TYPE,
    })
    type: ENUM_ROLE_TYPE;

    @ApiProperty({
        description: 'Indicates if the policy is delegated',
        example: false,
        required: true,
    })
    isDelegated: boolean;

    @ApiProperty({
        type: PermissionGetResponseDto,
        oneOf: [{ $ref: getSchemaPath(PermissionGetResponseDto) }],
        required: true,
        isArray: true,
        default: [],
    })
    @Type(() => PermissionGetResponseDto)
    permissions: PermissionGetResponseDto[];
}

