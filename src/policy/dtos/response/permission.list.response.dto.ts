import { ApiHideProperty, ApiProperty, OmitType } from '@nestjs/swagger';
import { Exclude, Transform } from 'class-transformer';
import { PermissionGetResponseDto } from 'src/policy/dtos/response/permission.get.response.dto';

export class PermissionListResponseDto extends OmitType(PermissionGetResponseDto, [
    'description',
] as const) {
    @ApiHideProperty()
    @Exclude()
    description?: string;
}
