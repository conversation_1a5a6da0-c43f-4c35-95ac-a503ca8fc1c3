export enum ENUM_POLICY_ACTION {
    VIEW = 'view',
    CREATE = 'create',
    UPDATE = 'update',
    FULL_ACCESS = 'full access',
    RESTRICTED = 'restricted',
}

// export enum ENUM_ROLE_TYPE {
//     SUPER_ADMIN = 'superAdmin',
//     ORGANIZATION = 'organization',
//     WEB_MASTER = 'webMaster',
//     FRONT_DESK_ADMIN = 'frontDeskAdmin',
//     TRAINER = 'trainer',
//     USER = 'user',
// }


export enum ENUM_POLICY_MODULE {
    AUTHENTICATION = 'Authentication',
    POS = 'POS',
    SERVICES = 'Services',
    SCHEDULING = 'Scheduling',

    REPORTS = 'Reports',
    PROMOTIONS = "Promotions",
    ORDERS = "Orders",

    SETTINGS = "Settings",

    PURCHASE = "Purchase",
}

/**
 * Enum for policy types.
 * The key format is: {MODULE}-{SUBJECT}
 * - MODULE: The main module (e.g., scheduling, pos, class-setup, authentication, pricing)
 * - SUBJECT: The specific subject or area within the module (e.g., availability, pos, service-categorysubtype, staff-onboarding, customer-onboarding, pricing, branch-onboarding, rooms)
 */
export enum ENUM_POLICY_TYPE {
    // Authentication
    AUTHENTICATION_CUSTOMER_ONBOARDING = "authentication-customer-onboarding",
    AUTHENTICATION_STAFF_ONBOARDING = "authentication-staff-onboarding",
    AUTHENTICATION_BRANCH_ONBOARDING = "authentication-branch-onboarding",
    AUTHENTICATION_PERMISSIONS = "authentication-permissions",

    // POS
    POS_POS = "pos-pos",
    POS_Z_OUT = "pos-z-out",
    POS_DISCOUNT = "pos-apply-discount",
    POS_VOUCHER_APPLY = "pos-apply-voucher",

    // Services
    SERVICES_PRICING = "services-pricing",
    SERVICES_SERVICE_SETUP = "services-service-setup",
    SERVICES_DISCOUNTS = "services-discount-management",
    SERVICES_VOUCHER = "services-voucher",

    // Scheduling
    SCHEDULING_AVAILABILITY = "scheduling-availability",
    SCHEDULING_BOOKING = "scheduling-booking",
    SCHEDULING_CLASS = "scheduling-class",
    SCHEDULING_COURSE = "scheduling-course",
    SCHEDULING_PERSONAL_APPOINTMENT = "scheduling-personal-appointment",

    // Reports
    REPORTS_REPORT_EXPORT = "reports-report-export",

    // Orders
    ORDERS_ORDER = "orders-order",
    ORDERS_ORDER_EXPORT = "orders-order-export",

    // Setting
    SETTING_ROOM = "settings-room",
    SETTING_ANNOUNCEMENT_ANNOUNCEMENT = "settings-announcement",
    SETTING_FEATURE_TABS = "settings-feature-tab",

    // Purchase
    PURCHASE_PURCHASE = "purchase-purchase",
}

export const ENUM_POLICY_SUBJECT_NAME = {
    // Authentication
    [ENUM_POLICY_TYPE.SCHEDULING_PERSONAL_APPOINTMENT]: "Personal Appointment",
    [ENUM_POLICY_TYPE.AUTHENTICATION_CUSTOMER_ONBOARDING]: "Customer Onboarding",
    [ENUM_POLICY_TYPE.AUTHENTICATION_STAFF_ONBOARDING]: "Staff Onboarding",
    [ENUM_POLICY_TYPE.AUTHENTICATION_BRANCH_ONBOARDING]: "Branch Onboarding",
    [ENUM_POLICY_TYPE.AUTHENTICATION_PERMISSIONS]: "Permissions",

    // POS
    [ENUM_POLICY_TYPE.POS_POS]: "POS",
    [ENUM_POLICY_TYPE.POS_Z_OUT]: "Z-Out",
    [ENUM_POLICY_TYPE.POS_DISCOUNT]: "Apply Discount",
    [ENUM_POLICY_TYPE.POS_VOUCHER_APPLY]: "Apply Voucher",

    // Scheduling
    [ENUM_POLICY_TYPE.SCHEDULING_AVAILABILITY]: "Staff Appointment",
    [ENUM_POLICY_TYPE.SCHEDULING_BOOKING]: "Booking",
    [ENUM_POLICY_TYPE.SCHEDULING_CLASS]: "Class",
    [ENUM_POLICY_TYPE.SCHEDULING_COURSE]: "Course",

    // Pricing
    // [ENUM_POLICY_TYPE.PRICING_PRICING]: "Pricing",
    // [ENUM_POLICY_TYPE.PRICING_DISCOUNTS]: "Discount Management",
    [ENUM_POLICY_TYPE.SERVICES_PRICING]: "Pricing",
    [ENUM_POLICY_TYPE.SERVICES_SERVICE_SETUP]: "Service Setup",
    [ENUM_POLICY_TYPE.SERVICES_DISCOUNTS]: "Discount Management",

    // Setting
    [ENUM_POLICY_TYPE.SETTING_FEATURE_TABS]: "Feature Tab",
    [ENUM_POLICY_TYPE.SETTING_ROOM]: "Room",
    [ENUM_POLICY_TYPE.SETTING_ANNOUNCEMENT_ANNOUNCEMENT]: "Announcement",

    // Reports
    [ENUM_POLICY_TYPE.REPORTS_REPORT_EXPORT]: "Report Export",

    // Orders
    [ENUM_POLICY_TYPE.ORDERS_ORDER_EXPORT]: "Order Export",
    [ENUM_POLICY_TYPE.ORDERS_ORDER]: "Order",

    // Purchase
    [ENUM_POLICY_TYPE.PURCHASE_PURCHASE]: "Package",
};
