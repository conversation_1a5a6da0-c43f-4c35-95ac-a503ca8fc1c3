/**
 * Enum for permission types.
 * The key format is: MODULE.SUBMODULE.ACTION
 * - MODULE: The main module (e.g., policy, role, user)
 * - SUBMODULE: Optional submodule or specific area within the module
 * - ACTION: The permitted action (e.g., view, write, full_access)
 */

export enum ENUM_PERMISSION_TYPE {

    SCHEDULING_SCHEDULE_READ = 'scheduling.read',

    SCHEDULING_SCHEDULE_BOOKING = 'scheduling.schedule.booking',
    SCHEDULING_SCHEDULE_PERSONAL_APPOINTMENT = 'scheduling.schedule.personalAppointment',
    SCHEDULING_SCHEDULE_CLASS = 'scheduling.schedule.class',
    SCHEDULING_SCHEDULE_COURSE = 'scheduling.schedule.course',

    SCHEDULING_UPDATE_BOOKING = 'scheduling.update.booking',
    SCHEDULING_UPDATE_PERSONAL_APPOINTMENT = 'scheduling.update.personalAppointment',
    SCHEDULING_UPDATE_CLASS = 'scheduling.update.class',
    SCHEDULING_UPDATE_COURSE = 'scheduling.update.course',

    SCHEDULING_CANCEL = 'scheduling.cancel',
    SCHEDULING_CHECKIN = 'scheduling.checkin',
    SCHEDULING_AVAILABILITY = 'scheduling.availability',
    SCHEDULING_ENROLL = 'scheduling.enroll',

    // Pricing
    PRICING_WRITE = 'pricing.write',
    PRICING_READ = 'pricing.read',
    PRICING_UPDATE = 'pricing.update',
    PRICING_DELETE = 'pricing.delete',

    // Voucher 
    VOUCHER_WRITE = 'voucher.write',
    VOUCHER_READ = 'voucher.read',
    VOUCHER_UPDATE = 'voucher.update',
    VOUCHER_DELETE = 'voucher.delete',

    // Clients
    CLIENTS_WRITE = 'clients.write',
    CLIENTS_READ = 'clients.read',
    CLIENTS_UPDATE = 'clients.update',
    CLIENTS_DELETE = 'clients.delete',

    // Facility
    FACILITY_WRITE = 'facility.write',
    FACILITY_READ = 'facility.read',
    FACILITY_UPDATE = 'facility.update',
    FACILITY_DELETE = 'facility.delete',

    // Staff
    STAFF_WRITE = 'staff.write',
    STAFF_READ = 'staff.read',
    STAFF_UPDATE = 'staff.update',
    STAFF_DELETE = 'staff.delete',

    STAFF_AVAILABILITY_WRITE = 'staff.availability.write',
    STAFF_AVAILABILITY_READ = 'staff.availability.read',
    STAFF_AVAILABILITY_UPDATE = 'staff.availability.update',
    STAFF_AVAILABILITY_DELETE = 'staff.availability.delete',

    // Room
    ROOM_WRITE = 'room.write',
    ROOM_READ = 'room.read',
    ROOM_UPDATE = 'room.update',
    ROOM_DELETE = 'room.delete',

    // Purchase
    PURCHASE_WRITE = 'purchase.write',
    PURCHASE_READ = 'purchase.read',
    // PURCHASE_UPDATE = 'purchase.update',
    PURCHASE_EXPORT = 'purchase.export',
    PURCHASE_REPORT_EXPORT = 'purchase.report.export',
    PURCHASE_PACKAGE_UPDATE_START = "purchase.package.update.start",
    PURCHASE_PACKAGE_UPDATE_SESSION = "purchase.package.update.session",

    PURCHASE_INVOICE_READ = 'purchase.invoice.read',
    PURCHASE_INVOICE_WRITE = 'purchase.invoice.write',
    PURCHASE_INVOICE_PAYMENT = 'purchase.invoice.payment',

    // Amenities
    AMENITIES_WRITE = 'amenities.write',
    AMENITIES_READ = 'amenities.read',
    AMENITIES_UPDATE = 'amenities.update',
    AMENITIES_DELETE = 'amenities.delete',

    // Attributes
    ATTRIBUTES_WRITE = 'attributes.write',
    ATTRIBUTES_READ = 'attributes.read',
    ATTRIBUTES_UPDATE = 'attributes.update',
    ATTRIBUTES_DELETE = 'attributes.delete',

    // Payment Methods
    PAYMENT_METHOD_WRITE = 'payment.method.write',
    PAYMENT_METHOD_READ = 'payment.method.read',
    PAYMENT_METHOD_UPDATE = 'payment.method.update',
    PAYMENT_METHOD_DELETE = 'payment.method.delete',

    // Authentication
    AUTH_USER_CREATE = 'auth.user.create',
    AUTH_USER_READ = 'auth.user.read',
    AUTH_USER_UPDATE = 'auth.user.update',
    AUTH_USER_DELETE = 'auth.user.delete',

    // Files
    FILE_UPLOAD = 'file.upload',
    FILE_READ = 'file.read',
    FILE_DELETE = 'file.delete',

    // Roles
    ROLE_WRITE = 'role.write',
    ROLE_READ = 'role.read',
    ROLE_UPDATE = 'role.update',
    ROLE_DELETE = 'role.delete',

    // Policies
    POLICY_READ = 'policy.read',
    POLICY_ASSIGN = 'policy.assign',

    // Transactions
    TRANSACTION_WRITE = 'transaction.write',
    TRANSACTION_READ = 'transaction.read',

    // Membership
    MEMBERSHIP_WRITE = 'membership.write',
    MEMBERSHIP_READ = 'membership.read',
    MEMBERSHIP_UPDATE = 'membership.update',
    MEMBERSHIP_DELETE = 'membership.delete',

    // Merchandise
    MERCHANDISE_WRITE = 'merchandise.write',
    MERCHANDISE_READ = 'merchandise.read',
    MERCHANDISE_UPDATE = 'merchandise.update',
    MERCHANDISE_DELETE = 'merchandise.delete',

    // Announcements
    ANNOUNCEMENT_WRITE = 'announcement.write',
    ANNOUNCEMENT_READ = 'announcement.read',
    ANNOUNCEMENT_UPDATE = 'announcement.update',
    ANNOUNCEMENT_DELETE = 'announcement.delete',

    // Pay Rate
    PAY_RATE_WRITE = 'pay.rate.write',
    PAY_RATE_READ = 'pay.rate.read',
    PAY_RATE_UPDATE = 'pay.rate.update',
    PAY_RATE_DELETE = 'pay.rate.delete',

    // Promotions
    PROMOTION_WRITE = 'promotion.write',
    PROMOTION_READ = 'promotion.read',
    PROMOTION_UPDATE = 'promotion.update',
    PROMOTION_DELETE = 'promotion.delete',
    PROMOTION_APPLY = 'promotion.apply',

    // Feature tabs
    FEATURE_WRITE = 'feature.write',
    FEATURE_READ = 'feature.read',
    FEATURE_UPDATE = 'feature.update',
    FEATURE_DELETE = 'feature.delete',

    // Organization
    ORGANIZATION_SERVICE_WRITE = 'organization.service.write',
    ORGANIZATION_SERVICE_READ = 'organization.service.read',
    ORGANIZATION_SERVICE_UPDATE = 'organization.service.update',
    ORGANIZATION_SERVICE_DELETE = 'organization.service.delete',


}
