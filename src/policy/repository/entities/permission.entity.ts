import { Types } from 'mongoose';
import { DatabaseEntityBase } from 'src/common/database/bases/database.entity';
import {
    DatabaseEntity,
    DatabaseProp,
    DatabaseSchema,
} from 'src/common/database/decorators/database.decorator';
import { IDatabaseDocument } from 'src/common/database/interfaces/database.interface';
import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';

export const PermissionTableName = 'permissions';

@DatabaseEntity({ collection: PermissionTableName })
export class PermissionEntity extends DatabaseEntityBase {
    @DatabaseProp({
        required: true,
        index: true,
        unique: true,
        trim: true,
        maxlength: 30,
        type: String,
    })
    name: string;

    @DatabaseProp({
        required: false,
        trim: true,
        type: String,
    })
    description?: string;

    @DatabaseProp({
        required: true,
        default: true,
        index: true,
        type: Boolean,
    })
    isActive: boolean;

    @DatabaseProp({
        required: true,
        index: true,
        unique: true,
        enum: ENUM_PERMISSION_TYPE,
    })
    type: ENUM_PERMISSION_TYPE;

    @DatabaseProp({
        required: true,
        type: Boolean,
        default: false,
    })
    isDelegated: boolean;

}

export const PermissionSchema = DatabaseSchema(PermissionEntity);
export type PermissionDocument = IDatabaseDocument<PermissionEntity>;
