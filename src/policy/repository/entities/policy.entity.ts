import { Types } from 'mongoose';
import { DatabaseEntityBase } from 'src/common/database/bases/database.entity';
import {
    DatabaseEntity,
    DatabaseProp,
    DatabaseSchema,
} from 'src/common/database/decorators/database.decorator';
import { IDatabaseDocument } from 'src/common/database/interfaces/database.interface';
import { PermissionEntity } from './permission.entity';
import { ENUM_POLICY_ACTION } from 'src/policy/enums/policy.enum';
import slugify from 'slugify';

export const PolicyTableName = 'policies';

@DatabaseEntity({ collection: PolicyTableName })
export class PolicyEntity extends DatabaseEntityBase {

    @DatabaseProp({
        required: true,
        index: true,
        trim: true,
        maxlength: 120,
        type: String,
    })
    subject: string;

    @DatabaseProp({
        required: true,
        trim: true,
        index: true,
        maxlength: 120,
        type: String,
    })
    module: string;

    @DatabaseProp({
        required: true,
        trim: true,
        index: true,
        maxlength: 120,
        enum: ENUM_POLICY_ACTION,
    })
    action: ENUM_POLICY_ACTION;

    @DatabaseProp({
        required: false,
        trim: true,
        type: String,
    })
    description?: string;

    @DatabaseProp({
        required: false,
        default: true,
        index: true,
        type: Boolean,
    })
    isActive: boolean;

    @DatabaseProp({
        required: true,
        index: true,
        type: String,
    })
    type: string;

    @DatabaseProp({
        required: false,
        default: [],
        ref: PermissionEntity.name,
        type: [Types.ObjectId],
    })
    permissions?: Types.ObjectId[];

    // @DatabaseProp({
    //     required: false,
    //     default: [],
    //     ref: PermissionEntity.name,
    //     type: [Types.ObjectId],
    // })
    // restrictedPermissions?: Types.ObjectId[];

    @DatabaseProp({
        required: false,
        type: Boolean,
        default: false,
    })
    isDelegated?: boolean;
}

export const PolicySchema = DatabaseSchema(PolicyEntity);
export type PolicyDocument = IDatabaseDocument<PolicyEntity>;


PolicySchema.index({ type: 1, action: 1 }, { unique: true });


const generatePolicyType = (type: string, module: string, subject: string): [string, boolean] => {
    const expectedType = slugify(`${module}-${subject}`, {
        lower: true,        // convert to lower case
        strict: true,       // strip special characters except replacement
        trim: true         // trim leading and trailing replacement chars
    });
    return [expectedType, expectedType === type];
};

PolicySchema.pre('save', function (next) {
    if (this.isModified('subject') || this.isModified('module')) {
        const [type, isValidType] = generatePolicyType(this.type, this.module, this.subject);
        // if (!isValidType) {
        //     next(new Error(`Invalid policy type ${this.type}. Expected type is ${type}`));
        // }
    }
    next();
});

PolicySchema.pre('findOneAndUpdate', async function (next) {
    const update = this.getUpdate() as { $set?: { subject?: string; module?: string, type?: string } };
    if (update.$set?.subject || update.$set?.module || update.$set?.type) {
        const doc = await this.findOne();
        const [type, isValidType] = generatePolicyType(
            update.$set?.type || doc.type, update.$set?.module || doc.module, update.$set?.subject || doc.subject);
        if (!isValidType) {
            next(new Error(`Invalid policy type ${doc.type}. Expected type is ${type}`));
        }
    }
    next();
});
