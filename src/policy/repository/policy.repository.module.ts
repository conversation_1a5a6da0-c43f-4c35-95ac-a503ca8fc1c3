import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { DATABASE_PRIMARY_CONNECTION_NAME } from 'src/common/database/constants/database.constant';
import { PolicyRepository } from 'src/policy/repository/repositories/policy.repository';
import { PermissionRepository } from 'src/policy/repository/repositories/permission.repository';
import { PolicySchema, PolicyEntity } from 'src/policy/repository/entities/policy.entity';
import { PermissionSchema, PermissionEntity } from 'src/policy/repository/entities/permission.entity';
import { PolicyController } from '../controllers/policy.controller';

@Module({
    controllers: [PolicyController],
    providers: [PolicyRepository, PermissionRepository],
    exports: [PolicyRepository, PermissionRepository],
    imports: [
        MongooseModule.forFeature(
            [
                {
                    name: PolicyEntity.name,
                    schema: PolicySchema,
                },
                {
                    name: PermissionEntity.name,
                    schema: PermissionSchema,
                },
            ],
            DATABASE_PRIMARY_CONNECTION_NAME
        ),
    ],
})
export class PolicyRepositoryModule { }
