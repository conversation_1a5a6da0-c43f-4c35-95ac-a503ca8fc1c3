import { CanActivate, ExecutionContext, Injectable, ForbiddenException } from '@nestjs/common';
import { IRequestApp } from 'src/common/request/interfaces/request.interface';
import { ENUM_SESSION_STATUS_CODE_ERROR } from 'src/session/enums/session.status-code.enum';

@Injectable()
export class UseDelegatedUserGuard implements CanActivate {
    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest<IRequestApp>();
        
        // Check if delegated user exists
        if (!request.__delegateUser) {
            throw new ForbiddenException({
                statusCode: ENUM_SESSION_STATUS_CODE_ERROR.SESSION_EXPIRED,
                message: 'session.error.delegatedUserNotFound',
            });
        }

        // Set delegated user as the active user
        request.__user = request.__delegateUser;
        
        return true;
    }
}