import { Injectable, OnApplicationBootstrap } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { WaitTimeGatewayService } from '../gateway/wait-time-gateway.service';
import { Scheduling } from 'src/scheduling/schemas/scheduling.schema';
import moment from 'moment';
import { ScheduleStatusType } from 'src/scheduling/enums/schedule-status.enum';
import cron from 'node-cron';

// @Injectable()
// export class WaitTimeService implements OnApplicationBootstrap {
//     constructor(
//         @InjectModel(Scheduling.name) private SchedulingModel: Model<Scheduling>,
//         private waitTimeGatewayService: WaitTimeGatewayService
//     ) { }

//     async onApplicationBootstrap() {
//         const startOfDay = moment().startOf('day').toDate();
//         const endOfDay = moment().endOf('day').toDate();
//         const currentISTTime = moment().tz("Asia/Kolkata").format("HH:mm");

//         const upcomingBookings = await this.SchedulingModel.find({
//             $and: [
//                 { date: { $gte: startOfDay, $lte: endOfDay } },
//                 { to: { $gt: currentISTTime } },
//                 {from: { $lte: currentISTTime }}, 
//                 { scheduleStatus: { $in: [ScheduleStatusType.BOOKED, ScheduleStatusType.CHECKEDIN] } }
//             ]

//         }, { from: 1, to: 1, facilityId: 1, date: 1 }).sort("from");
//         upcomingBookings.forEach(booking => {
//             this.scheduleWebSocketUpdate(booking.date, booking.from,booking.facilityId);
//         });
//     }

//     scheduleWebSocketUpdate(startDate: Date, startTime: string,facilityId: string) {
//         console.log(startDate, startTime, facilityId)
//         let datePart = moment(startDate).format("YYYY-MM-DD");
//         datePart = datePart.split("T")[0];
//         const combinedDateTimeString = `${datePart}T${startTime}:00.000+05:30`;    //conver It in UTC format
//         const combinedDateTime = new Date(combinedDateTimeString);
//         if (isNaN(combinedDateTime.getTime())) {
//             console.error("Invalid combinedDateTime:", combinedDateTimeString);
//             return;
//         } 

//         const delay = combinedDateTime.getTime() - Date.now();
//         // console.log("Delay (ms):", delay);
//         let delaySeconds = Math.abs(Math.floor(delay / 1000)); // Convert ms to seconds
//         let delayMinutes = Math.abs(Math.floor(delay / 60000)); // Convert ms to minutes
//         let delayHours = Math.floor(delayMinutes / 60);
//         let remainingMinutes = delayMinutes % 60;
//         let remainingSeconds = delaySeconds % 60;
    
//         // Format the delay time in HH:mm format
//         let formattedTime = `${String(delayHours).padStart(2, "0")}:${String(remainingMinutes).padStart(2, "0")}:${String(remainingSeconds).padStart(2, "0")}`;
//         let status = delay > 0 ? "after" : "before";
    
//         // Convert combinedDateTime to IST format
//         let scheduledISTTime = combinedDateTime.toLocaleTimeString("en-IN", { 
//             timeZone: "Asia/Kolkata", 
//             hour: "2-digit", 
//             minute: "2-digit", 
//             second: "2-digit" 
//         });
    
//         console.log(`Delay Time: ${formattedTime} ${status} | Scheduled IST Time: ${scheduledISTTime}`);
//         console.log("Delay Type:", typeof delay);

// console.log(typeof delay)
//         setTimeout(() => {
//             this.waitTimeGatewayService.sendWaitingTimeUpdate(facilityId);
//         }, delay>=0?delay+150:0);
//     }
// }



@Injectable()
export class WaitTimeService implements OnApplicationBootstrap {
    constructor(
        @InjectModel(Scheduling.name) private SchedulingModel: Model<Scheduling>,
        private waitTimeGatewayService: WaitTimeGatewayService
    ) { }

    async onApplicationBootstrap() {
        console.log("Scheduling WebSocket updates...");
        
        cron.schedule("* * * * *", async () => {  // Runs every minute
            const currentISTTime = moment().tz("Asia/Kolkata").format("HH:mm");
            const startOfDay = moment().startOf("day").toDate();
            const endOfDay = moment().endOf("day").toDate();
            // console.log(startOfDay)
            // console.log(endOfDay)

            //console.time("Query Execution Time"); // Start timer
            const distinctFacilityIds = await this.SchedulingModel.distinct("facilityId", {
                $and: [
                    { date: { $gte: startOfDay, $lte: endOfDay } },
                    { to: { $gt: currentISTTime } },
                    { from: { $lte: currentISTTime } },
                    { scheduleStatus: { $in: [ScheduleStatusType.BOOKED, ScheduleStatusType.CHECKEDIN] } }
                ]
            });
            // console.timeEnd("Query Execution Time"); // End timer and log execution time
            // console.log(JSON.stringify({
            //     $and: [
            //         { date: { $gte: startOfDay, $lte: endOfDay } },
            //         { to: { $gt: currentISTTime } },
            //         { from: { $lte: currentISTTime } },
            //         { scheduleStatus: { $in: [ScheduleStatusType.BOOKED, ScheduleStatusType.CHECKEDIN] } }
            //     ]
            // }))
            if(distinctFacilityIds?.length>0){
            distinctFacilityIds.forEach(id => {
                this.waitTimeGatewayService.sendWaitingTimeUpdate(id);
                console.log(`WebSocket Update Sent for Facility ${id} at ${moment().format("HH:mm:ss")}`);
            });
        }
        });
    

        console.log("WebSocket scheduler started!");
    }
}