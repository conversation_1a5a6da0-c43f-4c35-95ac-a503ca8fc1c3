import { forwardRef, Module } from '@nestjs/common';
import { WaitTimeService } from './services/wait-time.service';
import { JwtModule } from "@nestjs/jwt";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { AuthModule } from 'src/auth/auth.module';
import { WaitTimeController } from './wait-time.controller';
import { WaitTimeGatewayService } from './gateway/wait-time-gateway.service';
import { SocketAuthMiddlewareService } from './middleware/socket-auth-middleware.service';
import { MongooseModule } from '@nestjs/mongoose';
import { User, UserSchema } from 'src/users/schemas/user.schema';
import { RoomModule } from 'src/room/room.module';
import { Facility, FacilitySchema } from 'src/facility/schemas/facility.schema';
import { Scheduling, SchedulingSchema } from 'src/scheduling/schemas/scheduling.schema';
import { DATABASE_PRIMARY_CONNECTION_NAME } from 'src/common/database/constants/database.constant';

@Module({
    imports: [
        forwardRef(() => AuthModule),
        forwardRef(() => RoomModule),  
        JwtModule.registerAsync({
            imports: [ConfigModule],
            useFactory: async (configService: ConfigService) => ({
                global: true,
                secret: configService.get<string>("JWT_SECRET"),
                signOptions: { expiresIn: "24h" },
            }),
            inject: [ConfigService],
        }),
     MongooseModule.forFeature([
            { name: User.name, schema: UserSchema },
            { name: Facility.name, schema: FacilitySchema },
            { name: Scheduling.name, schema: SchedulingSchema },
            
          ], DATABASE_PRIMARY_CONNECTION_NAME)],
    providers: [WaitTimeService, WaitTimeGatewayService, SocketAuthMiddlewareService],
    controllers: [WaitTimeController],
    exports:[WaitTimeGatewayService,SocketAuthMiddlewareService,WaitTimeService]
})
export class WaitTimeModule { }
