
import { <PERSON><PERSON><PERSON>_POLICY_ACTION, ENUM_POLICY_MODULE, ENUM_POLICY_SUBJECT_NAME, ENUM_POLICY_TYPE } from 'src/policy/enums/policy.enum';
import { IDefaultPolicies } from 'src/policy/interfaces/policy.interface';

export const settingPolicies: IDefaultPolicies[] = [
    // Room
    // {
    //     subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.SETTING_ROOM],
    //     description: 'Grant view-only access to room',
    //     isActive: true,
    //     module: ENUM_POLICY_MODULE.SETTINGS,
    //     type: ENUM_POLICY_TYPE.SETTING_ROOM,
    //     action: ENUM_POLICY_ACTION.VIEW,
    // },
    // {
    //     subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.SETTING_ROOM],
    //     description: 'Grant full access to room',
    //     isActive: true,
    //     module: <PERSON><PERSON>M_POLICY_MODULE.SETTINGS,
    //     type: <PERSON><PERSON><PERSON>_POLICY_TYPE.SETTING_ROOM,
    //     action: ENUM_POLICY_ACTION.FULL_ACCESS,
    // },

    // // Announcement
    // {
    //     subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.SETTING_ANNOUNCEMENT_ANNOUNCEMENT],
    //     description: 'Grant view-only access to announcement',
    //     isActive: true,
    //     module: ENUM_POLICY_MODULE.SETTINGS,
    //     type: ENUM_POLICY_TYPE.SETTING_ANNOUNCEMENT_ANNOUNCEMENT,
    //     action: ENUM_POLICY_ACTION.VIEW,
    // },
    // {
    //     subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.SETTING_ANNOUNCEMENT_ANNOUNCEMENT],
    //     description: 'Grant full access to announcement',
    //     isActive: true,
    //     module: ENUM_POLICY_MODULE.SETTINGS,
    //     type: ENUM_POLICY_TYPE.SETTING_ANNOUNCEMENT_ANNOUNCEMENT,
    //     action: ENUM_POLICY_ACTION.FULL_ACCESS,
    // },

    // // Feature tabs
    // {
    //     subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.SETTING_FEATURE_TABS],
    //     description: 'Grant view-only access to feature tabs',
    //     isActive: true,
    //     module: ENUM_POLICY_MODULE.SETTINGS,
    //     type: ENUM_POLICY_TYPE.SETTING_FEATURE_TABS,
    //     action: ENUM_POLICY_ACTION.VIEW,
    // },
    // {
    //     subject: ENUM_POLICY_SUBJECT_NAME[ENUM_POLICY_TYPE.SETTING_FEATURE_TABS],
    //     description: 'Grant full access to feature tabs',
    //     isActive: true,
    //     module: ENUM_POLICY_MODULE.SETTINGS,
    //     type: ENUM_POLICY_TYPE.SETTING_FEATURE_TABS,
    //     action: ENUM_POLICY_ACTION.FULL_ACCESS,
    // },

];