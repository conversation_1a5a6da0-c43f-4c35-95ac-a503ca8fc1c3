import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { PermissionCreateRequestDto } from 'src/policy/dtos/request/permission.create.request.dto';

export const purchasePermissions: PermissionCreateRequestDto[] = [
    {
        name: "Purchase Write",
        type: ENUM_PERMISSION_TYPE.PURCHASE_WRITE,
        description: 'Grant purchase write access',
        isDelegated: true,
    },
    {
        name: "Purchase Read",
        type: ENUM_PERMISSION_TYPE.PURCHASE_READ,
        description: 'Grant purchase read access',
        isDelegated: true,
    },
    // {
    //     name: "Purchase Update",
    //     type: ENUM_PERMISSION_TYPE.PURCHASE_UPDATE,
    //     description: 'Grant purchase update access',
    //     isDelegated: true,
    // },
    {
        name: "Purchase Export",
        type: ENUM_PERMISSION_TYPE.PURCHASE_EXPORT,
        description: 'Grant purchase export access',
        isDelegated: true,
    },
    {
        name: "Purchase Report Export",
        type: ENUM_PERMISSION_TYPE.PURCHASE_REPORT_EXPORT,
        description: 'Grant purchase report export access',
        isDelegated: true,
    },
    {
        name: "Purchase Invoice Read",
        type: ENUM_PERMISSION_TYPE.PURCHASE_INVOICE_READ,
        description: 'Grant purchase invoice read access',
        isDelegated: true,
    },
    {
        name: "Purchase Invoice Write",
        type: ENUM_PERMISSION_TYPE.PURCHASE_INVOICE_WRITE,
        description: 'Grant purchase invoice write access',
        isDelegated: true,
    },
    {
        name: "Purchase Invoice Payment",
        type: ENUM_PERMISSION_TYPE.PURCHASE_INVOICE_PAYMENT,
        description: "Grant purchase invoice payment access",
        isDelegated: true,
    },
    {
        name: "Purchase update start date",
        type: ENUM_PERMISSION_TYPE.PURCHASE_PACKAGE_UPDATE_START,
        description: "Grant purchase update start date access",
        isDelegated: true,
    },
    {
        name: "Purchase update session",
        type: ENUM_PERMISSION_TYPE.PURCHASE_PACKAGE_UPDATE_SESSION,
        description: "Grant purchase update session  access",
        isDelegated: true,
    },
];

