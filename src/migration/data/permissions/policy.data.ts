
import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { PermissionCreateRequestDto } from 'src/policy/dtos/request/permission.create.request.dto';

export const policyPermissions: PermissionCreateRequestDto[] = [
    {
        name: "Policy Read",
        type: ENUM_PERMISSION_TYPE.POLICY_READ,
        description: 'Grant policy read access',
        isDelegated: true,
    },
    {
        name: "Policy Assign",
        type: ENUM_PERMISSION_TYPE.POLICY_ASSIGN,
        description: 'Grant policy assign access',
        isDelegated: true,
    },
];