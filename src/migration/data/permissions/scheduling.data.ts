import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { PermissionCreateRequestDto } from 'src/policy/dtos/request/permission.create.request.dto';

export const schedulingPermissions: PermissionCreateRequestDto[] = [
    {
        name: "Scheduling Session Booking",
        type: ENUM_PERMISSION_TYPE.SCHEDULING_SCHEDULE_BOOKING,
        description: 'Grant session booking access',
        isDelegated: false,
    },
    {
        name: "Scheduling Session Update",
        type: ENUM_PERMISSION_TYPE.SCHEDULING_UPDATE_BOOKING,
        description: 'Grant session update access',
        isDelegated: false,
    },
    {
        name: "Scheduling Personal Appointment",
        type: ENUM_PERMISSION_TYPE.SCHEDULING_SCHEDULE_PERSONAL_APPOINTMENT,
        description: 'Grant personal appointment access',
        isDelegated: false,
    },
    {
        name: "Scheduling Personal Appointment Update",
        type: ENUM_PERMISSION_TYPE.SCHEDULING_UPDATE_PERSONAL_APPOINTMENT,
        description: 'Grant personal appointment update access',
        isDelegated: false,
    },
    {
        name: "Scheduling Class",
        type: ENUM_PERMISSION_TYPE.SCHEDULING_SCHEDULE_CLASS,
        description: 'Grant class access',
        isDelegated: false,
    },
    {
        name: "Scheduling Class Update",
        type: ENUM_PERMISSION_TYPE.SCHEDULING_UPDATE_CLASS,
        description: 'Grant class update access',
        isDelegated: false,
    },
    {
        name: "Scheduling Course",
        type: ENUM_PERMISSION_TYPE.SCHEDULING_SCHEDULE_COURSE,
        description: 'Grant course access',
        isDelegated: false,
    },
    {
        name: "Scheduling Course Update",
        type: ENUM_PERMISSION_TYPE.SCHEDULING_UPDATE_COURSE,
        description: 'Grant course update access',
        isDelegated: false,
    },
    {
        name: "Scheduling Cancel",
        type: ENUM_PERMISSION_TYPE.SCHEDULING_CANCEL,
        description: 'Grant scheduling cancel access',
        isDelegated: false,
    },
    {
        name: "Scheduling Checkin",
        type: ENUM_PERMISSION_TYPE.SCHEDULING_CHECKIN,
        description: 'Grant scheduling checkin access',
        isDelegated: false,
    },
    {
        name: "Scheduling Read",
        type: ENUM_PERMISSION_TYPE.SCHEDULING_SCHEDULE_READ,
        description: 'Grant scheduling read access',
        isDelegated: false,
    },
    {
        name: "Scheduling Availability",
        type: ENUM_PERMISSION_TYPE.SCHEDULING_AVAILABILITY,
        description: 'Grant scheduling availability access',
        isDelegated: false,
    },
    {
        name: "Scheduling Enroll",
        type: ENUM_PERMISSION_TYPE.SCHEDULING_ENROLL,
        description: 'Grant scheduling enroll access and cancel enroll access',
        isDelegated: false,
    },

];

