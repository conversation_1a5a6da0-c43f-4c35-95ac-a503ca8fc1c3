import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { PermissionCreateRequestDto } from 'src/policy/dtos/request/permission.create.request.dto';

export const clientsPermissions: PermissionCreateRequestDto[] = [
    {
        name: "Clients Write",
        type: ENUM_PERMISSION_TYPE.CLIENTS_WRITE,
        description: 'Grant clients write access',
        isDelegated: true,
    },
    {
        name: "Clients Read",
        type: ENUM_PERMISSION_TYPE.CLIENTS_READ,
        description: 'Grant clients read access',
        isDelegated: true,
    },
    {
        name: "Clients Update",
        type: ENUM_PERMISSION_TYPE.CLIENTS_UPDATE,
        description: 'Grant clients update access',
        isDelegated: true,
    },
    {
        name: "Clients Delete",
        type: ENUM_PERMISSION_TYPE.CLIENTS_DELETE,
        description: 'Grant clients delete access',
        isDelegated: true,
    },
];

