import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { PermissionCreateRequestDto } from 'src/policy/dtos/request/permission.create.request.dto';

export const announcementPermissions: PermissionCreateRequestDto[] = [
    {
        name: "Announcement Write",
        type: ENUM_PERMISSION_TYPE.ANNOUNCEMENT_WRITE,
        description: 'Grant announcement write access',
        isDelegated: true,
    },
    {
        name: "Announcement Read",
        type: ENUM_PERMISSION_TYPE.ANNOUNCEMENT_READ,
        description: 'Grant announcement read access',
        isDelegated: true,
    },
    {
        name: "Announcement Update",
        type: ENUM_PERMISSION_TYPE.ANNOUNCEMENT_UPDATE,
        description: 'Grant announcement update access',
        isDelegated: true,
    },
    {
        name: "Announcement Delete",
        type: ENUM_PERMISSION_TYPE.ANNOUNCEMENT_DELETE,
        description: 'Grant announcement delete access',
        isDelegated: true,
    },
];