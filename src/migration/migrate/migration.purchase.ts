import { Command } from 'nestjs-command';
import { Injectable } from '@nestjs/common';
import {
    ENUM_ROLE_TYPE,
} from 'src/role/enums/role.enum';
import { RoleService } from 'src/role/services/role.service';
import { Purchase, PurchaseDocument } from 'src/users/schemas/purchased-packages.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ENUM_PRODUCT_ITEM_TYPE } from 'src/promotions/enums/item-type.enum';
import { Pricing } from 'src/organization/schemas/pricing.schema';

@Injectable()
export class MigrationItemType {
    constructor(
        @InjectModel(Purchase.name) private PurchaseModel: Model<PurchaseDocument>,
        @InjectModel(Pricing.name) private PricingModel: Model<Pricing>,
    ) { }

    @Command({
        command: 'pricing:inclusiveofgst',
        describe: 'Add consumers to purchase',
    })
    async seeds(): Promise<void> {

        try {
            const pricings = await this.PricingModel.find({
                $and: [
                    { isInclusiveofGst: { $exists: true } },
                    { itemType: ENUM_PRODUCT_ITEM_TYPE.SERVICE }
                ]
            });

            const promise = await Promise.all(pricings.map(async (pricing: any) => {
                return this.PricingModel.updateOne({ _id: pricing._id }, {
                    $set: {
                        price: pricing.finalPrice,
                    },
                    $unset: { isInclusiveofGst: 1, basePrice: 1, gstAmount: 1, finalPrice: 1 }
                });
            }));
        } catch (err: any) {
            throw new Error(err);
        }

        return;
    }

}
