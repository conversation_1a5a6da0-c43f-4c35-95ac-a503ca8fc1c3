import { Module } from '@nestjs/common';
import { MongooseModule } from "@nestjs/mongoose";
import { widgetController } from './controller/widget.controller';
import { widgetService } from './service/widget.service';
import { Services, ServiceSchema } from 'src/organization/schemas/services.schema';
import { StaffProfileDetails, StaffSchema } from 'src/staff/schemas/staff.schema';
import { PayRate, PayRateSchema } from 'src/staff/schemas/pay-rate.schema';
import { StaffAvailability, StaffAvailabilitySchema } from "src/staff/schemas/staff-availability";
import { widgetAuthenticationController } from './controller/widget-auth-controller';
import { AuthModule } from 'src/auth/auth.module';
import { WidgetAuthService } from './service/widget-auth.service';
import { OrganizationSchema, Organizations } from "src/organization/schemas/organization.schema";
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { User, UserSchema } from 'src/users/schemas/user.schema';
import { Clients, ClientSchema } from 'src/users/schemas/clients.schema';
import { Facility, FacilitySchema } from 'src/facility/schemas/facility.schema';
import { UtilsModule } from 'src/utils/utils.module';
import { MailModule } from 'src/mail/mail.module';
import { RoleModule } from 'src/role/role.module';
import { UsersModule } from 'src/users/users.module';
import { SessionModule } from 'src/session/session.module';
import { WidgetPaymentController } from './controller/widget-payment-controller';
import { WidgetPaymentService } from './service/widget-payment.service';
import { PaymentCredential, PaymentCredentialSchema } from 'src/payment/schema/paymentCredential.schema';
import { PaymentModule } from 'src/payment/payment.module';
import { Scheduling, SchedulingSchema } from 'src/scheduling/schemas/scheduling.schema';
import { Pricing, PricingSchema } from 'src/organization/schemas/pricing.schema';
import { Purchase, PurchaseSchema } from "src/users/schemas/purchased-packages.schema";
import { Room, RoomSchema } from "src/room/schema/room.schema";
import { SchedulingModule } from 'src/scheduling/scheduling.module';


@Module({
    imports: [
        MongooseModule.forFeature([
            { name: Services.name, schema: ServiceSchema },
            { name: StaffProfileDetails.name, schema: StaffSchema },
            { name: PayRate.name, schema: PayRateSchema },
            { name: StaffAvailability.name, schema: StaffAvailabilitySchema },
            { name: Organizations.name, schema: OrganizationSchema },
            { name: User.name, schema: UserSchema },
            { name: Clients.name, schema: ClientSchema },
            { name: Facility.name, schema: FacilitySchema },
            { name: PaymentCredential.name, schema: PaymentCredentialSchema },
            { name: Scheduling.name, schema: SchedulingSchema },
            { name: Pricing.name, schema: PricingSchema },
            { name: Purchase.name, schema: PurchaseSchema },
            { name: Room.name, schema: RoomSchema }
        ]),
        JwtModule.registerAsync({
            imports: [ConfigModule],
            useFactory: async (configService: ConfigService) => ({
                global: true,
                secret: configService.get<string>("JWT_SECRET"),
                signOptions: { expiresIn: "24h" },
            }),
            inject: [ConfigService],
        }),
        AuthModule,
        UtilsModule,
        MailModule,
        RoleModule,
        UsersModule,
        SessionModule,
        PaymentModule,
        SchedulingModule
    ],
    controllers: [widgetController, widgetAuthenticationController, WidgetPaymentController],
    providers: [widgetService, WidgetAuthService, WidgetPaymentService],
    exports: [widgetService, WidgetAuthService]
})
export class WidgetModule { }


