import { BadRequestException, Body, Controller, Get, HttpCode, HttpException, HttpStatus, Param, Patch, Post, UseGuards } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { widgetService } from "../service/widget.service";

@ApiTags("widget-api")
@ApiBearerAuth()
@Controller("widget")

export class widgetController {
    constructor(private widgetService: widgetService) { }
    @Post("/get-service-type")
    @HttpCode(HttpStatus.OK)
    @ApiOperation({ summary: "Get Service Type" })
    async getServiceType(@Body() body: any) {
        return await this.widgetService.getServiceType(body)
    }

    @Post('get-service-type-option') 
    @HttpCode(HttpStatus.OK)
    @ApiOperation({ summary: "Get Service Type" })
    async getServiceOption(@Body() body: any) {
        return await this.widgetService.getServiceOptionType(body)
    }
    
    @Post('get-trainer-availablity')
    @HttpCode(HttpStatus.OK)
    @ApiOperation({ summary: "Get Trainer Availablity" })
    async getTrainerAvailablity(@Body() body: any) {
        return await this.widgetService.getTrainerAvailablity(body)
    }

    @Post('get-course-list')
    @HttpCode(HttpStatus.OK)
    @ApiOperation({ summary: "Get Course List" })
    async getCourseList(@Body() body: any) {
        return await this.widgetService.getCourseList(body);
    }

    @Post('get-course-details')
    @HttpCode(HttpStatus.OK)
    @ApiOperation({ summary: "Get Course Detail" })
    async getCourseDetail(@Body() body: any) {
        return await this.widgetService.getCourseDetail(body);
    }

    @Post('get-trainer-time-slots')
    @HttpCode(HttpStatus.OK)
    @ApiOperation({ summary: "Get Trainer Time Slots based on subtype and availability" })
    async getTrainerTimeSlots(@Body() body: any) {
        return await this.widgetService.getTrainerTimeSlots(body);
    }

@Post('schedule-personal-appontment')
@HttpCode(HttpStatus.OK)
@ApiOperation({ summary: "Schedule Personal Appointment" })
async schedulePersonalAppointment(@Body() body: any) {
    return await this.widgetService.schedulePersonalAppointment(body);
}

}

