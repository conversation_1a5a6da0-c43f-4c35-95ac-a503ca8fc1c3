import { Injectable, NotFoundException, InternalServerErrorException, BadRequestException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import { Appointment } from "src/appointment/schema/appointment.schema";
import { Services } from "src/organization/schemas/services.schema";
import { PayRate } from "src/staff/schemas/pay-rate.schema";
import { StaffAvailability } from "src/staff/schemas/staff-availability";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { Facility } from "src/facility/schemas/facility.schema";
import { Pricing } from "src/organization/schemas/pricing.schema";
import { Scheduling } from "src/scheduling/schemas/scheduling.schema";
import { SessionType } from "src/utils/enums/session-type.enum";
import { ScheduleStatusType } from "src/scheduling/enums/schedule-status.enum";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { Purchase, PurchaseDocument } from "src/users/schemas/purchased-packages.schema";
import { Clients } from "src/users/schemas/clients.schema";
import { Room } from "src/room/schema/room.schema";
import { SchedulingService } from "src/scheduling/services/scheduling.service";
import { User } from "src/users/schemas/user.schema";


@Injectable()
export class widgetService {
    constructor(
        @InjectModel(Services.name) private ServiceModel: Model<Services>,
        @InjectModel(StaffProfileDetails.name) private StaffModel: Model<StaffProfileDetails>,
        @InjectModel(StaffAvailability.name) private staffAvailabilityModel: Model<StaffAvailability>,
        @InjectModel(Purchase.name) private readonly purchaseModel: Model<PurchaseDocument>,
        @InjectModel(Clients.name) private ClientModel: Model<Clients>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        @InjectModel(PayRate.name) private PayRateModel: Model<PayRate>,
        @InjectModel('Pricing') private PricingModel: Model<any>,
        @InjectModel(Scheduling.name) private SchedulingModel: Model<Scheduling>,
        @InjectModel(Room.name) private RoomModel: Model<Room>,
        @InjectModel(User.name) private userModel: Model<User>,
        private readonly schedulingService: SchedulingService

    ) { }

    async getServiceType(body: any) {
        try {
            if (!body || Object.keys(body).length === 0) {
                throw new BadRequestException('Invalid request data');
            }
            let query: any = {
                organizationId: "67a5e32e1bbdd61401b5e36c",
            };
            query["classType"] = ["personalAppointment", "bookings"];
            const countProm = this.ServiceModel.countDocuments(query);
            const listProm = this.ServiceModel.find(query).select({ name: 1, classType: 1 })
                .sort({ createdAt: -1 }).lean()
            const [list, count] = await Promise.all([listProm, countProm]);
            const groupedList = await this.groupByClassType(list);
            return groupedList
        }
        catch (error: any) {
            console.error(error)
        }
    }

    async getServiceOptionType(body: any) {
        try {
            if (!body || Object.keys(body).length === 0) {
                throw new BadRequestException('Invalid request data');
            }
            let query: any = {
                organizationId: "67a5e32e1bbdd61401b5e36c",
            };
            query["classType"] = ["personalAppointment", "bookings"];
            if (body.serviceId) {
                query["_id"] = body.serviceId
            }
            const countProm = this.ServiceModel.countDocuments(query);
            const listProm = this.ServiceModel.find(query).select({ appointmentType: 1, classType: 1 })
                .sort({ createdAt: -1 }).lean()
            const [list, count] = await Promise.all([listProm, countProm]);
            const subTypeIds = list.flatMap(service =>
                service.appointmentType?.map((type: any) => type._id)
            )
            const dayStart = new Date();
            dayStart.setUTCHours(0, 0, 0, 0);

            const dayEnd = new Date();
            dayEnd.setUTCHours(23, 59, 59, 999);

            const pipeline = [
                {
                    $match: {
                        date: { $gte: dayStart, $lte: dayEnd },
                        "timeSlots.availabilityStatus": { $ne: "unavailable" },
                    }
                },
                {
                    $addFields: {
                        timeSlots: {
                            $filter: {
                                input: "$timeSlots",
                                as: "slot",
                                cond: {
                                    $and: [
                                        { $ne: ["$$slot.availabilityStatus", "unavailable"] },
                                        { $in: ["$$slot.subTypeId", subTypeIds] },
                                    ]
                                }
                            }
                        }
                    }
                },
                { $match: { timeSlots: { $ne: [] } } },
                {
                    $lookup: {
                        from: "users",
                        localField: "userId",
                        foreignField: "_id",
                        as: "user",
                        pipeline: [{ $project: { firstName: 1, lastName: 1 } }]
                    }
                },
                { $unwind: { path: "$user", preserveNullAndEmptyArrays: true } },
                {
                    $project: {
                        userId: 1,
                        facilityId: 1,
                        date: 1,
                        timeSlots: 1,
                        userName: { $concat: ["$user.firstName", " ", "$user.lastName"] },
                    }
                }
            ];

            const staffAvailabilities = await this.staffAvailabilityModel.aggregate(pipeline);
            // Step 3: Extract payRateIds
            const payrateIds = [
                ...new Set(
                    staffAvailabilities.flatMap(doc =>
                        doc.timeSlots.flatMap(slot => slot.payRateIds || [])
                    )
                )
            ];

            const payrateDetails = await this.PayRateModel.find({
                _id: { $in: payrateIds },
                serviceCategory: body.serviceId,
                appointmentType: { $in: subTypeIds }
            });
            return payrateDetails
        }
        catch (error: any) {
            console.error(error)
        }
    }
    private async groupByClassType(list) {
        const result = {};

        list.forEach(item => {
            const { classType, ...rest } = item;

            if (!result[classType]) {
                result[classType] = new Set();
            }

            result[classType].add(JSON.stringify(rest)); // Convert to string for Set uniqueness
        });

        // Convert Sets of JSON strings back to arrays of objects
        Object.keys(result).forEach(key => {
            result[key] = Array.from(result[key]).map((str: any) => JSON.parse(str));
        });

        return result;
    }

//     async getTrainerAvailablity(body: any) {
//         try {
//             const organizationId = body.organizationId || "678505b84d8168563536ed5a";
//             const date = body.date ? new Date(body.date) : new Date();
//             const serviceTypes = body.serviceTypes || ["personalAppointment"]; // Default to these types if not specified


//             const dayStart = new Date(date);
//             dayStart.setUTCHours(0, 0, 0, 0);

//             const dayEnd = new Date(date);
//             dayEnd.setUTCHours(23, 59, 59, 999);

//             // Get all trainers from the organization
//             const trainers = await this.StaffModel.find(
//                 { organizationId: new Types.ObjectId(organizationId) },
//                 { userId: 1, facilityId: 1 }
//             ).lean();


//             if (!trainers.length) {
//                 return { trainers: [] };
//             }

//             const trainerIds = trainers.map(trainer => trainer.userId);

//             // Find all availability records for trainers
//             const allAvailabilities = await this.staffAvailabilityModel.find({
//                 organizationId: new Types.ObjectId(organizationId),
//                 userId: { $in: trainerIds },
//                 date: { $gte: dayStart, $lte: dayEnd }
//             }).lean();


//             // Group availabilities by userId
//             const availabilityByTrainer = {};
//             allAvailabilities.forEach(record => {
//                 if (!availabilityByTrainer[record.userId.toString()]) {
//                     availabilityByTrainer[record.userId.toString()] = record;
//                 } else {
//                     // Merge time slots if multiple records exist for same trainer
//                     availabilityByTrainer[record.userId.toString()].timeSlots = [
//                         ...availabilityByTrainer[record.userId.toString()].timeSlots,
//                         ...record.timeSlots
//                     ];
//                 }
//             });

//             // Get all facility IDs from trainers
//             const facilityIds = [...new Set(trainers.map(trainer => trainer.facilityId))];

//             // Get facility details
//             const facilities = await this.FacilityModel.find(
//                 { _id: { $in: facilityIds } },
//                 { facilityName: 1 }
//             ).lean();

//             // Create facility map
//             const facilityMap = {};
//             facilities.forEach(facility => {
//                 facilityMap[facility._id.toString()] = facility.facilityName;
//             });

//             // Get user details for all trainers including profile pictures
//             const userIds = Object.keys(availabilityByTrainer).map(id => new Types.ObjectId(id));
//             const users = await this.StaffModel.aggregate([
//                 {
//                     $match: { userId: { $in: userIds } }
//                 },
//                 {
//                     $lookup: {
//                         from: "users",
//                         localField: "userId",
//                         foreignField: "_id",
//                         as: "user",
//                         pipeline: [{ $project: { firstName: 1, lastName: 1 } }]
//                     }
//                 },
//                 { $unwind: { path: "$user", preserveNullAndEmptyArrays: true } },
//                 {
//                     $lookup: {
//                         from: "staffprofiledetails",
//                         localField: "userId",
//                         foreignField: "userId",
//                         as: "staffProfile",
//                         pipeline: [{ $project: { profilePicture: 1 } }]
//                     }
//                 },
//                 { $unwind: { path: "$staffProfile", preserveNullAndEmptyArrays: true } },
//                 {
//                     $project: {
//                         userId: 1,
//                         facilityId: 1,
//                         userName: { $concat: ["$user.firstName", " ", "$user.lastName"] },
//                         profilePicture: "$staffProfile.profilePicture"
//                     }
//                 }
//             ]);

//             // Create a map of user details
//             const userMap = {};
//             users.forEach(user => {
//                 userMap[user.userId.toString()] = user;
//             });

//             // Extract all payRateIds from available slots
//             const payrateIds = [
//                 ...new Set(
//                     Object.values(availabilityByTrainer).flatMap((record: any) =>
//                         record.timeSlots
//                             .filter(slot => slot.availabilityStatus !== "unavailable")
//                             .flatMap(slot => slot.payRateIds || [])
//                     )
//                 )
//             ];


//             // Get payrate details
//             const payrateDetails = await this.PayRateModel.find({
//                 _id: { $in: payrateIds }
//             }).lean();


//             // Get service categories and subtypes
//             const serviceCategoryIds = [...new Set(payrateDetails.map(pr => pr.serviceCategory))];

//             const serviceCategories = await this.ServiceModel.find({
//                 _id: { $in: serviceCategoryIds }
//             }).lean();

// console.log(serviceCategories)
//             // Get pricing information for these service categories and subtypes
//             const pricingQuery = {
//                 organizationId: new Types.ObjectId(organizationId),
//                 isActive: true,
//                 isSellOnline: true,
//                 $or: [
//                     // Direct service category match
//                     {
//                         "services.serviceCategory": { $in: serviceCategoryIds.map(id => new Types.ObjectId(id)) }
//                     },
//                     // Match through relationship
//                     {
//                         "services.relationShip.serviceCategory": { $in: serviceCategoryIds.map(id => new Types.ObjectId(id)) }
//                     }
//                 ]
//             };

//             const pricingList = await this.PricingModel.find(pricingQuery).lean();

//             // Log sample pricing data


//             // Create pricing maps for easy lookup
//             const pricingByServiceCategory = {};
//             const pricingBySubtype = {};

//             // Extract all appointment type IDs from service categories
//             const allSubtypeIds = serviceCategories.flatMap(category =>
//                 (category.appointmentType || []).map((subtype: any) => subtype._id.toString())
//             );

//             // Initialize empty arrays for all service categories and subtypes
//             serviceCategoryIds.forEach(id => {
//                 pricingByServiceCategory[id.toString()] = [];
//             });

//             allSubtypeIds.forEach(id => {
//                 pricingBySubtype[id] = [];
//             });

//             // Process each pricing option
//             pricingList.forEach(pricing => {
//                 if (!pricing.services) {
//                     return;
//                 }
//                 // Include all pricing parameters
//                 const pricingInfo = {
//                     _id: pricing._id,
//                     name: pricing.name,
//                     price: pricing.price,
//                     tax: pricing.tax || 0,
//                     sessionType: pricing.services.sessionType,
//                     sessionCount: pricing.services.sessionCount,
//                     expiredInDays: pricing.expiredInDays || 0,
//                     durationUnit: pricing.durationUnit || 'days',
//                     discount: pricing.discount || null,
//                     isTrialPricing: pricing.isTrialPricing || false,
//                     isSellOnline: pricing.isSellOnline || false,
//                     isActive: pricing.isActive || false,
//                     description: pricing.description || '',
//                     hsnOrSacCode: pricing.hsnOrSacCode || '',
//                     membershipId: pricing.membershipId || null,
//                     isBundledPricing: pricing.isBundledPricing || false,
//                     pricingIds: pricing.pricingIds || []
//                 };

//                 // Direct service category pricing
//                 if (pricing.services.serviceCategory) {
//                     const serviceCategoryId = pricing.services.serviceCategory.toString();
//                     if (pricingByServiceCategory[serviceCategoryId]) {
//                         pricingByServiceCategory[serviceCategoryId].push(pricingInfo);
//                     }

//                     // Map pricing to appointment types (subtypes)
//                     if (pricing.services.appointmentType && Array.isArray(pricing.services.appointmentType)) {
//                         pricing.services.appointmentType.forEach(appointmentTypeId => {
//                             const subtypeId = appointmentTypeId.toString();
//                             if (pricingBySubtype[subtypeId]) {
//                                 pricingBySubtype[subtypeId].push(pricingInfo);
//                             }
//                         });
//                     }
//                 }

//                 // Relationship-based pricing
//                 if (pricing.services.relationShip && Array.isArray(pricing.services.relationShip)) {
//                     pricing.services.relationShip.forEach(relation => {
//                         if (!relation || !relation.serviceCategory) {
//                             return;
//                         }

//                         const relationServiceCategoryId = relation.serviceCategory.toString();
//                         if (pricingByServiceCategory[relationServiceCategoryId]) {
//                             pricingByServiceCategory[relationServiceCategoryId].push(pricingInfo);
//                         }

//                         // Map pricing to subtype IDs in the relationship
//                         if (relation.subTypeIds && Array.isArray(relation.subTypeIds)) {
//                             relation.subTypeIds.forEach(subtypeId => {
//                                 const subtypeIdStr = subtypeId.toString();
//                                 if (pricingBySubtype[subtypeIdStr]) {
//                                     pricingBySubtype[subtypeIdStr].push(pricingInfo);
//                                 }
//                             });
//                         }
//                     });
//                 }
//             });



//             const subtypeMap = {};
//             serviceCategories.forEach(category => {
//                 if (!category.appointmentType) {
//                     return;
//                 }

//                 category.appointmentType.forEach((subtype: any) => {
//                     if (!subtype || !subtype._id) {
//                         return;
//                     }

//                     const subtypeId = subtype._id.toString();
//                     const serviceCategoryId = category._id.toString();
//                     console.log(category.description)
//                     subtypeMap[subtypeId] = {
//                         name: subtype.name,
//                         durationInMinutes: subtype.durationInMinutes,
//                         onlineBookingAllowed: subtype.onlineBookingAllowed,
//                         description:category?.description ? category?.description : undefined,
//                         isActive: subtype.isActive !== undefined ? subtype.isActive : true,
//                         image: subtype.image || "",
//                         isFeatured: subtype.isFeatured || false,
//                         serviceId: category._id,
//                         serviceName: category.name,
//                         classType: category.classType,
//                         pricing: pricingBySubtype[subtypeId] || [],
//                         serviceCategoryPricing: pricingByServiceCategory[serviceCategoryId] || []
//                     };
//                 });
//             });

//             // Process each trainer's availability
//             const trainerAvailability = [];

//             for (const userId in availabilityByTrainer) {
//                 const record = availabilityByTrainer[userId];
//                 const userInfo = userMap[userId] || {
//                     userName: "Unknown",
//                     facilityId: record.facilityId,
//                     profilePicture: ""
//                 };

//                 const facilityId = userInfo.facilityId?.toString();
//                 const facilityName = facilityId ? (facilityMap[facilityId] || "Unknown Facility") : "Unknown Facility";

//                 // Separate available and unavailable slots
//                 const availableSlots = record.timeSlots.filter(slot => slot.availabilityStatus !== "unavailable");

//                 // Add payrate details to each slot
//                 const slotsWithPayrates = availableSlots.map(slot => {
//                     const slotPayrates = slot.payRateIds ?
//                         payrateDetails.filter(pr =>
//                             slot.payRateIds.some(id => id.toString() === pr._id.toString())
//                         ) : [];

//                     // Add subtype details to payrates
//                     const payratesWithSubtypes = slotPayrates.map(pr => {
//                         const subtypeId = pr.appointmentType?.toString();
//                         const subtypeDetails = subtypeMap[subtypeId] || {
//                             name: 'Unknown',
//                             durationInMinutes: 0,
//                             onlineBookingAllowed: false,
//                             isActive: true,
//                             pricing: [],
//                             serviceCategoryPricing: []
//                         };

//                         return {
//                             ...pr,
//                             subtypeName: subtypeDetails.name,
//                             durationInMinutes: subtypeDetails.durationInMinutes,
//                             onlineBookingAllowed: subtypeDetails.onlineBookingAllowed,
//                             isActive: subtypeDetails.isActive,
//                             image: subtypeDetails.image,
//                             isFeatured: subtypeDetails.isFeatured,
//                             serviceId: subtypeDetails.serviceId,
//                             serviceName: subtypeDetails.serviceName,
//                             serviceDescription:subtypeDetails.description,
//                             classType: subtypeDetails.classType,
//                             pricing: subtypeDetails.pricing || [],
//                             serviceCategoryPricing: subtypeDetails.serviceCategoryPricing || []
//                         };
//                     });

//                     // Filter payrates by service types only
//                     const filteredPayrates = payratesWithSubtypes.filter(pr =>
//                         pr.classType && serviceTypes.includes(pr.classType)
//                     );

//                     return {
//                         ...slot,
//                         payrates: filteredPayrates
//                     };
//                 });

//                 // Only include slots that have payrates after filtering
//                 const filteredSlots = slotsWithPayrates.filter(slot =>
//                     slot.payrates && slot.payrates.length > 0);

//                 // Only include trainer if they have available slots after filtering
//                 if (filteredSlots.length > 0) {
//                     trainerAvailability.push({
//                         userId: new Types.ObjectId(userId),
//                         userName: userInfo.userName,
//                         facilityId: userInfo.facilityId,
//                         facilityName: facilityName,
//                         profilePicture: userInfo.profilePicture || "",
//                         date: record.date,
//                         timeSlots: filteredSlots
//                     });
//                 }
//             }


//             return {
//                 date: date,
//                 trainers: trainerAvailability
//             };
//         } catch (error) {
//             console.error('Error in getTrainerAvailablity:', error);
//             throw new BadRequestException(error.message);
//         }
//     }
  async getTrainerAvailablity(body: any) {
        try {
            const organizationId = body.organizationId || "67a5e32e1bbdd61401b5e36c";
            const date = body.date ? new Date(body.date) : new Date();
            const serviceTypes = body.serviceTypes || ["personalAppointment"]; // Default to these types if not specified
            
            
            const dayStart = new Date(date);
            dayStart.setUTCHours(0, 0, 0, 0);
            
            const dayEnd = new Date(date);
            dayEnd.setUTCHours(23, 59, 59, 999);
            
            // Get all trainers from the organization
            const trainers = await this.StaffModel.find(
                { organizationId: new Types.ObjectId(organizationId) },
                { userId: 1, facilityId: 1 }
            ).lean();
            
            
            if (!trainers.length) {
                return { trainers: [] };
            }
            
            const trainerIds = trainers.map(trainer => trainer.userId);
            
            // Find all availability records for trainers
            const allAvailabilities = await this.staffAvailabilityModel.find({
                organizationId: new Types.ObjectId(organizationId),
                userId: { $in: trainerIds },
                date: { $gte: dayStart, $lte: dayEnd }
            }).lean();
            
            
            // Group availabilities by userId
            const availabilityByTrainer = {};
            allAvailabilities.forEach(record => {
                if (!availabilityByTrainer[record.userId.toString()]) {
                    availabilityByTrainer[record.userId.toString()] = record;
                } else {
                    // Merge time slots if multiple records exist for same trainer
                    availabilityByTrainer[record.userId.toString()].timeSlots = [
                        ...availabilityByTrainer[record.userId.toString()].timeSlots,
                        ...record.timeSlots
                    ];
                }
            });
            
            // Get all facility IDs from trainers
            const facilityIds = [...new Set(trainers.map(trainer => trainer.facilityId))];
            
            // Get facility details
            const facilities = await this.FacilityModel.find(
                { _id: { $in: facilityIds } },
                { facilityName: 1 }
            ).lean();
            
            // Create facility map
            const facilityMap = {};
            facilities.forEach(facility => {
                facilityMap[facility._id.toString()] = facility.facilityName;
            });
            
            // Get user details for all trainers including profile pictures
            const userIds = Object.keys(availabilityByTrainer).map(id => new Types.ObjectId(id));
            const users = await this.StaffModel.aggregate([
                {
                    $match: { userId: { $in: userIds } }
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "userId",
                        foreignField: "_id",
                        as: "user",
                        pipeline: [{ $project: { firstName: 1, lastName: 1 } }]
                    }
                },
                { $unwind: { path: "$user", preserveNullAndEmptyArrays: true } },
                {
                    $lookup: {
                        from: "staffprofiledetails",
                        localField: "userId",
                        foreignField: "userId",
                        as: "staffProfile",
                        pipeline: [{ $project: { profilePicture: 1 } }]
                    }
                },
                { $unwind: { path: "$staffProfile", preserveNullAndEmptyArrays: true } },
                {
                    $project: {
                        userId: 1,
                        facilityId: 1,
                        userName: { $concat: ["$user.firstName", " ", "$user.lastName"] },
                        profilePicture: "$staffProfile.profilePicture"
                    }
                }
            ]);
            
            // Create a map of user details
            const userMap = {};
            users.forEach(user => {
                userMap[user.userId.toString()] = user;
            });
            
            // Extract all payRateIds from available slots
            const payrateIds = [
                ...new Set(
                    Object.values(availabilityByTrainer).flatMap((record: any) =>
                        record.timeSlots
                            .filter(slot => slot.availabilityStatus !== "unavailable")
                            .flatMap(slot => slot.payRateIds || [])
                    )
                )
            ];
            
            
            // Get payrate details
            const payrateDetails = await this.PayRateModel.find({
                _id: { $in: payrateIds }
            }).lean();
            
            
            // Get service categories and subtypes
            const serviceCategoryIds = [...new Set(payrateDetails.map(pr => pr.serviceCategory))];
            
            const serviceCategories = await this.ServiceModel.find({
                _id: { $in: serviceCategoryIds }
            }).lean();
            
            
            // Get pricing information for these service categories and subtypes
            const pricingQuery = {
                organizationId: new Types.ObjectId(organizationId),
                isActive: true,
                isSellOnline: true,
                $or: [
                    // Direct service category match
                    {
                        "services.serviceCategory": { $in: serviceCategoryIds.map(id => new Types.ObjectId(id)) }
                    },
                    // Match through relationship
                    {
                        "services.relationShip.serviceCategory": { $in: serviceCategoryIds.map(id => new Types.ObjectId(id)) }
                    }
                ]
            };
            
            const pricingList = await this.PricingModel.find(pricingQuery).lean();
            
            // Log sample pricing data
           
            
            // Create pricing maps for easy lookup
            const pricingByServiceCategory = {};
            const pricingBySubtype = {};
            
            // Extract all appointment type IDs from service categories
            const allSubtypeIds = serviceCategories.flatMap(category => 
                (category.appointmentType || []).map((subtype: any) => subtype._id.toString())
            );
            
            // Initialize empty arrays for all service categories and subtypes
            serviceCategoryIds.forEach(id => {
                pricingByServiceCategory[id.toString()] = [];
            });
            
            allSubtypeIds.forEach(id => {
                pricingBySubtype[id] = [];
            });
            
            // Process each pricing option
            pricingList.forEach(pricing => {
                if (!pricing.services || pricing.isTrialPricing !== true) {
                    // Skip non-trial pricing
                    return;
                }
                
                // Include only trial pricing parameters
                const pricingInfo = {
                    _id: pricing._id,
                    name: pricing.name,
                    price: pricing.price,
                    tax: pricing.tax || 0,
                    sessionType: pricing.services.sessionType,
                    sessionCount: pricing.services.sessionCount,
                    expiredInDays: pricing.expiredInDays || 0,
                    durationUnit: pricing.durationUnit || 'days',
                    isTrialPricing: true,
                    description: pricing.description || ''
                };
                
                // Direct service category pricing
                if (pricing.services.serviceCategory) {
                    const serviceCategoryId = pricing.services.serviceCategory.toString();
                    if (pricingByServiceCategory[serviceCategoryId]) {
                        pricingByServiceCategory[serviceCategoryId].push(pricingInfo);
                    }
                    
                    // Map pricing to appointment types (subtypes)
                    if (pricing.services.appointmentType && Array.isArray(pricing.services.appointmentType)) {
                        pricing.services.appointmentType.forEach(appointmentTypeId => {
                            const subtypeId = appointmentTypeId.toString();
                            if (pricingBySubtype[subtypeId]) {
                                pricingBySubtype[subtypeId].push(pricingInfo);
                            }
                        });
                    }
                }
                
                // Relationship-based pricing
                if (pricing.services.relationShip && Array.isArray(pricing.services.relationShip)) {
                    pricing.services.relationShip.forEach(relation => {
                        if (!relation || !relation.serviceCategory) {
                            return;
                        }
                        
                        const relationServiceCategoryId = relation.serviceCategory.toString();
                        if (pricingByServiceCategory[relationServiceCategoryId]) {
                            pricingByServiceCategory[relationServiceCategoryId].push(pricingInfo);
                        }
                        
                        // Map pricing to subtype IDs in the relationship
                        if (relation.subTypeIds && Array.isArray(relation.subTypeIds)) {
                            relation.subTypeIds.forEach(subtypeId => {
                                const subtypeIdStr = subtypeId.toString();
                                if (pricingBySubtype[subtypeIdStr]) {
                                    pricingBySubtype[subtypeIdStr].push(pricingInfo);
                                }
                            });
                        }
                    });
                }
            });
            
            // Log pricing map sizes
          
            
            // Create subtype map with all details
            const subtypeMap = {};
            serviceCategories.forEach(category => {
                if (!category.appointmentType) {
                    return;
                }
                
                category.appointmentType.forEach((subtype: any) => {
                    if (!subtype || !subtype._id) {
                        return;
                    }
                    
                    const subtypeId = subtype._id.toString();
                    const serviceCategoryId = category._id.toString();
                    
                    subtypeMap[subtypeId] = {
                        name: subtype.name,
                        durationInMinutes: subtype.durationInMinutes,
                        onlineBookingAllowed: subtype.onlineBookingAllowed,
                        isActive: subtype.isActive !== undefined ? subtype.isActive : true,
                        image: subtype.image || "",
                        isFeatured: subtype.isFeatured || false,
                        serviceId: category._id,
                        serviceName: category.name,
                        classType: category.classType,
                        pricing: pricingBySubtype[subtypeId] || [],
                        serviceCategoryPricing: pricingByServiceCategory[serviceCategoryId] || []
                    };
                });
            });
            
            // Process each trainer's availability
            const trainerAvailability = [];
            
            for (const userId in availabilityByTrainer) {
                const record = availabilityByTrainer[userId];
                const userInfo = userMap[userId] || { 
                    userName: "Unknown", 
                    facilityId: record.facilityId,
                    profilePicture: "" 
                };
                
                const facilityId = userInfo.facilityId?.toString();
                const facilityName = facilityId ? (facilityMap[facilityId] || "Unknown Facility") : "Unknown Facility";
                
                // Separate available and unavailable slots
                const availableSlots = record.timeSlots.filter(slot => slot.availabilityStatus !== "unavailable");
                
                // Add payrate details to each slot
                const slotsWithPayrates = availableSlots.map(slot => {
                    const slotPayrates = slot.payRateIds ? 
                        payrateDetails.filter(pr => 
                            slot.payRateIds.some(id => id.toString() === pr._id.toString())
                        ) : [];
                    
                    // Add subtype details to payrates
                    const payratesWithSubtypes = slotPayrates.map(pr => {
                        const subtypeId = pr.appointmentType?.toString();
                        const subtypeDetails = subtypeMap[subtypeId] || {
                            name: 'Unknown',
                            durationInMinutes: 0,
                            onlineBookingAllowed: false,
                            isActive: true,
                            pricing: [],
                            serviceCategoryPricing: []
                        };
                        
                        return {
                            ...pr,
                            subtypeName: subtypeDetails.name,
                            durationInMinutes: subtypeDetails.durationInMinutes,
                            onlineBookingAllowed: subtypeDetails.onlineBookingAllowed,
                            isActive: subtypeDetails.isActive,
                            image: subtypeDetails.image,
                            isFeatured: subtypeDetails.isFeatured,
                            serviceId: subtypeDetails.serviceId,
                            serviceName: subtypeDetails.serviceName,
                            classType: subtypeDetails.classType,
                            pricing: subtypeDetails.pricing || [],
                            serviceCategoryPricing: subtypeDetails.serviceCategoryPricing || []
                        };
                    });
                    
                    // Filter payrates by service types and only include those with trial pricing
                    const filteredPayrates = payratesWithSubtypes.filter(pr => {
                        // Only include if the payrate has trial pricing options
                        const hasTrialPricing = 
                            (pr.pricing && pr.pricing.length > 0) || 
                            (pr.serviceCategoryPricing && pr.serviceCategoryPricing.length > 0);
                        
                        return pr.classType && 
                               serviceTypes.includes(pr.classType) && 
                               hasTrialPricing;
                    });
                    
                    return {
                        ...slot,
                        payrates: filteredPayrates
                    };
                });
                
                // Only include slots that have payrates after filtering
                const filteredSlots = slotsWithPayrates.filter(slot => 
                    slot.payrates && slot.payrates.length > 0);
                
                // Only include trainer if they have available slots after filtering
                if (filteredSlots.length > 0) {
                    trainerAvailability.push({
                        userId: new Types.ObjectId(userId),
                        userName: userInfo.userName,
                        facilityId: userInfo.facilityId,
                        facilityName: facilityName,
                        profilePicture: userInfo.profilePicture || "",
                        date: record.date,
                        timeSlots: filteredSlots
                    });
                }
            }
            
            
            return {
                date: date,
                trainers: trainerAvailability
            };
        } catch (error) {
            console.error('Error in getTrainerAvailablity:', error);
            throw new BadRequestException(error.message);
        }
    }
    async getCourseList(body: any) {
        try {
            // if (!body || Object.keys(body).length === 0) {
            //     throw new BadRequestException('Invalid request data');
            // }

            const organizationId = body.organizationId || "678505b84d8168563536ed5a";

            const filter: any = {
                organizationId: new Types.ObjectId(organizationId),
                isBundledPricing: { $ne: true },
                "services.type": "courses",
                isActive: true,
                isSellOnline: true
            };

            if (body.search) filter.name = { $regex: body.search, $options: "i" };
            if (body.isFeatured !== undefined) filter.isFeatured = body.isFeatured;

            const pricingList = await this.PricingModel.find(filter)
                .select('name price tax expiredInDays services.serviceCategory isFeatured')
                .sort({ createdAt: -1 })
                .lean();

            // Get service category details for each course
            const serviceCategoryIds = pricingList.map(pricing => pricing.services?.serviceCategory);
            const serviceCategories = await this.ServiceModel.find(
                { _id: { $in: serviceCategoryIds } },
                { name: 1, appointmentType: 1, image: 1 }
            ).lean();

            // Create a map for quick lookup
            const serviceCategoryMap = {};
            serviceCategories.forEach(category => {
                serviceCategoryMap[category._id.toString()] = category;
            });

            // Combine pricing with service category details
            const courses = pricingList.map(pricing => {
                const serviceCategory = pricing.services?.serviceCategory ?
                    serviceCategoryMap[pricing.services.serviceCategory.toString()] : null;

                return {
                    _id: pricing._id,
                    name: pricing.name,
                    price: pricing.price,
                    tax: pricing.tax || 0,
                    expiredInDays: pricing.expiredInDays,
                    isFeatured: pricing.isFeatured || false,
                    serviceCategory: serviceCategory ? {
                        _id: serviceCategory._id,
                        name: serviceCategory.name,
                        image: serviceCategory.image || ""
                    } : null
                };
            });

            return courses;
        } catch (error) {
            console.error('Error in getCourseList:', error);
            throw new BadRequestException(error.message);
        }
    }

    async getCourseDetail(body: any) {
        try {
            if (!body || !body.id) {
                throw new BadRequestException('Course ID is required');
            }

            const courseId = body.id;
            const organizationId = body.organizationId || "678505b84d8168563536ed5a";

            // Get course pricing details
            const pricingData: any = await this.PricingModel.findOne({
                _id: new Types.ObjectId(courseId),
                isActive: true
            }).lean();

            if (!pricingData) {
                throw new NotFoundException('Course not found');
            }

            // Get service category details
            const serviceCategoryId = (pricingData as any)?.services?.serviceCategory;
            const serviceCategory = await this.ServiceModel.findOne(
                { _id: serviceCategoryId },
                { name: 1, appointmentType: 1, description: 1, image: 1 }
            ).lean();

            if (!serviceCategory) {
                throw new NotFoundException('Service category not found');
            }

            // Get trainers with specialization for this course
            const trainers = await this.PayRateModel.aggregate([
                {
                    $match: {
                        serviceCategory: new Types.ObjectId(serviceCategoryId),
                        appointmentType: { $in: pricingData.services?.appointmentType || [] }
                    }
                },
                {
                    $lookup: {
                        from: "staffprofiledetails",
                        localField: "userId",
                        foreignField: "userId",
                        as: "staffProfile"
                    }
                },
                {
                    $unwind: "$staffProfile"
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "userId",
                        foreignField: "_id",
                        as: "userData"
                    }
                },
                {
                    $unwind: "$userData"
                },
                {
                    $lookup: {
                        from: "facilities",
                        localField: "staffProfile.facilityId",
                        foreignField: "_id",
                        as: "facilityData"
                    }
                },
                {
                    $unwind: {
                        path: "$facilityData",
                        preserveNullAndEmptyArrays: true
                    }
                },
                {
                    $project: {
                        _id: 1,
                        userId: 1,
                        serviceCategory: 1,
                        appointmentType: 1,
                        payRate: 1,
                        trainerName: { $concat: ["$userData.firstName", " ", "$userData.lastName"] },
                        profilePicture: "$staffProfile.profilePicture",
                        facilityId: "$staffProfile.facilityId",
                        facilityName: "$facilityData.facilityName",
                        experience: "$staffProfile.experience",
                        certification: "$staffProfile.certification",
                        description: "$staffProfile.description"
                    }
                }
            ]);

            // Calculate expiration date if applicable
            let expirationInfo = null;
            if (pricingData && !Array.isArray(pricingData) && pricingData.expiredInDays) {
                const multiplierMap: Record<string, number> = {
                    days: 24 * 60 * 60 * 1000,
                    months: 30 * 24 * 60 * 60 * 1000,
                    years: 365 * 24 * 60 * 60 * 1000,
                };

                const durationUnit = pricingData.durationUnit || 'days';
                const multiplier = multiplierMap[durationUnit] || multiplierMap.days;
                const expirationMs = pricingData.expiredInDays * multiplier;

                expirationInfo = {
                    days: pricingData.expiredInDays,
                    unit: durationUnit,
                    expirationMs
                };
            }

            // Ensure pricingData is not an array before accessing its properties
            const singlePricingData = Array.isArray(pricingData) ? pricingData[0] : pricingData;

            if (!singlePricingData) {
                throw new NotFoundException('Course not found');
            }

            // Format the response
            const courseDetail = {
                _id: singlePricingData._id,
                name: singlePricingData.name,
                price: singlePricingData.price,
                tax: singlePricingData.tax || 0,
                description: singlePricingData.description || serviceCategory.description || "",
                expiredInDays: singlePricingData.expiredInDays,
                expirationInfo,
                isFeatured: singlePricingData.isFeatured || false,
                isSellOnline: singlePricingData.isSellOnline || true,
                sessionType: singlePricingData.services?.sessionType,
                sessionCount: singlePricingData.services?.sessionCount,
                serviceCategory: {
                    _id: serviceCategory._id,
                    name: serviceCategory.name,
                    image: serviceCategory.image || "",
                    appointmentTypes: serviceCategory.appointmentType || []
                },
                trainers: trainers
            };

            return courseDetail;
        } catch (error) {
            console.error('Error in getCourseDetail:', error);
            throw new BadRequestException(error.message);
        }
    }

    async getTrainerTimeSlots(body: any) {
        try {

            if (!body || !body.trainerId || !body.subtypeId || !body.date) {
                throw new BadRequestException('trainerId, subtypeId, and date are required');
            }

            const { trainerId, subtypeId, date, organizationId = "678505b84d8168563536ed5a", serviceCategoryId } = body;

            // Parse the date
            const targetDate = new Date(date);
            const dayStart = new Date(targetDate);
            dayStart.setUTCHours(0, 0, 0, 0);

            const dayEnd = new Date(targetDate);
            dayEnd.setUTCHours(23, 59, 59, 999);

            // Get subtype details to get duration
            const subtypeDetails = await this.getSubtypeDetails(subtypeId, serviceCategoryId, organizationId);
            if (!subtypeDetails) {
                throw new BadRequestException('Invalid subtype ID');
            }

            const durationInMinutes = subtypeDetails.durationInMinutes;

            // Get trainer availability for the date
            const trainerAvailability = await this.staffAvailabilityModel.findOne({
                userId: new Types.ObjectId(trainerId),
                date: { $gte: dayStart, $lte: dayEnd },
                organizationId: new Types.ObjectId(organizationId)
            }).lean();

            if (!trainerAvailability) {
                return {
                    date: targetDate,
                    trainerId,
                    subtypeId,
                    durationInMinutes,
                    timeSlots: []
                };
            }

            // Get existing bookings for the trainer on this date
            const existingBookings = await this.SchedulingModel.find({
                trainerId: new Types.ObjectId(trainerId),
                date: { $gte: dayStart, $lte: dayEnd },
                scheduleStatus: { $ne: 'canceled' }
            }, {
                from: 1,
                to: 1
            }).lean();

            // Generate time slots based on availability and check against bookings
            const timeSlots = this.generateTimeSlots(
                trainerAvailability.timeSlots,
                existingBookings,
                durationInMinutes,
                subtypeId
            );
            const reqBody = {
                serviceCategoryId,
                subTypeId: subtypeId,
                classType: "personalAppointment",
                clientUserId: body.userId,
                date
            }
            const purchasedPackage = await this.pricingByUserAndSubType(reqBody, organizationId);
            return {
                date: targetDate,
                trainerId,
                subtypeId,
                durationInMinutes,
                trainerName: await this.getTrainerName(trainerId),
                timeSlots,
                purchasePackage: purchasedPackage
            };

        } catch (error) {
            console.error('Error in getTrainerTimeSlots:', error);
            throw new BadRequestException(error.message);
        }
    }

    private async getSubtypeDetails(subtypeId: string, serviceCategoryId: string, organizationId: string) {
        try {
            // Find the service that contains this subtype
            const service = await this.ServiceModel.findOne({
                'appointmentType._id': new Types.ObjectId(subtypeId),
                _id: new Types.ObjectId(serviceCategoryId),
                organizationId: new Types.ObjectId(organizationId)
            }).lean();
            if (service && service.appointmentType && service.appointmentType.length > 0) {
                return service.appointmentType[0];
            }
            return null;
        } catch (error) {
            console.error('Error getting subtype details:', error);
            return null;
        }
    }

    private async getTrainerName(trainerId: string) {
        try {
            const trainer = await this.StaffModel.aggregate([
                {
                    $match: { userId: new Types.ObjectId(trainerId) }
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "userId",
                        foreignField: "_id",
                        as: "user",
                        pipeline: [{ $project: { firstName: 1, lastName: 1 } }]
                    }
                },
                { $unwind: { path: "$user", preserveNullAndEmptyArrays: true } },
                {
                    $project: {
                        userName: { $concat: ["$user.firstName", " ", "$user.lastName"] }
                    }
                }
            ]);

            return trainer.length > 0 ? trainer[0].userName : "Unknown Trainer";
        } catch (error) {
            console.error('Error getting trainer name:', error);
            return "Unknown Trainer";
        }
    }

    private generateTimeSlots(availabilitySlots: any[], existingBookings: any[], durationInMinutes: number, subtypeId: string) {
        const timeSlots = [];

        // Filter availability slots that are available and match the subtype
        const relevantSlots = availabilitySlots.filter(slot =>
            slot.availabilityStatus === 'available' &&
            slot.payRateIds && slot.payRateIds.length > 0
        );

        for (const availabilitySlot of relevantSlots) {
            const slotTimeSlots = this.generateSlotsForTimeRange(
                availabilitySlot.from,
                availabilitySlot.to,
                durationInMinutes,
                existingBookings,
                availabilitySlot.payRateIds
            );
            timeSlots.push(...slotTimeSlots);
        }

        // Sort time slots by start time
        timeSlots.sort((a, b) => a.from.localeCompare(b.from));

        return timeSlots;
    }

    private generateSlotsForTimeRange(
        startTime: string,
        endTime: string,
        durationInMinutes: number,
        existingBookings: any[],
        payRateIds: string[]
    ) {
        const slots = [];

        // Convert time strings to minutes for easier calculation
        const startMinutes = this.timeToMinutes(startTime);
        const endMinutes = this.timeToMinutes(endTime);

        let currentMinutes = startMinutes;

        while (currentMinutes + durationInMinutes <= endMinutes) {
            const slotStart = this.minutesToTime(currentMinutes);
            const slotEnd = this.minutesToTime(currentMinutes + durationInMinutes);

            // Check if this slot conflicts with existing bookings
            const isAvailable = !this.hasTimeConflict(slotStart, slotEnd, existingBookings);

            slots.push({
                from: slotStart,
                to: slotEnd,
                isAvailable,
                payRateIds,
                durationInMinutes
            });

            // Move to next slot (using the duration as the increment)
            currentMinutes += durationInMinutes;
        }

        return slots;
    }

    private timeToMinutes(timeString: string): number {
        const [hours, minutes] = timeString.split(':').map(Number);
        return hours * 60 + minutes;
    }

    private minutesToTime(minutes: number): string {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
    }

    private hasTimeConflict(slotStart: string, slotEnd: string, existingBookings: any[]): boolean {
        return existingBookings.some(booking => {
            // Check if the slot overlaps with any existing booking
            return (
                (slotStart >= booking.from && slotStart < booking.to) ||
                (slotEnd > booking.from && slotEnd <= booking.to) ||
                (slotStart <= booking.from && slotEnd >= booking.to)
            );
        });
    }
    async pricingByUserAndSubType(reqBody: any, organizationId: any,): Promise<any> {
        let { classType, clientUserId, serviceCategoryId, subTypeId, date } = reqBody;
        let role = 'user'
        const targetDate = new Date(date);
        const dayStart = new Date(targetDate);
        dayStart.setUTCHours(0, 0, 0, 0);
        const dayEnd = new Date(targetDate);
        dayEnd.setUTCHours(23, 59, 59, 999);
        if (!organizationId) throw new BadRequestException("Organization ID not found");


        let purchasedPackages = await this.purchaseModel.aggregate([
            {
                $match: {
                    organizationId: new Types.ObjectId(organizationId),
                    consumers: new Types.ObjectId(clientUserId),
                    isActive: true,
                    $and: [
                        { isExpired: { $ne: true } },
                        {
                            startDate: { $lte: dayStart },
                            endDate: { $gte: dayEnd }
                        }
                    ]
                }
            },
            {
                $lookup: {
                    from: "pricings",
                    localField: "packageId",
                    foreignField: "_id",
                    as: "pricingDetails"
                }
            },
            {
                $unwind: {
                    path: "$pricingDetails",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $match: {
                    "pricingDetails.services.type": classType,
                    "pricingDetails.services.serviceCategory": new Types.ObjectId(serviceCategoryId),
                    "pricingDetails.services.appointmentType": new Types.ObjectId(subTypeId)
                }
            },
            // {
            //     $set: {
            //         sessionConsumed: { $ifNull: ["$sessionConsumed", 0] }
            //     }
            // },
            // {
            //     $addFields: {
            //         remainingSession: {
            //             $cond: {
            //                 if: { $eq: ["$totalSessions", Infinity] },
            //                 then: "unlimited",
            //                 else: { $subtract: ["$totalSessions", "$sessionConsumed"] }
            //             }
            //         },
            //     },
            // },

            {
                $project: {
                    purchaseId: "$_id",
                    packageId: "$pricingDetails._id",
                    packageName: "$pricingDetails.name",
                    sessionCount: {
                        $cond: {
                            if: { $eq: ["$totalSessions", Infinity] },
                            then: "unlimited",
                            else: "$totalSessions"
                        }
                    },
                    sessionConsumed: "$sessionConsumed",
                    remainingSession: 1,
                    isPurchased: { $literal: true },
                    sessionType: 1,
                    dayPassLimit: 1,
                    pricingDetails: "$pricingDetails.services"
                }
            },
        ]).exec();

        if (role === ENUM_ROLE_TYPE.USER && purchasedPackages.length === 0) {
            purchasedPackages = await this.PricingModel.aggregate([
                {
                    $match: {
                        organizationId: new Types.ObjectId(organizationId),
                        "services.type": classType,
                        isActive: { $ne: false },
                        $or: [
                            {
                                "services.serviceCategory": new Types.ObjectId(serviceCategoryId),
                                "services.appointmentType": new Types.ObjectId(subTypeId)
                            },
                        ]
                    }
                },

                {
                    $project: {
                        packageId: "$_id",
                        packageName: "$name",
                        sessionCount: {
                            $cond: {
                                if: { $eq: ["$services.sessionCount", Infinity] },
                                then: "unlimited",
                                else: "$services.sessionCount"
                            }
                        },
                        sessionConsumed: { $literal: 0 },
                        remainingSession: {
                            $cond: {
                                if: { $eq: ["$services.sessionCount", Infinity] },
                                then: "unlimited",
                                else: "$services.sessionCount"
                            }
                        },
                        isPurchased: { $literal: false },
                        sessionType: 1,
                        dayPassLimit: 1

                    }
                },

            ]).exec();
        }
        if (purchasedPackages.length) {
            const dayPassPurchases = purchasedPackages.length ? purchasedPackages.filter(item => item.sessionType === SessionType.DAY_PASS) : [];
            const dayPassPurchaseIds = dayPassPurchases.map(item => new Types.ObjectId(item._id));

            const sessionsCount = await this.SchedulingModel.aggregate([
                {
                    $match: {
                        purchaseId: { $in: dayPassPurchaseIds },
                        scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                    },
                },
                {
                    $group: {
                        _id: { purchaseId: "$purchaseId", date: "$date" },
                    },
                },
            ]);

            const sessionsByPurchaseId = sessionsCount.reduce((acc, session) => {
                const purchaseId = session._id.purchaseId.toString();
                const date = session._id.date.toISOString().split("T")[0];
                if (!acc[purchaseId]) acc[purchaseId] = [];
                acc[purchaseId].push(date);
                return acc;
            }, {});

            const todayUTC = new Date();
            todayUTC.setUTCHours(0, 0, 0, 0);
            const todayISTDate = todayUTC.toISOString().split("T")[0];

            for (const item of purchasedPackages || []) {
                if (item.sessionType === SessionType.DAY_PASS) {
                    const bookedDates = sessionsByPurchaseId[item._id.toString()] || [];
                    const remainingDays = bookedDates.filter((date) => date < todayISTDate);

                    const consumedDayPassLimit = remainingDays.length;
                    const assignedDayPassLimit = item.dayPassLimit || 0;
                    item.remainingSession = !isNaN(assignedDayPassLimit - consumedDayPassLimit) ? `${assignedDayPassLimit - consumedDayPassLimit} x Day Pass(es)` : 0;
                }
            }

        }
        return purchasedPackages;
    }

    /********************************schedule Personal APpointment************************************************** */
    async schedulePersonalAppointment(body: any) {
        try {
            const { clientId, trainerId, date, from, to, classType, dateRange, serviceCategoryId, purchaseId, subtypeId, duration, checkIn, organizationId } = body;
            const clientDetail = await this.ClientModel.findOne({ userId: clientId });
            body["facilityId"] = clientDetail.facilityId
            body["date"] = new Date(date)
            const userDetail: any = await this.userModel.findById(clientId).populate("role");
            userDetail.id = userDetail._id
            const res = await this.schedulingService.schedulePersonalAppointment(body, userDetail);
            return res;

        } catch (error: any) {
            throw new InternalServerErrorException(error.message);
        }
    }

}
