import { Injectable, UnauthorizedException } from '@nestjs/common';
import { Response } from "express";
import { BadRequestException, ForbiddenException, InternalServerErrorException, NotFoundException } from "@nestjs/common/exceptions";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import * as bcrypt from "bcrypt";
import { User, UserDocument } from "src/users/schemas/user.schema";
import { JwtService } from "@nestjs/jwt";
import { AuthTypes } from "../../utils/enums/auth.enum";
import { TransactionService } from "src/utils/services/transaction.service";
import { MailService } from "src/mail/services/mail.service";
import * as path from "path";
import * as jwt from "jsonwebtoken";
import { plainToInstance } from 'class-transformer';
import { GeneralService } from "src/utils/services/general.service";
import { Clients } from "src/users/schemas/clients.schema";
import { Facility } from "src/facility/schemas/facility.schema";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { HelperDateService } from "src/common/helper/services/helper.date.service";
import { HelperEncryptionService } from "src/common/helper/services/helper.encryption.service";
import { ConfigService } from "@nestjs/config";
import { RoleService } from "src/role/services/role.service";
import { UserService } from "src/users/services/user.service";
import { log } from "console";
import { SessionService } from "src/session/services/session.service";
import { IRequestApp } from "src/common/request/interfaces/request.interface";
import { IDatabaseObjectId } from 'src/common/database/interfaces/database.objectid.interface';
import { RELATION_ENUM } from "src/utils/enums/relation.enum";
import { Gender } from "src/utils/enums/gender.enum";
import { Organizations } from "src/organization/schemas/organization.schema";
import { WidgetRegisterUserDto } from '../dto/widget-Register-dto';
import { AuthJwtAccessPayloadDto } from 'src/auth/dto/jwt/auth.jwt.access-payload.dto';

@Injectable()
export class WidgetAuthService {
    // jwt
    private readonly jwtAccessTokenSecretKey: string;
    private readonly jwtAccessTokenExpirationTime: number;
    private readonly jwtPrefix: string;
    private readonly jwtAudience: string;
    private readonly jwtIssuer: string;

    constructor(
        @InjectModel(User.name) private UserModel: Model<User>,
        @InjectModel(Clients.name) private ClientModel: Model<Clients>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        private readonly configService: ConfigService,
        private readonly helperDateService: HelperDateService,
        private readonly helperEncryptionService: HelperEncryptionService,
        private readonly userService: UserService,
        private roleService: RoleService,
    ) {
        this.jwtAccessTokenSecretKey = this.configService.get<string>(
            'auth.jwt.accessToken.secretKey'
        );
        this.jwtAccessTokenExpirationTime = this.configService.get<number>(
            'auth.jwt.accessToken.expirationTime'
        );
        this.jwtPrefix = this.configService.get<string>('auth.jwt.prefix');
        this.jwtAudience = this.configService.get<string>('auth.jwt.audience');
        this.jwtIssuer = this.configService.get<string>('auth.jwt.issuer');

    }
    async registerUser(registerUserDto: WidgetRegisterUserDto, organizationId: string): Promise<{ user: IUserDocument; loginDate: Date; organizationId: string | null }> {
        try {
            const user = await this.UserModel.findOne({ [registerUserDto.type]: registerUserDto[registerUserDto.type], organizationId }).exec();

            if (user) throw new BadRequestException(`${[registerUserDto.type]} already exists`);
            const role = await this.roleService.findOneByType(ENUM_ROLE_TYPE.USER);
            if (!role) throw new BadRequestException("Invalid role");
            let userDetails = {
                name: [registerUserDto.firstName, registerUserDto.lastName].join(" ").trim(),
                firstName: registerUserDto.firstName ? registerUserDto.firstName : "",
                lastName: registerUserDto.lastName ? registerUserDto.lastName : "",
                role: role._id,
                isActive: true,
                organizationId,
                isUserAcceptTerms: registerUserDto.isUserAcceptTerms ? registerUserDto.isUserAcceptTerms : false,
                email: registerUserDto.email ? registerUserDto.email : undefined,
            };
            const emailRequired = registerUserDto.type === AuthTypes.EMAIL;
            const mobileRequired = registerUserDto.type === AuthTypes.MOBILE;

            if (emailRequired) {
                if (!registerUserDto[AuthTypes.EMAIL]) {
                    throw new Error("Email is required");
                }
                userDetails[AuthTypes.EMAIL] = registerUserDto[AuthTypes.EMAIL];

                if (!registerUserDto[AuthTypes.MOBILE]) {
                    throw new Error("Mobile number is required for registration");
                }
                let checkMobileNoExist = await this.UserModel.findOne({ mobile: registerUserDto[AuthTypes.MOBILE], organizationId }).select({ mobile: 1 }).exec();
                if (checkMobileNoExist) {
                    throw new BadRequestException(`${registerUserDto[AuthTypes.MOBILE]} already exists`);
                }
                userDetails[AuthTypes.MOBILE] = registerUserDto[AuthTypes.MOBILE];
                userDetails["countryCode"] = registerUserDto?.countryCode ? registerUserDto.countryCode : "+91";
            } else if (mobileRequired) {
                if (!registerUserDto[AuthTypes.MOBILE]) {
                    throw new Error("Mobile number is required");
                }
                userDetails[AuthTypes.MOBILE] = registerUserDto[AuthTypes.MOBILE];
                userDetails["countryCode"] = registerUserDto?.countryCode ? registerUserDto.countryCode : "+91";

                // if (!registerUserDto[AuthTypes.EMAIL]) {
                //     throw new Error("Email is required for registration");
                // }

                if (AuthTypes.EMAIL === registerUserDto.type) {
                    let checkEmailExist = await this.UserModel.findOne({ email: registerUserDto[AuthTypes.EMAIL], organizationId }).select({ email: 1 }).exec();
                    if (checkEmailExist) {
                        throw new BadRequestException(`${registerUserDto[AuthTypes.MOBILE]} already exists`);
                    }
                    userDetails[AuthTypes.EMAIL] = registerUserDto[AuthTypes.EMAIL];
                }
            } else {
                throw new Error("Invalid registration type specified");
            }
            const createdUser = new this.UserModel(userDetails);

            await createdUser.save();

            const facilitiesDetailArray = await this.FacilityModel.find({ organizationId: registerUserDto.organizationId, });
            const clientDetails = {
                userId: createdUser["_id"],
                facilityId: facilitiesDetailArray.length > 0 ? facilitiesDetailArray[0]._id : undefined,
                organizationId: registerUserDto.organizationId,
                address: {
                    state: facilitiesDetailArray.length > 0 ? facilitiesDetailArray[0].address.state : undefined
                },
                createdBy:createdUser["_id"],
            };
            if (registerUserDto.facilityId) {
                let facilityValid = await this.FacilityModel.findOne({ organizationId: registerUserDto.organizationId, _id: registerUserDto.facilityId });
                if (!facilityValid) throw new BadRequestException("Facility not found");
            }

            let saveClient = new this.ClientModel(clientDetails);
            await saveClient.save();
            createdUser.password = createdUser.salt = undefined;

            const newUser = await this.userService.findOneWithRoleAndPermissions({ _id: createdUser._id });
            const tokenData = await this.createToken(newUser, registerUserDto.organizationId);
            // return { user: createdUser, accessToken: accessToken };
            return { user: newUser, loginDate: tokenData.loginDate, organizationId: null }
        } catch (error) {
            console.log(error)
            throw new Error(error.message);
        }
    }
    async createToken(
        user: IUserDocument,
        organization?: Types.ObjectId | string
    ): Promise<any> {
        const loginDate = this.helperDateService.create();
        const roleType = user.role.type;

        const payloadAccessToken: AuthJwtAccessPayloadDto =
            await this.createPayloadAccessToken(
                user,
                loginDate,
                organization.toString()
            );
        const accessToken: string = await this.createAccessToken(
            user._id.toString(),
            payloadAccessToken
        );

        return {
            tokenType: this.jwtPrefix,
            roleType,
            loginDate,
            expiresIn: this.jwtAccessTokenExpirationTime,
            accessToken,
        };
    }
    async createPayloadAccessToken(
        data: IUserDocument,
        loginDate: Date,
        organization: Types.ObjectId | string
    ): Promise<AuthJwtAccessPayloadDto> {
        return plainToInstance(AuthJwtAccessPayloadDto, {
            user: data._id.toString(),
            type: data.role.type,
            role: data.role._id.toString(),
            roleIsActive: data.role.isActive,
            email: data.email?.toString(),
            loginDate: loginDate,
            organization: organization
        } as Partial<AuthJwtAccessPayloadDto>);
    }
    async createAccessToken(
        subject: string,
        payload: AuthJwtAccessPayloadDto
    ): Promise<string> {
        return this.helperEncryptionService.jwtEncrypt(
            { ...payload },
            {
                secretKey: this.jwtAccessTokenSecretKey,
                expiredIn: this.jwtAccessTokenExpirationTime,
                audience: this.jwtAudience,
                issuer: this.jwtIssuer,
                subject,
            }
        );
    }
}