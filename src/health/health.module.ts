import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { HealthController } from './controllers/health.controller';
import { HealthService } from './services/health.service';
import { MongooseModule } from '@nestjs/mongoose';
import { DATABASE_PRIMARY_CONNECTION_NAME } from 'src/common/database/constants/database.constant';
import { CachingModule } from 'src/common/caching/caching.module';

@Module({
  imports: [
    HttpModule,
    CachingModule,
    MongooseModule.forFeature([], DATABASE_PRIMARY_CONNECTION_NAME)
  ],
  controllers: [HealthController],
  providers: [HealthService]
})
export class HealthModule {}
