import { Injectable } from '@nestjs/common';
import { InjectConnection } from '@nestjs/mongoose';
import { Connection } from 'mongoose';
import { DATABASE_PRIMARY_CONNECTION_NAME } from 'src/common/database/constants/database.constant';

@Injectable()
export class HealthService {
    constructor(
        @InjectConnection(DATABASE_PRIMARY_CONNECTION_NAME) private readonly mongoConnection: Connection
    ) {}

    async checkHealth() {
        let status = 200;
        const data = {
            status: 'ok',
            timestamp: new Date().toISOString(),
            services: {
                database: {
                    status: 'unhealthy'
                },
                api: {
                    status: 'healthy'
                },
                redis: {
                    status: 'healthy'
                },
                messaging: {
                    status: 'healthy'
                }
            }
        };

        try {
            // Check MongoDB connection
            const isConnected = this.mongoConnection.readyState === 1;
            data.services.database.status = isConnected ? 'healthy' : 'unhealthy';
        } catch (error) {
            data.services.database.status = 'unhealthy';
            data.status = 'error';
        }

        // Check if any service is unhealthy
        const hasUnhealthyService = Object.values(data.services).some(
            service => service.status === 'unhealthy'
        );

        if (hasUnhealthyService) {
            data.status = 'error';
            status = 500;
        }

        return {
            status: status,
            data: data
        };
    }
}
