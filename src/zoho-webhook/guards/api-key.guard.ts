import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { webHookAPIkey } from '../schema/api-key.schema'
import * as crypto from 'crypto';

@Injectable()
export class ApiKeyGuard implements CanActivate {
  constructor(
    @InjectModel(webHookAPIkey.name)
    private readonly apiKeyModel: Model<webHookAPIkey>,
  ) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const receivedApiKey = request.headers['x-api-key'];

    if (!receivedApiKey) {
      throw new UnauthorizedException('Missing API key');
    }

    const hashedKey = crypto
      .createHash('sha256')
      .update(receivedApiKey)
      .digest('hex');

    const validApiKey = await this.apiKeyModel.findOne({
      hashedKey,
      status: 'active',
    });

    if (!validApiKey) {
      throw new UnauthorizedException('Invalid or revoked API key');
    }

    request.apiKeyRecord = validApiKey;
    request.apiKeyRecord = {
      organizationId: validApiKey.organizationId,
      facilityId: validApiKey.facilityId,
    };
    return true;
  }
}
