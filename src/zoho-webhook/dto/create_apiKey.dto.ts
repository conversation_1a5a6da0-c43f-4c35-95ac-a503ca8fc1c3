import { ApiProperty } from '@nestjs/swagger';
import { IsMongoId, IsNotEmpty, IsOptional, IsString, Length } from 'class-validator';

export class CreateApiKeyDto {
    @ApiProperty({
        description: "Organization Id",
        example: "673283423423dsdfsd3223",
    })
    @IsNotEmpty()
    @IsString()
    @IsMongoId()
    organizationId: string;

    @ApiProperty({
        description: "Facility Id of the organization",
        example: "673283423423dsdfsd3223",
    })
    @IsNotEmpty()
    @IsString()
    @IsMongoId()
    facilityId: string;
}
