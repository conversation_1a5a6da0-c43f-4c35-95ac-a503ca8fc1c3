import { Module } from '@nestjs/common';
import { ZohoWebhookController } from './controller/zoho.controller';
import { ZohowebhookService } from './services/zoho-service/zoho-webhook.service';
import { ClientLead, ClientLeadSchema } from './schema/zoho-webhook.schema';
import { ApiKeyService } from './services/api-key.service';
import { webHookAPIkey, ApiKeySchema } from './schema/api-key.schema';
import { ApiKeyController } from './controller/api.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthModule } from 'src/auth/auth.module';
import { clientLeadController } from './controller/client-lead.controller';
import { ClientLeadService } from './services/client-lead.service';
import { UtilsModule } from 'src/utils/utils.module';

@Module({
  imports: [
    AuthModule,
    UtilsModule,

    MongooseModule.forFeature([
      { name: ClientLead.name, schema: ClientLeadSchema },
      { name: webHookAPIkey.name, schema: ApiKeySchema }
    ]),
  ],
  controllers: [ZohoWebhookController, ApiKeyController, clientLeadController],
  providers: [ZohowebhookService, ApiKeyService, ClientLeadService],
  exports: [ZohowebhookService],
})
export class WebhookModule { }
