
import {
    Controller,
    Get,
    Post,
    Body,
    Param,
    Delete,
    Query,
    Put,
    Patch,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { AuthJwtAccessProtected } from 'src/auth/decorators/auth.jwt.decorator';
import { PolicyAbilityProtected } from 'src/policy/decorators/policy.decorator';
import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { Response, ResponsePaging } from 'src/common/response/decorators/response.decorator';

import {
    PromotionCreateDoc,
    PromotionListDoc,
    PromotionGetDoc,
    PromotionUpdateDoc,
    PromotionDeleteDoc,
    PromotionApplyDoc,
    PromotionGetByTypeDoc,
} from '../docs/promotion.doc';
import { PromotionService } from '../services/promotion.service';
import { CreatePromotionDto } from '../dto/create-promotion.dto';
import { UpdatePromotionDto, UpdatePromotionStatusDto } from '../dto/update-promotion.dto';
import { ApplyPromotionDto, ApplyPromotionToItemsDto } from '../dto/apply-promotion.dto';
import { GetPromotionsDto } from '../dto/get-promotions.dto';
import { MongoIdPipeTransform } from 'src/common/database/pipes/mongo-id.pipe';
import { IDatabaseObjectId } from 'src/common/database/interfaces/database.objectid.interface';
import { PROMOTION_PRICING_DEFAULT_AVAILABLE_SEARCH, PROMOTION_PRODUCT_DEFAULT_AVAILABLE_SEARCH } from '../constants/promotion.constant';
import { GetPromotionPricingDto } from '../dto/get-promotion.pricing.dto';
import { PaginationService } from 'src/common/pagination/services/pagination.service';
import { GetOrganizationId } from 'src/organization/decorators/organization.decorator';
import { GetItemPromotionsDto } from '../dto/get-item-promotions.dto';
import { ENUM_PRODUCT_ITEM_TYPE } from '../enums/item-type.enum';
import { PaginationQuery } from 'src/common/pagination/decorators/pagination.decorator';
import { PaginationListDto } from 'src/common/pagination/dtos/pagination.list.dto';
import { ENUM_PAGINATION_ORDER_DIRECTION_TYPE } from 'src/common/pagination/enums/pagination.enum';
import { GetPromotionProductDto } from '../dto/get-promotion-product.dto';
import { GetProductPromotionsAllDto } from '../dto/get-product-promotions-all.dto';

@ApiTags('modules.promotions')
@ApiBearerAuth()
@Controller('promotions')
export class PromotionController {
    constructor(
        private readonly promotionService: PromotionService,
        private readonly paginationService: PaginationService
    ) { }

    @PromotionCreateDoc()
    @Response('promotion.create')
    @Post('/create')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PROMOTION_WRITE)
    @AuthJwtAccessProtected()
    async create(
        @Body() createPromotionDto: CreatePromotionDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ) {

        const data = await this.promotionService.createNewPromotion(
            organizationId,
            createPromotionDto
        );

        return {
            message: 'Promotion created successfully',
            data
        };
    }

    @PromotionListDoc()
    @ResponsePaging('promotion.list')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PROMOTION_READ)
    @AuthJwtAccessProtected()
    @Get('/list')
    async findAll(

        @GetOrganizationId() organizationId: IDatabaseObjectId,
        @Query() query: GetPromotionsDto
    ) {
        const {
            page,
            perPage,
            search,
            type,
            target,
            isActive,
            orderBy,
            orderDirection,
            startDate,
            endDate,
            facilityIds,
            pricingId,
            includePricing,
            itemType
        } = query;
        let _limit = query.perPage ?? query.perPage
        const facilityId = facilityIds?.split(',')?.map((id: string) => new Types.ObjectId(id)) ?? [];

        const _offset = this.paginationService.offset(Number(page), Number(_limit));
        const _search = this.paginationService.search(search, ['name']);
        const _order = this.paginationService.order(orderBy, orderDirection, ['updatedAt', 'name']);
        let find: Record<string, any> = {
            organizationId: organizationId
        };
        if (type) find.type = type;
        if (itemType) find.itemType = itemType;
        if (target) find.target = target;
        if (isActive !== undefined) find.isActive = isActive;
        if (search) find = { ...find, ..._search };
        if (startDate) find.startDate = { $gte: startDate };
        if (endDate) find.endDate = { $lte: endDate };
        if (facilityId?.length) {
            // Return promotions for this facility or promotions with no facility restriction
            find.$or = [
                { facilityIds: { $in: facilityId } },
            ];
        }

        // Add pricing ID filter if provided
        let pricingFilter = null;
        if (pricingId) {
            pricingFilter = {
                pricingId: new Types.ObjectId(pricingId),
                includePricing: includePricing !== undefined ? includePricing : true
            };
        }

        const { data, total } = await this.promotionService.getPromotions(
            find,
            {
                paging: {
                    limit: _limit,
                    offset: _offset,
                },
                order: _order
            },
            pricingFilter
        );

        return {
            message: 'Promotions retrieved successfully',
            data,
            _pagination: {
                total,
                page: page,
                limit: _limit,
                totalPages: this.paginationService.totalPage(total, _limit)
            }
        };
    }

    @PromotionGetDoc()
    @Response('promotion.get')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PROMOTION_READ)
    @AuthJwtAccessProtected()
    @Get(':id/get')
    async findOne(
        @Param('id') id: string
    ) {
        const data = await this.promotionService.findOneById(
            new Types.ObjectId(id),
            {
                join: [
                    {
                        path: 'facilityIds',
                        select: 'facilityName',
                    }
                ]
            }
        );

        return {
            message: 'Promotion retrieved successfully',
            data
        };
    }

    @PromotionUpdateDoc()
    @Response('promotion.update')
    @Put(':id/update')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PROMOTION_UPDATE)
    @AuthJwtAccessProtected()
    async update(
        @GetOrganizationId() organizationId: IDatabaseObjectId,
        @Param('id') id: string,
        @Body() updatePromotionDto: UpdatePromotionDto
    ) {
        const data = await this.promotionService.updatePromotion(
            new Types.ObjectId(id),
            organizationId,
            updatePromotionDto
        );

        return {
            message: 'Promotion updated successfully',
            data
        };
    }

    @PromotionDeleteDoc()
    @Response('promotion.delete')
    @Delete(':id/delete')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PROMOTION_DELETE)
    @AuthJwtAccessProtected()
    async remove(
        @Param('id') id: string,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ) {
        await this.promotionService.delete(
            new Types.ObjectId(id),
            organizationId
        );

        return {
            message: 'Promotion deleted successfully'
        };
    }

    @Response('promotion.status')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PROMOTION_UPDATE)
    @AuthJwtAccessProtected()
    @Patch(':id/status')
    async status(
        @Param('id', MongoIdPipeTransform) id: IDatabaseObjectId,
        @GetOrganizationId() organizationId: IDatabaseObjectId,
        @Body() updatePromotionDto: UpdatePromotionStatusDto
    ) {
        await this.promotionService.updatePromotionStatus(
            id,
            organizationId,
            updatePromotionDto.isActive
        );

        return {
            data: true
        };


    }

    @PromotionApplyDoc()
    @ApiOperation({ summary: "Apply/Remove promotions to a items" })
    @Response('promotion.apply')
    @Post('/:itemId/apply-to-items')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PROMOTION_APPLY)
    @AuthJwtAccessProtected()
    async applyPromotionToItems(
        @GetOrganizationId() organizationId: IDatabaseObjectId,
        @Body() body: ApplyPromotionToItemsDto,
        @Param('itemId', MongoIdPipeTransform) itemId: IDatabaseObjectId
    ) {
        await this.promotionService.applyPromotionsToItem(
            organizationId,
            body,
            {
                itemType: ENUM_PRODUCT_ITEM_TYPE.SERVICE,
                _id: itemId,
                price: 0
            }
        );

        return {
            message: 'Promotion applied successfully'
        };
    }

    @Response("promotion.delete")
    @Delete(':promotionId/delete')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PROMOTION_DELETE)
    @AuthJwtAccessProtected()
    async removePromotion(
        @GetOrganizationId() organizationId: IDatabaseObjectId,
        @Param('promotionId') promotionId: string,
        @Query('itemId') itemId?: string,
        @Query('categoryId') categoryId?: string
    ) {
        await this.promotionService.removePromotion(
            organizationId,
            promotionId,
            itemId,
            categoryId
        );

        return {
            message: 'Promotion removed successfully'
        };
    }

    // @PromotionGetByTypeDoc()
    @ResponsePaging('promotion.pricing-list')
    @Get('/pricing-list')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PROMOTION_READ)
    @AuthJwtAccessProtected()
    async getByItemType(
        @GetOrganizationId() organizationId: IDatabaseObjectId,
        @Query() query: GetPromotionPricingDto,
    ) {
        const { page, search, promotionId, facilityIds, classType, itemType } = query;
        console.log(itemType)
        let _limit = query.perPage ?? query.pageSize
        const facilityId = facilityIds?.split(',')?.map(id => new Types.ObjectId(id)) ?? [];
        const _search = this.paginationService.search(search, PROMOTION_PRICING_DEFAULT_AVAILABLE_SEARCH);
        const _offset = this.paginationService.offset(Number(page), Number(_limit));

        const { data, total } = await this.promotionService.getPromotionPricingSetting(
            {
                search: _search,
                classType: classType,
                serviceId: query.serviceId,
                organizationId: organizationId,
                promotionId: promotionId,
                facilityId: facilityId,
            },
            {
                paging: {
                    limit: _limit,
                    offset: _offset,
                },
            }
        );
        const totalPage: number = this.paginationService.totalPage(
            total,
            _limit
        );
        return {
            __pagination: { total: total, totalPage: totalPage, page: page },
            data: data
        };
    }

    @ResponsePaging('promotion.list')
    @AuthJwtAccessProtected()
    @Get('/item/:itemId/discounts')
    async getPricingDiscounts(
        @PaginationQuery({
            defaultOrderBy: 'name',
            defaultOrderDirection: ENUM_PAGINATION_ORDER_DIRECTION_TYPE.ASC,
            availableOrderBy: ['name'],
            availableSearch: ['name']
        }) { _limit, _offset, _order, _search }: PaginationListDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId,
        @Param('itemId', MongoIdPipeTransform) itemId: IDatabaseObjectId,
        @Query() query: GetItemPromotionsDto
    ) {
        const { page, search, type, target, isActive, startDate, endDate = new Date(), facilityIds } = query;
        const facilityId = facilityIds?.split(',')?.map(id => new Types.ObjectId(id)) ?? [];
        // const _offset = this.paginationService.offset(page, _limit);
        // const _search = this.paginationService.search(search, ['name']);

        const { data, total } = await this.promotionService.getPricingDiscounts(
            {
                organizationId,
                itemId: new Types.ObjectId(itemId),
                type,
                target,
                isActive,
                search: _search,
                startDate,
                endDate,
                facilityId
            },
            {
                paging: {
                    limit: _limit,
                    offset: _offset,
                }
            }
        );

        return {
            message: 'Pricing discounts retrieved successfully',
            data,
            _pagination: {
                total,
                page,
                limit: _limit,
                totalPages: this.paginationService.totalPage(total, _limit)
            }
        };
    }
    @ResponsePaging('promotion.product-list')
    @Get('/product-list')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PROMOTION_READ)
    @AuthJwtAccessProtected()
    async getProductListForPromotion(
        @GetOrganizationId() organizationId: IDatabaseObjectId,
        @Query() query: GetPromotionProductDto,
    ) {
        const {
            page,
            search,
            promotionId,
            facilityIds,
            productType,
            brandIds,
            categoryIds,
        } = query;
        console.log(query)
        // page size compatibility (perPage | pageSize)
        const _limit = Number(query.perPage ?? query.pageSize ?? 10);

        // CSV -> ObjectId[] for facilityIds (same style as pricing-list)
        const facilityId =
            facilityIds?.split(',')?.filter(Boolean).map((id) => new Types.ObjectId(id)) ?? [];

        // CSV -> string[] for brand & category (service layer converts to ObjectId)
        const brandId = brandIds?.split(',')?.filter(Boolean) ?? [];
        const categoryId = categoryIds?.split(',')?.filter(Boolean) ?? [];

        const _search = this.paginationService.search(
            search,
            PROMOTION_PRODUCT_DEFAULT_AVAILABLE_SEARCH,
        );
        const _offset = this.paginationService.offset(Number(page), _limit);

        const { data, total } = await this.promotionService.getPromotionAttachedProducts(
            {
                organizationId,
                promotionId,
                facilityId,
            },

        );

        const totalPage: number = this.paginationService.totalPage(total, _limit);

        return {
            __pagination: { total, totalPage, page },
            data,
        };
    }
    @Get('/product-promotions/all')
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PROMOTION_READ)
    @AuthJwtAccessProtected()
    async getProductPromotionsAll(
        @GetOrganizationId() organizationId: IDatabaseObjectId,
        @Query() query: GetProductPromotionsAllDto,
    ) {
        const facilityId = query.facilityIds
            ? query.facilityIds.split(',').filter(Boolean).map((id) => new Types.ObjectId(id))
            : [];

        const { data, total } = await this.promotionService.getProductPromotionsAll({
            organizationId,
            productId: new Types.ObjectId(query.productId),
            variantId: query.variantId ? new Types.ObjectId(query.variantId) : undefined,
            type: query.type as any,
            target: query.target as any,
            isActive: query.isActive === undefined ? undefined : ['true', '1', true].includes(query.isActive as any),
            startDate: query.startDate ? new Date(query.startDate) : undefined,
            endDate: query.endDate ? new Date(query.endDate) : undefined,
            facilityId,
        });

        return { data, total };
    }
}
