
import { ApiProperty, OmitType } from '@nestjs/swagger';
import { IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { ENUM_PRODUCT_ITEM_TYPE } from '../enums/item-type.enum';
import { Transform } from 'class-transformer';
import { PaginationListDto } from 'src/common/pagination/dtos/pagination.list.dto';
import { Types } from 'mongoose';
import { ClassType } from 'src/utils/enums/class-type.enum';

export class GetPromotionPricingDto extends OmitType(PaginationListDto, ['orderBy', 'orderDirection']) {
    @ApiProperty({
        description: 'Type of item',
        example: ENUM_PRODUCT_ITEM_TYPE.SERVICE,
        required: true,
    })
    @IsOptional()
    @IsEnum(ENUM_PRODUCT_ITEM_TYPE, { message: 'Invalid item type' })
    itemType: ENUM_PRODUCT_ITEM_TYPE

    @ApiProperty({
        description: 'The type of service',
        example: 'personal-appointment',
        required: true,
    })
    @IsOptional()
    @IsString()
    @IsEnum(ClassType, { message: 'Invalid service type' })
    classType?: ClassType;

    @ApiProperty({
        description: 'The ID of the service',
        example: '60d21b4667d0d8992e610c85',
        required: true,
    })
    @IsOptional()
    @IsMongoId()
    serviceId?: string;

    @ApiProperty({
        description: 'The ID of the promotion',
        example: '60d21b4667d0d8992e610c85',
        required: true,
    })
    @IsOptional()
    @IsMongoId()
    promotionId?: string;

    @ApiProperty({
        description: 'Filter by facility ID (returns promotions for this facility or promotions with no facility restriction)',
        example: '60d21b4667d0d8992e610c85,60d21b4667d0d8992e610c85',
        required: false,
    })
    @IsMongoId()
    @IsOptional()
    facilityIds?: string;
}
