// dtos/get-promotion-product.dto.ts
import { IsOptional, IsString } from 'class-validator';

export class GetPromotionProductDto {
  @IsOptional() @IsString() page?: number | string;     // keep parity with your existing DTO style
  @IsOptional() @IsString() perPage?: number | string;
  @IsOptional() @IsString() pageSize?: number | string;

  @IsOptional() @IsString() search?: string;
  @IsOptional() @IsString() promotionId?: string;

  // CSV lists
  @IsOptional() @IsString() facilityIds?: string;
  @IsOptional() @IsString() brandIds?: string;
  @IsOptional() @IsString() categoryIds?: string;

  // filters
  @IsOptional() @IsString() productType?: 'simple' | 'variable';
}
