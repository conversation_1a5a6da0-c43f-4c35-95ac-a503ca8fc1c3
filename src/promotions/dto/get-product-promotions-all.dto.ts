// dto/get-product-promotions-all.dto.ts
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class GetProductPromotionsAllDto {
  @IsString() @IsNotEmpty()
  productId!: string;

  @IsOptional() @IsString()
  variantId?: string;

  @IsOptional() @IsString()
  type?: string;   // DiscountType

  @IsOptional() @IsString()
  target?: string; // ENUM_PROMOTION_TARGET

  @IsOptional() @IsString()
  isActive?: string; // 'true' | 'false'

  @IsOptional() @IsString()
  startDate?: string;

  @IsOptional() @IsString()
  endDate?: string;

  // CSV of facility ids to respect Promotion.facilityIds scoping
  @IsOptional() @IsString()
  facilityIds?: string;
}
