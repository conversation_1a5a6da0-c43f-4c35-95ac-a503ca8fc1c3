import { Injectable } from '@nestjs/common';
import { Model, PopulateOptions } from 'mongoose';
import { DatabaseRepositoryBase } from 'src/common/database/bases/database.repository';
import { InjectDatabaseModel } from 'src/common/database/decorators/database.decorator';
import {
    PromotionDocument,
    PromotionEntity,
} from 'src/promotions/repository/entities/promotion.entity';

@Injectable()
export class PromotionRepository extends DatabaseRepositoryBase<
    PromotionEntity,
    PromotionDocument
> {

    constructor(
        @InjectDatabaseModel(PromotionEntity.name)
        private readonly promotionModel: Model<PromotionEntity>
    ) {
        super(promotionModel, [
            {
                path: 'facilityIds',
                localField: 'facilityIds',
                foreignField: '_id',
                model: "Facility",
                justOne: false,
            },
        ]);

    }
}
