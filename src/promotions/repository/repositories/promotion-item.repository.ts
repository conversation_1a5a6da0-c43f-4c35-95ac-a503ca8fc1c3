import { Injectable } from '@nestjs/common';
import { Model, PopulateOptions } from 'mongoose';
import { DatabaseRepositoryBase } from 'src/common/database/bases/database.repository';
import { InjectDatabaseModel } from 'src/common/database/decorators/database.decorator';
import {
    PromotionItemDocument,
    PromotionItemEntity,
} from 'src/promotions/repository/entities/promotion-item.entity';
import { PromotionEntity } from '../entities/promotion.entity';

@Injectable()
export class PromotionItemRepository extends DatabaseRepositoryBase<
    PromotionItemEntity,
    PromotionItemDocument
> {
    readonly _joinActive: PopulateOptions[] = [
        {
            path: 'promotionId',
            localField: 'promotionId',
            foreignField: '_id',
            model: PromotionEntity.name,
            match: {
                isActive: true,
            },
        },
    ];

    constructor(
        @InjectDatabaseModel(PromotionItemEntity.name)
        private readonly promotionItemModel: Model<PromotionItemEntity>
    ) {
        super(promotionItemModel, [
            {
                path: 'promotionId',
                localField: 'promotionId',
                foreignField: '_id',
                model: PromotionEntity.name,
                match: {
                    isActive: true,
                },
            },
        ]);
    }
}
