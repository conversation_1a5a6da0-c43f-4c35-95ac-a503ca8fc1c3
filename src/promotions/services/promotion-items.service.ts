import { Injectable, NotFoundException } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { Document, Types } from 'mongoose';
import {
    IDatabaseCreateOptions,
    IDatabaseFindAllOptions,
    IDatabaseGetTotalOptions,
    IDatabaseCreateManyOptions,
    IDatabaseSaveOptions,
    IDatabaseDeleteManyOptions,
    IDatabaseFindOneOptions,
    IDatabaseOptions,
} from 'src/common/database/interfaces/database.interface';
import { IPaginationOptions } from 'src/common/pagination/interfaces/pagination.interface';
import { PipelineStage } from 'mongoose';
import { IDatabaseObjectId } from 'src/common/database/interfaces/database.objectid.interface';
import { PromotionItemDocument, PromotionItemEntity } from '../repository/entities/promotion-item.entity';
import { PromotionItemRepository } from '../repository/repositories/promotion-item.repository';

@Injectable()
export class PromotionItemService {
    constructor(
        private readonly promotionItemRepository: PromotionItemRepository,
    ) {}

    async findAll(
        find?: Record<string, any>,
        options?: IDatabaseFindAllOptions
    ): Promise<PromotionItemDocument[]> {
        return this.promotionItemRepository.findAll(find, options);
    }

    async getTotal(
        find?: Record<string, any>,
        options?: IDatabaseGetTotalOptions
    ): Promise<number> {
        return this.promotionItemRepository.getTotal(find, options);
    }

    async findOneById(
        _id: Types.ObjectId,
        options?: IDatabaseFindOneOptions
    ): Promise<PromotionItemDocument> {
        return this.promotionItemRepository.findOneById(_id, options);
    }

    async findOne(
        find: Record<string, any>,
        options?: IDatabaseFindOneOptions
    ): Promise<PromotionItemDocument> {
        return this.promotionItemRepository.findOne(find, options);
    }

    async findOneByName(
        name: string,
        options?: IDatabaseFindOneOptions
    ): Promise<PromotionItemDocument> {
        return this.promotionItemRepository.findOne({ name }, options);
    }

    async findOneByType(
        type: string,
        options?: IDatabaseFindOneOptions
    ): Promise<PromotionItemDocument> {
        return this.promotionItemRepository.findOne({ type }, options);
    }

    async findOneActiveById(
        _id: string,
        options?: IDatabaseFindOneOptions
    ): Promise<PromotionItemDocument> {
        return this.promotionItemRepository.findOne({ _id, isActive: true }, options);
    }

    async existByName(
        name: string,
        options?: IDatabaseOptions
    ): Promise<boolean> {
        return this.promotionItemRepository.exists(
            {
                name,
            },
            options
        );
    }

    async create(
        data: Record<string, any>,
        options?: IDatabaseCreateOptions
    ): Promise<PromotionItemDocument> {
        return this.promotionItemRepository.create<PromotionItemDocument>(data as any, options);
    }

    async createMany(
        data: Partial<PromotionItemEntity>[],
        options?: IDatabaseCreateManyOptions
    ): Promise<boolean> {
        const creates: PromotionItemEntity[] = data.map((item) => {
            const entity = new PromotionItemEntity();
            Object.assign(entity, item);
            return entity;
        });

        await this.promotionItemRepository.createMany<PromotionItemEntity>(creates, options);
        return true;
    }

    async deleteOne(
        find: Record<string, any>,
        options?: IDatabaseDeleteManyOptions
    ): Promise<boolean> {
        await this.promotionItemRepository.delete(find, options);
        return true;
    }

    async deleteMany(
        find: Record<string, any>,
        options?: IDatabaseDeleteManyOptions
    ): Promise<boolean> {
        await this.promotionItemRepository.deleteMany(find, options);
        return true;
    }

    async updateOne(
        find: Record<string, any>,
        data: Partial<PromotionItemEntity>,
        options?: IDatabaseSaveOptions
    ): Promise<PromotionItemDocument> {
        return this.promotionItemRepository.update(find, data, options);
    }

    async updateMany(
        find: Record<string, any>,
        data: PromotionItemEntity,
        options?: IDatabaseSaveOptions
    ): Promise<boolean> {
        await this.promotionItemRepository.updateMany<PromotionItemEntity>(find, data, options);
        return true;
    }

    async aggregate<T>(
        pipeline: PipelineStage[],
        options?: IDatabaseOptions
    ): Promise<T[]> {
        return this.promotionItemRepository.aggregate<any>(pipeline, options);
    }

    async active(
        repository: PromotionItemDocument,
        options?: IDatabaseSaveOptions
    ): Promise<PromotionItemDocument> {
        repository.isActive = true;
        return this.promotionItemRepository.save(repository, options);
    }

    async inactive(
        repository: PromotionItemDocument,
        options?: IDatabaseSaveOptions
    ): Promise<PromotionItemDocument> {
        repository.isActive = false;
        return this.promotionItemRepository.save(repository, options);
    }

    async softDelete(
        repository: PromotionItemDocument,
        options?: IDatabaseSaveOptions
    ): Promise<PromotionItemDocument> {
        repository.deletedAt = new Date();
        return this.promotionItemRepository.save(repository, options);
    }
}
