import { Injectable, NotFoundException, PipeTransform } from '@nestjs/common';
import { ENUM_PROMOTION_STATUS_CODE_ERROR } from 'src/promotions/enums/promotion.status-code.enum';
import { PromotionDocument } from 'src/promotions/repository/entities/promotion.entity';
import { PromotionService } from 'src/promotions/services/promotion.service';

@Injectable()
export class PromotionParsePipe implements PipeTransform {
    constructor(private readonly promotionService: PromotionService) {}

    async transform(value: any): Promise<PromotionDocument> {
        const promotion: PromotionDocument = await this.promotionService.findOneById(value);
        if (!promotion) {
            throw new NotFoundException({
                statusCode: ENUM_PROMOTION_STATUS_CODE_ERROR.NOT_FOUND,
                message: 'promotion.error.notFound',
            });
        }

        return promotion;
    }
}
