import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, SchemaTypes } from 'mongoose';

@Schema({
    versionKey: false,
    timestamps: false
})
export class Address extends Document {
    @Prop({ type: String, required: false })
    addressLine1?: string;

    @Prop({ type: String, required: false })
    addressLine2?: string;

    @Prop({ type: Number, required: false })
    postalCode?: number;

    @Prop({ type: SchemaTypes.ObjectId, ref: "Cities", required: false })
    city?: string;

    @Prop({ type: SchemaTypes.ObjectId, ref: "State", required: false })
    state?: string;

    @Prop({ type: String, required: false })
    country?: string;

    @Prop({ type: Boolean, default: false })
    isDefault?: boolean;
}

export const AddressSchema = SchemaFactory.createForClass(Address);
export type AddressDocument = Address & Document;
