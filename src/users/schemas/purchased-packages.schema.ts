import { InternalServerErrorException } from "@nestjs/common";
import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, SchemaTypes, Types } from "mongoose";
import { ENUM_PRODUCT_ITEM_TYPE } from "src/promotions/enums/item-type.enum";
import { PaymentStatus } from "src/utils/enums/payment.enum";
import { SessionType } from "src/utils/enums/session-type.enum";

@Schema({ timestamps: true, versionKey: false })
export class Suspensions extends Document {

    @Prop({ type: Boolean, required: false, default: undefined })
    isResumed?: boolean; // This is used to check if the suspension has been resumed, If it is not true and time elapsed then the package considered as resumed

    @Prop({ type: Date, required: true })
    fromDate: Date;

    @Prop({ type: Date, required: true })
    endDate: Date;

    @Prop({ type: String, required: false, default: "" })
    notes: string;
}
export const SuspensionsSchema = SchemaFactory.createForClass(Suspensions);

/**
 * This is the schema for the purchased packages
 */
@Schema({ timestamps: true })
export class Purchase {

    @Prop({ type: SchemaTypes.ObjectId, required: false, ref: "User", index: true })
    invoiceId: string;

    /**
     * User who is the primary user of the package or Owner of the package
     * Cannot be change
     */
    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User", index: true })
    userId: string;

    /**
     * User who is the sponsor of the package or the user who is paying for the package
     */
    @Prop({ type: SchemaTypes.ObjectId, required: false, ref: "User", default: null, index: true })
    sponsorUser?: string;

    /**
     * Users who are the consumers of the package or the users who are using the package
     * Consumers will always have `userId` as one of the primary consumer and cannot removed
     */
    @Prop({ type: [SchemaTypes.ObjectId], index: true, required: true, ref: "User" })
    consumers?: string[];

    /**
     * Users who are removed from the package
     * This can be use to track the users who are removed from the package and restore session if user is removed after booking
     */
    @Prop({ type: [SchemaTypes.ObjectId], index: true, required: false, ref: "User" })
    removedConsumers?: string[];

    /**
     * Pricing of the package
     */
    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: 'Pricing', index: true })
    packageId: string;

    /**
     * Type of item
     */
    @Prop({ type: String, required: true, enum: ENUM_PRODUCT_ITEM_TYPE, index: true })
    itemType: ENUM_PRODUCT_ITEM_TYPE;

    /**
     * Bundled pricing id if the package is a bundled package
     */
    @Prop({ type: SchemaTypes.ObjectId, required: false, ref: 'Pricing' })
    bundledPricingId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    organizationId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: 'Facility', index: true })
    facilityId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: 'User' })
    purchasedBy: string;

    @Prop({ type: SchemaTypes.ObjectId, required: false, default: null, ref: 'membership' })
    membershipId: string;

    @Prop({ type: Number, required: function () { return this.itemType === ENUM_PRODUCT_ITEM_TYPE.VOUCHER; }, default: undefined })
    voucherAmount?: number;

    @Prop({ type: Number, required: false, default: 0 })
    amountConsumed?: number;

    @Prop({ type: Date, required: true, index: true })
    purchaseDate: Date;

    @Prop({ type: String, required: true, enum: PaymentStatus })
    paymentStatus: PaymentStatus;

    @Prop({ type: Boolean, required: true })
    isExpired: boolean;

    @Prop({ type: Boolean, required: false, default: false })
    sharePass: boolean;

    /**
     * User who shared the pass
     */
    @Prop({ type: SchemaTypes.ObjectId, required: false, ref: "User", default: null })
    sharedBy: string;

    @Prop({ type: Boolean, required: false, default: false })
    isExchanged: boolean;

    @Prop({ type: SchemaTypes.ObjectId, required: false, ref: "invoices" })
    exchangedInvoiceId?: string;

    @Prop({ type: Date, required: false })
    exchangeDate?: Date;

    @Prop({
        enum: SessionType,
        required: function () {
            return this.itemType === ENUM_PRODUCT_ITEM_TYPE.SERVICE;
        }
    })
    sessionType: SessionType;

    @Prop({
        type: Number, required: function () {
            return this.itemType === ENUM_PRODUCT_ITEM_TYPE.SERVICE;
        }
    })
    totalSessions: number;

    @Prop({
        type: Number, required: function () {
            return this.itemType === ENUM_PRODUCT_ITEM_TYPE.SERVICE;
        }, default: 0
    })
    sessionConsumed?: number;

    @Prop({ type: Number, required: false, default: 0 })
    sessionShared?: number; // It is only to store how much session is shared 

    @Prop({ type: Number, required: false })
    sessionPerDay?: number;

    @Prop({ type: Number, required: false })
    dayPassLimit?: number;

    @Prop({ type: Boolean, required: false, default: true })
    isActive?: boolean;

    @Prop({ type: Date, required: true, index: true })
    startDate: Date;

    @Prop({ type: Date, required: true, index: true })
    endDate: Date;

    @Prop({ type: [SuspensionsSchema], required: false, default: [] })
    suspensions?: Suspensions[];

    @Prop({ type: String, required: false })
    qrCodeUrl: String;


}

export type PurchaseDocument = Purchase & Document;
export const PurchaseSchema = SchemaFactory.createForClass(Purchase);


PurchaseSchema.pre('save', function (next) {
    /**
     * - If consumers are not provided, then the user who is purchasing the package is the consumer
     * - If the user is modified, then the user should be added to the consumers
     */

    if (this.isModified("consumers") || !this.consumers || !this.consumers?.length) {
        this.consumers = [
            ...new Set([
                this.userId,
                ...this.consumers || []].map(c => c.toString()))
        ];

        // Remove consumers from removed consumers
        this.removedConsumers = [...new Set([
            ...(this.removedConsumers || []).map(c => c.toString()).filter(c => !this.consumers?.includes(c))
        ]
        )];

    } else if (this.isNew) {
        this.consumers = [this.userId];
    }

    if (!this.consumers.length) {
        throw new InternalServerErrorException("Package must have at least one consumer user");
    }
    next();
});


PurchaseSchema.post(/^find/ as any, function () {
    /**
     * - If consumers are not provided, then the user who is purchasing the package is the consumer
     * - If the user is modified, then the user should be added to the consumers
     */
    this.consumers = this.consumers?.length ? this.consumers : [this.userId];
});