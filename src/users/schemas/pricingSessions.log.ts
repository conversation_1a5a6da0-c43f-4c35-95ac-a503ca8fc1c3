import { Schema, Prop, SchemaFactory } from '@nestjs/mongoose';
import { Document, SchemaTypes } from 'mongoose';
import { SessionType } from 'src/utils/enums/session-type.enum';

export type PricingSessionLogsDocument = PricingSessionLogs & Document;

@Schema({ timestamps: true })
export class PricingSessionLogs {

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: 'User' })
    userId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: 'User' })
    organizationId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: 'Facility' })
    facilityId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: 'purchases' })
    purchaseId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: 'Pricing' })
    packageId: string;

    @Prop({ enum: SessionType, required: true })
    sessionType: SessionType;

    @Prop({ type: String, default: '' })
    notes?: string;

    @Prop({ type: SchemaTypes.ObjectId, ref: 'User' })
    modifiedBy?: string;

    @Prop({
        type: {
            startDate: { type: Date, required: true },
            endDate: { type: Date, required: true },
            totalSessions: { type: String, required: true },
        },
        required: true,
    })
    previousValues: {
        startDate: Date;
        endDate: Date;
        totalSessions: string;
    };

    @Prop({
        type: {
            startDate: { type: Date, required: true },
            endDate: { type: Date, required: true },
            totalSessions: { type: String, required: true },
        },
        required: true,
    })
    updatedValues: {
        startDate: Date;
        endDate: Date;
        totalSessions: string;
    };

}

export const PricingSessionLogsSchema = SchemaFactory.createForClass(PricingSessionLogs);