import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, SchemaTypes } from "mongoose";
import { Gender } from "src/utils/enums/gender.enum";
import { ActivityLevel } from "src/utils/enums/activity-level.enum";
import { Goal } from "src/utils/enums/goal.enum";

export class Measurement {
    @Prop({ type: String })
    chest?: string;

    @Prop({ type: String })
    shoulder?: string;

    @Prop({ type: String })
    arm?: string;

    @Prop({ type: String })
    bicep?: string;

    @Prop({ type: String })
    forearm?: string;

    @Prop({ type: String })
    waist?: string;

    @Prop({ type: String })
    hip?: string;

    @Prop({ type: String })
    thigh?: string;

    @Prop({ type: String })
    calf?: string;
}

export class UserFitnessProfile {
    @Prop({ type: String, enum: Gender })
    gender?: string;

    @Prop({ type: String })
    age?: string;

    @Prop({ type: String })
    weight?: string;

    @Prop({ type: String })
    height?: string;

    @Prop({ type: String, enum: Goal })
    goal?: string;

    @Prop({ type: String, enum: ActivityLevel })
    activityLevel?: string;

    @Prop({ type: Measurement })
    measurement?: Measurement;
}

export class Address {
    @Prop({ type: String })
    street?: string;

    @Prop({ type: String })
    city?: string;

    @Prop({ type: String })
    state?: string;

    @Prop({ type: String })
    country?: string;

    @Prop({ type: Number })
    pincode?: number;
}

export type ClientProfileDocument = HydratedDocument<ClientProfileDetails>;
@Schema({ timestamps: true })
export class ClientProfileDetails {
    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    userId: string;

    @Prop({ type: String,required:true,unique:true})
    clientId: string;

    @Prop({ type: String })
    profilePicture?: string;

    @Prop({ type: Address })
    address?: Address;

    @Prop({ type: UserFitnessProfile })
    fitnessProfile?: UserFitnessProfile;

    @Prop({ type: Number })
    currentStep?: number;

    @Prop({ type: Number })
    totalStep?: number;
}

export const ClientProfileSchema = SchemaFactory.createForClass(ClientProfileDetails);
