import { applyDecorators, UseGuards } from '@nestjs/common';
import { UserGuard } from 'src/users/guards/user.guard';

/**
 * Decorator to mark required roles for service methods
 * @param roles List of roles required to execute the method
 * @returns A method decorator.
 * @deprecated Use PolicyRoleGuard or PolicyAbilityGuard instead.
 */
export function UserProtected(): MethodDecorator {
    return applyDecorators(UseGuards(UserGuard));
}
