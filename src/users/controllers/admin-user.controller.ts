import { Body, Controller, Get, Param, Post, UseGuards } from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { RolesGuard } from "src/auth/roles.guard";
import { Roles } from "src/auth/decorators/roles.decorator";
import { UserService } from "../services/user.service";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { ApiBearerAuth, ApiTags, ApiOperation, ApiResponse, ApiBody } from "@nestjs/swagger";
import { User } from "../schemas/user.schema";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { CreateUserDto } from "../dto/user-create-profile.dto";
import { PaginationDTO } from "../dto/pagination.dto";


@ApiTags("Admin-User")
@ApiBearerAuth()
@Controller("admin/user")
export class AdminUserController {
    constructor(private userService: UserService) {}

    @Get("/profile/:id")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN)
    @ApiOperation({ summary: "Fetch user profile details by Id" })
    async getUserDetailsById(@Param("id") id: string): Promise<any> {
        try {
            const user = await this.userService.adminGetUserDetailsById(id);
            return user;
        } catch (error) {
            throw new Error(error.message);
        }
    }

    @Post("/get-all-users-list")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN)
    @ApiOperation({ summary: "Fetch all user list" })
    async getAllUserList(@Body() paginationDTO: PaginationDTO): Promise<any> {
        try {
            const user = await this.userService.adminGetAllUserList(paginationDTO);
            return user;
        } catch (error) {
            throw new Error(error.message);
        }
    }
}
