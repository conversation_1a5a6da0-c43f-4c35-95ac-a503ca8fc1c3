import { BadRequestException, Inject, Injectable, NotFoundException, PipeTransform } from '@nestjs/common';
import { Types } from 'mongoose';
import { UserService } from '../services/user.service';
import { ENUM_USER_STATUS_CODE_ERROR } from '../enums/user.status-code.enum';
import { UserDocument } from '../schemas/user.schema';
import { REQUEST } from '@nestjs/core';
import { IRequestApp } from 'src/common/request/interfaces/request.interface';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';

@Injectable()
export class GetUserPipe implements PipeTransform {
    constructor(private readonly userService: UserService) {}

    async transform(userId: string): Promise<UserDocument> {
        if (!Types.ObjectId.isValid(userId)) {
            throw new NotFoundException({
                statusCode: ENUM_USER_STATUS_CODE_ERROR.NOT_FOUND,
                message: 'user.error.invalidId',
            });
        }

        const user = await this.userService.findOneById(new Types.ObjectId(userId));
        
        if (!user) {
            throw new NotFoundException({
                statusCode: ENUM_USER_STATUS_CODE_ERROR.NOT_FOUND,
                message: 'user.error.notFound',
            });
        }

        return user;
    }
}
@Injectable()
export class UserNotSelfPipe implements PipeTransform {
    constructor(
        @Inject(REQUEST) private readonly request: IRequestApp,
        private readonly userService: UserService
    ) {}

    async transform(userId: string): Promise<UserDocument> {
        const { user } = this.request;

        // Check if the requested user is the same as the authenticated user
        if (user.user === userId && user.type !== ENUM_ROLE_TYPE.SUPER_ADMIN) {
            throw new BadRequestException({
                statusCode: ENUM_USER_STATUS_CODE_ERROR.USER_IS_SELF,
                message: 'Cannot perform action on self',
            });
        }

        const userDoc = await this.userService.findOneById(new Types.ObjectId(userId));
        
        if (!userDoc) {
            throw new NotFoundException({
                statusCode: ENUM_USER_STATUS_CODE_ERROR.NOT_FOUND,
                message: 'user.error.notFound',
            });
        }

        return userDoc;
    }
}