import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString } from "class-validator";

export class UploadProfilePictureDto {
  @ApiProperty({
    type: 'string',
    format: 'binary',
    description: 'Profile picture file (JPEG, PNG, etc.)',
    required: true,
  })
  @IsNotEmpty({ message: "Profile picture file is required" })
  @IsString({ message: "Invalid file format, expected a string representation of the file" })
  image: string;
}