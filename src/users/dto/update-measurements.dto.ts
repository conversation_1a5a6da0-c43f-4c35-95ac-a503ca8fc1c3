import { ApiProperty } from "@nestjs/swagger";
import { IsMongoId, IsObject } from "class-validator";

export class UpdateMeasurementsDto {
    @ApiProperty({
        description: "Id of the associated facility.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsMongoId({ message: "Invalid type of facility" })
    facilityId: string;

    @ApiProperty({
        description: "Basic Assessment details.",
        example: "{}",
        required: true,
    })
    @IsObject({ message: "Invalid Basic Assessment" })
    basicAssessments: Object;
}
