import { ApiProperty } from "@nestjs/swagger";
import {
    <PERSON><PERSON>rray,
    IsMongoId,
    IsNotEmpty,
    IsNumber,
    ValidateNested
} from "class-validator";
import { Type } from "class-transformer";

class ShareInstruction {
    @ApiProperty({
        description: "User ID to whom the pass is being shared",
        example: "659d268dee4b6081dacd41fd"
    })
    @IsMongoId({ message: "Invalid Receiver User ID (shareTo)" })
    shareTo: string;

    @ApiProperty({
        description: "ID of the associated package purchase",
        example: "659d268dee4b6081dacd41fd",
        required: true
    })
    @IsMongoId({ message: "Invalid Purchase ID" })
    purchaseId: string;

    @ApiProperty({
        description: "Number of sessions to be shared",
        example: 2
    })
    @IsNotEmpty({ message: "Number of sessions must be provided" })
    @IsNumber({}, { message: "Number of sessions must be a number" })
    noOfSessions: number;
}

export class SharePassMultipleDto {
    @ApiProperty({
        description: "User ID who is sharing the pass",
        example: "659d268dee4b6081dacd41fd"
    })
    @IsMongoId({ message: "Invalid Sender User ID (shareFrom)" })
    shareFrom: string;

    @ApiProperty({
        description: "Organization ID",
        example: "659d268dee4b6081dacd41fd"
    })
    @IsMongoId({ message: "Invalid Organization ID" })
    organizationId: string;

    @ApiProperty({
        description: "List of share instructions",
        type: [ShareInstruction]
    })
    @IsArray({ message: "Shares must be an array" })
    @ValidateNested({ each: true })
    @Type(() => ShareInstruction)
    shares: ShareInstruction[];
}