import { OmitType, ApiProperty } from "@nestjs/swagger";
import { GetInvoicesDto } from "./get-invoices.dto";
import { IsOptional, IsEnum } from "class-validator";

export class ExportInvoicesDto extends OmitType(GetInvoicesDto, [
    'search', 'page', 'pageSize'] as const) {
    
    @ApiProperty({
        description: "Response type for export",
        enum: ['string', 'stream'],
        default: 'stream'
    })
    @IsOptional()
    @IsEnum(['string', 'stream'])
    responseType?: 'string' | 'stream' = 'stream';
}