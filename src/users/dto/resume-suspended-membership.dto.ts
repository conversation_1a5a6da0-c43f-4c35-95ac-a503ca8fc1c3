import { ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsDate, IsMongoId, IsNotEmpty } from "class-validator";

export class ResumeMembershipDto {


    @ApiProperty({
        description: "Purchase ID",
        type: String,
        example: "666666666666666666666666",
    })
    @IsNotEmpty()
    @IsMongoId()
    purchaseId: string;

    @ApiProperty({
        description: "Suspendtion ID",
        type: String,
        example: "666666666666666666666666",
    })
    @IsNotEmpty()
    @IsMongoId()
    suspensionId: string;

    @ApiProperty({
        description: "From Date",
        type: Date,
        example: new Date(new Date().setDate(new Date().getDate() + 2)),
    })
    @IsNotEmpty()
    @IsDate()
    @Type(() => Date)
    @Transform(({ value }) => {
        const date = new Date(value);
        date.setUTCHours(0, 0, 0, 0);
        return date;
    })
    fromDate: Date;
}