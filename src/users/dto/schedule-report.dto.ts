import { ApiProperty } from "@nestjs/swagger";
import {
    IsArray,
    IsEnum,
    IsNotEmpty,
    ArrayNotEmpty,
    <PERSON>Date,
    IsOptional
} from "class-validator";
import { Type } from "class-transformer";
import { Types } from "mongoose";
import { ClassType } from "src/utils/enums/class-type.enum";
import { ScheduleStatusType } from "src/scheduling/enums/schedule-status.enum";

export class ExportScheduleReportDto {
    @ApiProperty({
        description: "Start date of the report range",
        example: "2024-09-15T00:00:00Z",
        required: true,
    })
    @IsNotEmpty({ message: "Start date is required." })
    @Type(() => Date)
    @IsDate({ message: "Start date must be a valid ISO date." })
    startDate: Date;

    @ApiProperty({
        description: "End date of the report range",
        example: "2024-09-20T00:00:00Z",
        required: true,
    })
    @IsNotEmpty({ message: "End date is required." })
    @Type(() => Date)
    @IsDate({ message: "End date must be a valid ISO date." })
    endDate: Date;

    @ApiProperty({
        description: "Facility IDs for which the sales report is to be generated",
        example: ["66ceffc7a13db5380edb06b1"],
        required: true,
    })
    @IsNotEmpty({ message: "Facility IDs are required." })
    @IsArray({ message: "Facility IDs must be an array." })
    @ArrayNotEmpty({ message: "Facility IDs array must not be empty." })
    facilityIds: Types.ObjectId[];

    @ApiProperty({
        description: "Response type for export",
        enum: ['string', 'stream'],
        default: 'stream',
    })
    @IsNotEmpty({ message: "Response type is required." })
    @IsEnum(['string', 'stream'], {
        message: "Response type must be either 'string' or 'stream'.",
    })
    responseType: 'string' | 'stream';

    @ApiProperty({
        description: "Service category IDs to filter the report (optional)",
        example: ["66ceffc7a13db5380edb06b1"],
        required: true,
    })
    @IsOptional()
    @IsArray({ message: "Service category IDs must be an array." })
    serviceCategoryIds?: string[];

    @ApiProperty({
        description: "Staff IDs to filter the report (optional)",
        example: ["66ceffc7a13db5380edb06b1"],
        required: false,
    })
    @IsOptional()
    @IsArray({ message: "Staff IDs must be an array." })
    @ArrayNotEmpty({ message: "Staff IDs array must not be empty if provided." })
    staffIds?: Types.ObjectId[];

    @ApiProperty({
        description: "Array of class types (e.g., Personal Appointment, Classes)",
        enum: ClassType,
        isArray: true,
        example: [ClassType.PERSONAL_APPOINTMENT, ClassType.CLASSES],
        required: true,
    })
    @IsNotEmpty({ message: "Class types are required." })
    @IsArray({ message: "Class types must be an array of valid enum values." })
    @IsEnum(ClassType, {
        each: true,
        message: "Each service type must be a valid value from the ClassType enum.",
    })
    classType: ClassType[];

    @ApiProperty({
        description: "Array of Schedule Status types",
        enum: ScheduleStatusType,
        isArray: true,
        example: [ScheduleStatusType.BOOKED, ScheduleStatusType.CANCELED],
        required: false,
    })
    @IsOptional()
    @IsArray({ message: "Schedule Status types must be an array of valid enum values." })
    @IsEnum(ScheduleStatusType, {
        each: true,
        message: "Each Schedule Status must be a valid value from the ScheduleStatusType enum.",
    })
    scheduleStatus?: ScheduleStatusType[];
}