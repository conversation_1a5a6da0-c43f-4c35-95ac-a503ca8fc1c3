import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsBoolean, IsInt, IsNotEmpty, IsOptional, IsString, Max, <PERSON> } from "class-validator";

export class PaginationDTO {
    @ApiProperty({
        description: "Search query to filter results.",
        example: "Search",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Search must be a string" })
    search?: string;

    @ApiProperty({
        description: "Number of items per page.",
        example: 10,
        minimum: 1,
        maximum: 50,
        required: true,
    })
    @IsNotEmpty({ message: "PageSize is required" })
    @IsInt({ message: "PageSize must be an integer" })
    @Min(1, { message: "PageSize must be at least 1" })
    @Max(50, { message: "PageSize must be at most 50" })
    pageSize: number = 10;

    @ApiProperty({
        description: "Page number to jump to.",
        example: 1,
        minimum: 1,
        required: true,
    })
    @IsNotEmpty({ message: "Page is required" })
    @IsInt({ message: "Page must be an integer" })
    @Min(1, { message: "Page must be at least 1" })
    page: number = 1;

    @ApiProperty({
        description: "Whether to fetch Pricing with bundled data or not .",
        type: Boolean,
        example: false,
    })
    @IsOptional()
    @IsBoolean()
    fetchWithBundled?: boolean;

    @ApiProperty({
        description: "Whether to fetch Pricing with active time frames or not .",
        type: Boolean,
        example: false,
    })
    @IsOptional()
    @IsBoolean()
    @Type(() => Boolean)
    includeTimeFrameActive?: boolean;
}