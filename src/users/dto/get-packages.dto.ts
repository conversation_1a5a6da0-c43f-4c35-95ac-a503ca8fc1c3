import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsEnum, IsMongoId, IsOptional } from "class-validator";
import { ClassType } from "src/utils/enums/class-type.enum";

export class GetPackagesDto {
    @ApiProperty({
        description: "Type of service",
        example: "bookings",
        required: false,
    })
    @IsOptional()
    @IsEnum(ClassType)
    classType: string;

    @ApiProperty({
        description: "Client Id",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Client of package" })
    clientId: string;
    
}
