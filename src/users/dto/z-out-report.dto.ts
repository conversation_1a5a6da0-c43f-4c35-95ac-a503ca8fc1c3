import { ApiProperty } from "@nestjs/swagger";
import {
    IsArray,
    IsEnum,
    IsNotEmpty,
    ArrayNotEmpty,
} from "class-validator";
import { Types } from "mongoose";

export class ExportZOutReportDto {

    @ApiProperty({
        description: "ZoutId for which the sales report is to be generated",
        example: ["66ceffc7a13db5380edb06b1"],
        required: true,
    })
    @IsNotEmpty({ message: "ZoutId  are required." })
    zOutId: string;

    @ApiProperty({
        description: "Facility IDs for which the sales report is to be generated",
        example: ["66ceffc7a13db5380edb06b1"],
        required: true,
    })
    @IsNotEmpty({ message: "Facility IDs are required." })
    @IsArray({ message: "Facility IDs must be an array." })
    @ArrayNotEmpty({ message: "Facility IDs array must not be empty." })
    facilityIds: Types.ObjectId[];

    @ApiProperty({
        description: "Response type for export",
        enum: ['string', 'stream', 'pdf', 'html'],
        default: 'stream',
    })
    @IsNotEmpty({ message: "Response type is required." })
    @IsEnum(['string', 'stream', 'pdf', 'html'], {
        message: "Response type must be either 'string' or 'stream'.",
    })
    responseType: 'string' | 'stream' | 'html' | 'pdf';
    // @ApiProperty({
    //     description: "Start date of the report range",
    //     example: "2024-09-15T00:00:00Z",
    //     required: true,
    // })
    // @IsNotEmpty({ message: "Start date is required." })
    // @Type(() => Date)
    // @IsDate({ message: "Start date must be a valid ISO date." })
    // startDate: Date;

    // @ApiProperty({
    //     description: "End date of the report range",
    //     example: "2024-09-20T00:00:00Z",
    //     required: true,
    // })
    // @IsNotEmpty({ message: "End date is required." })
    // @Type(() => Date)
    // @IsDate({ message: "End date must be a valid ISO date." })
    // endDate: Date;

    // @ApiProperty({
    //     description: "Start time of the report range",
    //     example: '08:00',
    //     required: true

    // })
    // @IsNotEmpty({ message: "From time is required." })
    // @IsString({ message: 'From time must be a string' })
    // @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: 'From time must be in the format HH:mm' })
    // from: string;

    // @ApiProperty({
    //     description: "End time of the report range",
    //     example: '17:00',
    //     required: true
    // })
    // @IsNotEmpty({ message: "To time is required." })
    // @IsString({ message: 'To time must be a string' })
    // @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: 'To time must be in the format HH:mm' })
    // to: string;


}