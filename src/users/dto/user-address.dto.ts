import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "class-validator";

export class AddressDto {
    @ApiProperty({ description: "Street of the user where they live", example: "Udyog Vihar" })
    @IsOptional()
    @IsString({ message: "Street must be a string" })
    street?: string;

    @ApiProperty({ description: "City of the user where they live", example: "Mumbai" })
    @IsOptional()
    @IsString({ message: "City must be a string" })
    city?: string;

    @ApiProperty({ description: "State of the user where they live", example: "Maharashtra" })
    @IsOptional()
    @IsString({ message: "State must be a string" })
    state?: string;

    @ApiProperty({ description: "Country of the user where they live", example: "India" })
    @IsOptional()
    @IsString({ message: "Country must be a string" })
    country?: string;

    @ApiProperty({ description: "Pincode of the user where they live", example: 110006 })
    @IsOptional()
    @IsInt({ message: "<PERSON>nco<PERSON> must be an integer" })
    @Min(100000, { message: "<PERSON>nco<PERSON> must be at least 6 digits" })
    @Max(999999, { message: "<PERSON>nco<PERSON> must be at most 6 digits" })
    pincode?: number;
}