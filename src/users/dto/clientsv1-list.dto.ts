import { ApiProperty } from "@nestjs/swagger";
import { PaginationDTO } from "./pagination.dto";
import { IsArray, IsInt, IsMongoId, IsNotEmpty, IsOptional, <PERSON>String, <PERSON>, <PERSON> } from "class-validator";

export class ClientsListDtoV1 {

    @ApiProperty({
        description: "Organization ID of the staff. Must be a valid MongoDB ObjectId.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsNotEmpty({ message: "Organization ID is required." })
    @IsMongoId({ message: "Organization ID must be a valid MongoDB ObjectId." })
    organizationId: string;

    @ApiProperty({
        description: "Client ID of the staff. Must be a valid MongoDB ObjectId.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsOptional()
    @IsMongoId({ message: "Client ID must be a valid MongoDB ObjectId." })
    clientId: string;
    @ApiProperty({
        description: "Facility IDs (optional). Must be an array of valid MongoDB ObjectIds.",
        example: ["659d268dee4b6081dacd41fd", "659d268dee4b6081dacd41fe"],
        required: false,
    })
    @IsOptional()
    @IsArray()
    @IsMongoId({ each: true, message: "Each Facility ID must be a valid MongoDB ObjectId." })
    facilityIds?: string[];

    @ApiProperty({
        description: "Search keyword (optional). Must be a string.",
        example: "Knox",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Search must be a valid string." })
    search?: string;

    @ApiProperty({
        description: "Number of items per page.",
        example: 10,
        minimum: 1,
        maximum: 50,
        required: true,
    })
    @IsNotEmpty({ message: "PageSize is required" })
    @IsInt({ message: "PageSize must be an integer" })
    @Min(1, { message: "PageSize must be at least 1" })
    @Max(50, { message: "PageSize must be at most 50" })
    pageSize: number = 10;

    @ApiProperty({
        description: "Page number to jump to.",
        example: 1,
        minimum: 1,
        required: true,
    })
    @IsNotEmpty({ message: "Page is required" })
    @IsInt({ message: "Page must be an integer" })
    @Min(1, { message: "Page must be at least 1" })
    page: number = 1;

}