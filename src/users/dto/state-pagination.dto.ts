import { ApiProperty } from "@nestjs/swagger";
import { IsInt, IsMongoId, IsNotEmpty, IsO<PERSON>al, IsString, <PERSON>, <PERSON> } from "class-validator";

export class StatePaginationDTO {
    @ApiProperty({
        description: "Search query to filter results.",
        example: "Search",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Search must be a string" })
    search?: string;

    @ApiProperty({
        description: "Number of items per page.",
        example: 10,
        minimum: 1,
        maximum: 50,
        required: true,
    })
    @IsNotEmpty({ message: "PageSize is required" })
    @IsInt({ message: "PageSize must be an integer" })
    @Min(1, { message: "PageSize must be at least 1" })
    @Max(50, { message: "PageSize must be at most 50" })
    pageSize: number;

    @ApiProperty({
        description: "Page number to jump to.",
        example: 1,
        minimum: 1,
        required: true,
    })
    @IsNotEmpty({ message: "Page is required" })
    @IsInt({ message: "Page must be an integer" })
    @Min(1, { message: "Page must be at least 1" })
    page: number;

    @ApiProperty({
        description: "stateId.",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Invalid type of stateId" })
    stateId?: string;

    @ApiProperty({
        description: "Id of the associated cityId.",
        example: "659d268dee4b6081dacd41f1",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Invalid type of cityId" })
    cityId?: string;
}