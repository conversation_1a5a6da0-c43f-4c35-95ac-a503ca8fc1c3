import { ApiProperty } from '@nestjs/swagger';
import { DiscountType } from 'src/utils/enums/discount.enum';

export class PurchaseResponseDto {
  @ApiProperty({
    description: 'Invoice ID',
    example: '60d21b4667d0d8992e610c85',
    required: false,
  })
  invoiceId?: string;

  @ApiProperty({
    description: 'Invoice number',
    example: 1001,
    required: false,
  })
  invoiceNumber?: number;

  @ApiProperty({
    description: 'Purchase items',
    example: [
      {
        packageId: '60d21b4667d0d8992e610c85',
        purchaseIds: ['60d21b4667d0d8992e610c85', '60d21b4667d0d8992e610c86'],
        packageName: "Package 1",
        quantity: 2,
        isBundledPricing: false,
        expireIn: 30,
        durationUnit: 30,
        startDate: new Date(),
        endDate: new Date(),
        unitPrice: 999,
        discountType: DiscountType.FLAT,
        discountValue: 99,
        discountedBy: "60d21b4667d0d8992e610c85",
        discountExcludeCart: 0,
        discountIncludeCart: 0,
        hsnOrSacCode: "998314",
        tax: 99,
        gstAmount: 99,
      },
    ],
    required: false,
  })
  purchaseItems?: any[];

  @ApiProperty({
    description: 'Order ID',
    example: 5001,
    required: false,
  })
  orderId?: number;

  @ApiProperty({
    description: 'Grand total of the purchase',
    example: 165.17,
    required: false,
  })
  grandTotal?: number;

  @ApiProperty({
    description: 'Amount paid for the purchase',
    example: 165.17,
    required: false,
  })
  amountPaid?: number;

  @ApiProperty({
    description: 'Error details if the purchase failed',
    example: 'Insufficient stock for product X',
    required: false,
  })
  error?: string;
}
