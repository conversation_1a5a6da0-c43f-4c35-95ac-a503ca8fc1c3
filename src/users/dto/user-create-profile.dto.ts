import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString, IsNumber, ValidateNested } from "class-validator";
import { AddressDto } from "./user-address.dto";
import { UserFitnessProfileDto } from "./user-fitness-profile.dto";
import { Type } from "class-transformer";

export class CreateUserDto {
    @ApiProperty({ description: "Profile Picture of the user", example: "https://my-bucket.s3.us-west-2.amazonaws.com/uploads/images/example.jpg" })
    @IsOptional()
    @IsString({ message: "Profile Picture must be a string" })
    profilePicture?: string;

    // @ApiProperty({ description: "Address of the user", example: AddressDto })
    // @IsOptional()
    // @ValidateNested()
    // @Type(() => AddressDto)
    // address?: AddressDto;

    @ApiProperty({ description: "UserFitnessProfile of the user", example: UserFitnessProfileDto })
    @IsOptional()
    @ValidateNested({ each: true, message: "Fitness Profile must be a valid UserFitnessProfileDto object" })
    @Type(() => UserFitnessProfileDto)
    fitnessProfile?: UserFitnessProfileDto;

    @ApiProperty({ description: "Current step in the setup process", example: 1 })
    @IsOptional()
    @IsNumber({}, { message: "Current Step must be a number" })
    currentStep?: number;

    @ApiProperty({ description: "Total step in the setup process", example: 5 })
    @IsOptional()
    @IsNumber({}, { message: "Total Step must be a number" })
    totalStep?: number;
}