
import { ApiProperty, OmitType, PickType } from "@nestjs/swagger";
import { IsArray, IsMongoId, IsNotEmpty } from "class-validator";
import { PaginationListDto } from "src/common/pagination/dtos/pagination.list.dto";

export class SharePackageDto {
    @ApiProperty({
        description: "User ID who is sharing the package",
        example: "659d268dee4b6081dacd41fd"
    })
    @IsMongoId({ message: "Invalid Sender User" })
    shareFrom: string;

    @ApiProperty({
        description: "List of user IDs to whom the package is being shared",
        type: [String],
        example: ["659d268dee4b6081dacd41fd", "659d268dee4b6081dacd41fd"]
    })
    @IsArray({ message: "Shares must be an array" })
    @IsMongoId({ each: true, message: "Each share must be a valid user" })
    shareTo: string[];

    @ApiProperty({
        description: "ID of the associated package purchase",
        example: "659d268dee4b6081dacd41fd",
        required: true
    })
    @IsMongoId({ message: "Invalid Purchase details" })
    purchaseId: string;
}

export class SharePackagePublicDto extends OmitType(SharePackageDto, ['shareFrom']) { }

export class FamilyShareClientDto extends PaginationListDto {

    @ApiProperty({
        description: "ID of the associated package purchase",
        example: "659d268dee4b6081dacd41fd",
        required: true
    })
    @IsMongoId({ message: "Invalid Purchase details" })
    purchaseId: string;

    @ApiProperty({
        description: "User ID who is sharing the package",
        example: "659d268dee4b6081dacd41fd",
        required: true
    })
    @IsMongoId({ message: "Invalid Sender User" })
    userId: string;
}

export class FamilyShareClientListPublicDto extends OmitType(FamilyShareClientDto, ['userId']) { }