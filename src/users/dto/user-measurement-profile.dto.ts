import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString } from "class-validator";

export class MeasurementDto {
    @ApiProperty({ description: "Chest measurement in inches", example: "40" })
    @IsOptional()
    @IsString({ message: "Chest measurement must be a string" })
    chest?: string;

    @ApiProperty({ description: "Shoulder measurement in inches", example: "50" })
    @IsOptional()
    @IsString({ message: "Shoulder measurement must be a string" })
    shoulder?: string;

    @ApiProperty({ description: "Arm measurement in inches", example: "15" })
    @IsOptional()
    @IsString({ message: "Arm measurement must be a string" })
    arm?: string;

    @ApiProperty({ description: "Bicep measurement in inches", example: "14" })
    @IsOptional()
    @IsString({ message: "Bicep measurement must be a string" })
    bicep?: string;

    @ApiProperty({ description: "Forearm measurement in inches", example: "12" })
    @IsOptional()
    @IsString({ message: "Forearm measurement must be a string" })
    forearm?: string;

    @ApiProperty({ description: "Waist measurement in inches", example: "32" })
    @IsOptional()
    @IsString({ message: "Waist measurement must be a string" })
    waist?: string;

    @ApiProperty({ description: "Hip measurement in inches", example: "38" })
    @IsOptional()
    @IsString({ message: "Hip measurement must be a string" })
    hip?: string;

    @ApiProperty({ description: "Thigh measurement in inches", example: "24" })
    @IsOptional()
    @IsString({ message: "Thigh measurement must be a string" })
    thigh?: string;

    @ApiProperty({ description: "Calf measurement in inches", example: "16" })
    @IsOptional()
    @IsString({ message: "Calf measurement must be a string" })
    calf?: string;
}