import { Injectable } from '@nestjs/common';
import { PaymentService } from '../service/payment.service';
import { PaymentHistory } from '../schema/payment.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
@Injectable()
export class WebhookService {
  constructor(private readonly paymentService: PaymentService,
    @InjectModel(PaymentHistory.name) private paymentHistoryModel: Model<PaymentHistory>,
  ) { }

  async saveWebhook(event: any, organizationId: string) {
  const eventType = event.event;
  const paymentData = event.payload?.payment?.entity || event.payload?.payment_link?.entity || {};
  const paymentId = paymentData.id;
  const status = paymentData.status || 'pending';

  if (!paymentId) {
    throw new Error('Payment ID not found in webhook payload');
  }

  const existingPayment = await this.paymentHistoryModel.findOne({ paymentId });

  if (existingPayment) {
    existingPayment.paymentmetadata = event.payload;
    existingPayment.status = status;
    await existingPayment.save();
  } else {
    const newPayment = new this.paymentHistoryModel({
      paymentId,
      paymentmetadata: event.payload,
      organizationId: organizationId ?? null,
    });

    await newPayment.save();
  }
}

}
