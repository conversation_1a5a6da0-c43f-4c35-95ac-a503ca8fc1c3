import { BadRequestException, Injectable } from '@nestjs/common';
const Razorpay = require('razorpay');
import { PaymentCredential, PaymentCredentialDocument } from '../schema/paymentCredential.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as crypto from 'crypto';



@Injectable()
export class RazorpayService {
  private razorpay: any;
  @InjectModel(PaymentCredential.name) private paymentCredentialModel: Model<PaymentCredentialDocument>

  constructor() {
    this.razorpay = new Razorpay({
      key_id: "rzp_test_gCGM3asXMW22Wi",
      key_secret: "Hqw7Q0mxChvitsigtN6SiJVf",
    });
  }

  async createPaymentLink(amount: number): Promise<string> {
    const response = await this.razorpay.paymentLink.create({
      amount: amount * 100, // in paise
      currency: 'INR',
      description: 'Payment for your order',
      customer: {
        name: 'Customer',
        email: '<EMAIL>',
        contact: '8077889124',
      },
      notify: {
        sms: true,
        email: true,
      },
      reminder_enable: true,
      //   callback_url: 'https://yourdomain.com/payment/success', // Optional: URL to redirect after payment
      //   callback_method: 'get',
    });
    return response.short_url;
  }
  private decrypt(encrypted: string): string {
    const [keyHex, ivHex, encryptedText] = encrypted.split(':');
    const key = Buffer.from(keyHex, 'hex');
    const iv = Buffer.from(ivHex, 'hex');
    const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
    let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }
  async capturePaymentRazorPay(paymentId: string, amount: number, organizationId: string): Promise<any> {
    const razorPayCredential = await this.paymentCredentialModel.findOne({ organizationId, paymentGateway: 'razorpay' })
    if (!razorPayCredential) {
      throw new BadRequestException('Razor pay credential not found for this organization');
    }
    const keyId = this.decrypt(razorPayCredential.keyId);
    const keySecret = this.decrypt(razorPayCredential.keySecret);
    const razorpay = new Razorpay({
      key_id: keyId,
      key_secret: keySecret,
    })
    const captureResponse = await razorpay.payments.capture(paymentId, amount, 'INR');
  }

}
