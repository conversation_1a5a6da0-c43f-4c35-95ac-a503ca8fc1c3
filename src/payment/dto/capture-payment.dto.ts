import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsMongoId, IsNotEmpty, IsNumber, IsOptional, IsString, Min } from 'class-validator';

export class CapturePaymentDto {
  @ApiProperty({
    description: 'Razorpay payment ID to be captured',
    example: 'pay_Qb6MGaEXBowcSB',
  })
  @IsString()
  @IsNotEmpty()
  paymentId: string;

  @ApiProperty({
    description: 'Amount to capture in paise (e.g. ₹100 = 10000)',
    example: 3513404,
  })
  @IsNumber()
  @Min(1)
  amount: number;

  @ApiPropertyOptional({
    description: 'Optional organization ID if multi-tenant support is used',
    example: '663ad3e1f9d4e8309be9d211',
  })
  @IsMongoId()
  organizationId?: string;
}
