import { forwardRef, Module } from '@nestjs/common';
import { RazorpayService } from './service/razorpay.service';
import { PaymentController } from './controller/payment.controller';
import { PaymentService } from './service/payment.service';
import { MongooseModule } from '@nestjs/mongoose';
import { PaymentHistory, PaymentHistorySchema } from './schema/payment.schema';
import { WebhookController } from './controller/webhook.controller';
import { WebhookService } from './service/webhook.service';
import { PaymentCredential, PaymentCredentialSchema } from './schema/paymentCredential.schema';
import { AuthModule } from 'src/auth/auth.module';
import { UtilsModule } from 'src/utils/utils.module';

@Module({
  imports: [
    forwardRef(() => AuthModule),
    UtilsModule,
    MongooseModule.forFeature([{ name: PaymentHistory.name, schema: PaymentHistorySchema }]),
    MongooseModule.forFeature([{ name: PaymentCredential.name, schema: PaymentCredentialSchema }])],
  controllers: [PaymentController, WebhookController],
  providers: [PaymentService, RazorpayService, WebhookService],
  exports: [PaymentService, RazorpayService, WebhookService],
})
export class PaymentModule { }
