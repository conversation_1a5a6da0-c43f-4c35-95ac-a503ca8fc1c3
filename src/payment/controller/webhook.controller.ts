import { Controller, Post, Req, UseGuards } from '@nestjs/common';
import { WebhookService } from '../service/webhook.service';
import { RazorpaySignatureGuard } from '../guard/razorpay-signature.guard';
import { Request } from 'express';

@Controller('webhook')
export class WebhookController {
  constructor(private readonly webhookService: WebhookService) { }

  @Post('razorpay')
  @UseGuards(RazorpaySignatureGuard)
  async handleWebhook(@Req() req: Request) {
    const orgId = req.organizationId;
    await this.webhookService.saveWebhook(req.body,orgId);
    return { success: true };
  }
}
