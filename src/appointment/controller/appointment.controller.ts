import { BadRequestException, Body, Controller, Delete, Param, Post, UseGuards } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { AppointmentService } from "../service/appointment.service";
import { AuthGuard } from "@nestjs/passport";
import { RolesGuard } from "src/auth/roles.guard";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { Roles } from "src/auth/decorators/roles.decorator";
import { CreateAppointmentDto } from "../dto/book-appointment.dto";

@ApiTags("Appointment")
@ApiBearerAuth()
@Controller("appointment")
export class AppointmentController {
    constructor(private readonly appointmentService: AppointmentService) {}

    @Post("/create")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.USER, ENUM_ROLE_TYPE.WEB_MASTER)
    @ApiOperation({ summary: "Book an appointment" })
    async bookAppointment(@Body() createAppointmentDto: CreateAppointmentDto, @GetUser() user: any): Promise<any> {
        let role = user.role;
        let result = "";
        // let facilityAvailability = await this.appointmentService.checkFacilityAvailability(createAppointmentDto, user);
        // if (!facilityAvailability) throw new BadRequestException("Facility not available");
        // let trainerAvailability = await this.appointmentService.checkTrainerAvailability(createAppointmentDto, user);
        // if (!trainerAvailability) throw new BadRequestException("Trainer not available");
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                result = await this.appointmentService.bookingByOrganization(createAppointmentDto, user);
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
                result = await this.appointmentService.bookingByWebMaster(createAppointmentDto, user);
                break;
            case ENUM_ROLE_TYPE.USER:
                result = await this.appointmentService.bookingByUser(createAppointmentDto, user);
                break;
            case ENUM_ROLE_TYPE.TRAINER:
                result = await this.appointmentService.bookingByTrainer(createAppointmentDto, user);
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        return {
            message: "Appointment booked successfully!",
            data: result,
        };
    }

    @Post("/update/:id")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.USER, ENUM_ROLE_TYPE.WEB_MASTER)
    @ApiOperation({ summary: "Update an existing appointment" })
    async updateAppointment(@Param("id") id: string, @Body() createAppointmentDto: CreateAppointmentDto, @GetUser() user: any): Promise<any> {
        let role = user.role;
        let result = "";
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                result = await this.appointmentService.updateBookingByOrganization(id, createAppointmentDto, user);
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
                result = await this.appointmentService.updateBookingByWebMaster(id, createAppointmentDto, user);
                break;
            case ENUM_ROLE_TYPE.USER:
                result = await this.appointmentService.updateBookingByUser(id, createAppointmentDto, user);
                break;
            case ENUM_ROLE_TYPE.TRAINER:
                result = await this.appointmentService.updateBookingByTrainer(id, createAppointmentDto, user);
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        return {
            message: "Appointment updated successfully!",
            data: result,
        };
    }

    @Post("/get/:id")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.USER, ENUM_ROLE_TYPE.WEB_MASTER)
    @ApiOperation({ summary: "Get details of a specific appointment" })
    async getBooking(@Param("id") id: string, @GetUser() user: any): Promise<any> {
        let role = user.role;
        let result = "";
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                result = await this.appointmentService.getBookingByOrganization(id, user);
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
                result = await this.appointmentService.getBookingByWebMaster(id, user);
                break;
            case ENUM_ROLE_TYPE.USER:
                result = await this.appointmentService.getBookingByUser(id, user);
                break;
            case ENUM_ROLE_TYPE.TRAINER:
                result = await this.appointmentService.getBookingByTrainer(id, user);
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        return {
            message: "Booking details retrieved successfully!",
            data: result,
        };
    }

    @Post("/getAll")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.USER, ENUM_ROLE_TYPE.WEB_MASTER)
    @ApiOperation({ summary: "Get all bookings for the user" })
    async getAllBookings(@GetUser() user: any): Promise<any> {
        let role = user.role;
        let result = "";
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                result = await this.appointmentService.getAllBookingsByOrganization(user);
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
                result = await this.appointmentService.getAllBookingsByWebMaster(user);
                break;
            case ENUM_ROLE_TYPE.USER:
                result = await this.appointmentService.getAllBookingsByUser(user);
                break;
            case ENUM_ROLE_TYPE.TRAINER:
                result = await this.appointmentService.getAllBookingsByTrainer(user);
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        return {
            message: "All bookings retrieved successfully!",
            data: result,
        };
    }

    @Delete("/delete/:id")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.USER, ENUM_ROLE_TYPE.WEB_MASTER)
    @ApiOperation({ summary: "Delete a specific appointment" })
    async deleteBooking(@Param("id") id: string, @GetUser() user: any): Promise<any> {
        let role = user.role;
        let result = "";
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                result = await this.appointmentService.deleteBookingByOrganization(id, user);
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
                result = await this.appointmentService.deleteBookingByWebMaster(id, user);
                break;
            case ENUM_ROLE_TYPE.USER:
                result = await this.appointmentService.deleteBookingByUser(id, user);
                break;
            case ENUM_ROLE_TYPE.TRAINER:
                result = await this.appointmentService.deleteBookingByTrainer(id, user);
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        return {
            message: "Appointment deleted successfully!",
            data: result,
        };
    }
}
