import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsO<PERSON>al, IsString, Matches, IsBoolean, IsArray, IsNumber } from "class-validator";

export class BookingTimeSlotsDTO {
    @ApiProperty({
        description: "Date of the appointment",
        type: Date,
        example: new Date(),
    })
    @IsNotEmpty()
    date: Date;

    @ApiProperty({
        description: "Start time of the appointment in HH:MM format (24-hour clock)",
        example: "09:00",
        pattern: "^([0-1]\\d|2[0-3]):([0-5]\\d)$",
    })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, {
        message: "Start time must be in HH:MM format",
    })
    from: string;

    @ApiProperty({
        description: "End time of the appointment in HH:MM format (24-hour clock)",
        example: "12:00",
        pattern: "^([0-1]\\d|2[0-3]):([0-5]\\d)$",
    })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, {
        message: "End time must be in HH:MM format",
    })
    to: string;
}

export class CreateAppointmentDto {
    @ApiProperty({
        description: "Facility ID for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    facilityId: string;

    @ApiProperty({
        description: "Organization ID for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    organizationId: string;

    @ApiProperty({
        description: "Trainer ID assigned for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    trainerId: string;

    @ApiProperty({
        description: "Client ID for whom the appointment is made",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    clientId: string;

    @ApiProperty({
        description: "Package ID if any package is selected for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsOptional()
    packageId?: string;

    @ApiProperty({
        description: "Appointment Id any package is selected for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    appointmentId: string;

    @ApiProperty({
        description: "Service ID if any service is selected for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsOptional()
    serviceId?: string;

    @ApiProperty({
        description: "Flag to send confirmation for the appointment",
        type: Boolean,
        example: false,
    })
    @IsOptional()
    @IsBoolean()
    sendConfirmation?: boolean;

    @ApiProperty({
        description: "Number of sessions for the appointment",
        type: Number,
        example: 3,
    })
    @IsNotEmpty()
    @IsNumber()
    noOfSessions: number;

    @ApiProperty({
        description: "Duration of the Session",
        example: 60,
    })
    @IsNotEmpty()
    @IsString()
    duration: string;

    @ApiProperty({
        description: "Time slots for the appointment",
        type: [BookingTimeSlotsDTO], // Specify as array of BookingTimeSlotsDTO
        required: false,
    })
    @IsOptional()
    @IsArray()
    timeSlots: BookingTimeSlotsDTO[];

    @ApiProperty({
        description: "Note for appointment",
        example: 60,
    })
    @IsOptional()
    @IsString()
    notes?: string;
}
