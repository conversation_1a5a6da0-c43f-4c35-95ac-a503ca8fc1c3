import { ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import {IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, Matches, Max, Min, ValidateIf } from "class-validator";
import { ClassType } from "src/utils/enums/class-type.enum";
import { DateRange } from "src/utils/enums/date-range-enum";
import { ScheduleStatusType } from "../enums/schedule-status.enum";


export class GetSchedulesDto {
    @ApiProperty({
        description: "Search input for user search",
        type: String,
        example: "Raoul",
    })
    @IsOptional()
    search?: string;

    @ApiProperty({
        description: "Filter by schedule status",
        type: String,
        example: ScheduleStatusType.BOOKED,
    })
    @IsOptional()
    @IsEnum(ScheduleStatusType)
    scheduleStatus?: string;

    @ApiProperty({
        description: "Facility ID for the appointment",
        type: String,
        example: ["615c2f8e2c1ae9123cbb3c1b"],
    })
    @IsOptional()
    facilityId?: string[] = [];

    @ApiProperty({
        description: "Staff ids",
        type: String,
        example: ["615c2f8e2c1ae9123cbb3c1b"],
    })
    @IsOptional()
    trainerId?: string[] = [];

    @ApiProperty({
        description: "Room ids",
        type: String,
        example: ["615c2f8e2c1ae9123cbb3c1b"],
    })
    @IsOptional()
    roomId?: string[] = [];

    @ApiProperty({
        description: "Array of ClassTypes for Availability",
        enum: ClassType,
        example: ClassType.PERSONAL_APPOINTMENT,
        required: false,
    })
    @IsOptional()
    @IsEnum(ClassType, { each: true, message: "Each ClassType must be a valid enum value" })
    classType?: ClassType;

    @ApiProperty({
        description: "Appointment Id any package is selected for the appointment",
        type: String,
        example: ["615c2f8e2c1ae9123cbb3c1b"],
    })
    @IsOptional()
    serviceCategory?: string[] = [];

    @ApiProperty({
        description: "Start sate range",
        example: new Date(),
    })
    // @IsOptional()
    @ValidateIf((req) => !!req.endDate)
    @Type(()=>Date)
    startDate?: Date;

    @ApiProperty({
        description: "End sate range",
        example: new Date(),
    })
    // @IsOptional()
    @ValidateIf((req) => !!req.startDate)
    @Type(()=>Date)
    endDate?: Date;

    @ApiProperty({
        description: "Page number",
        type: Number,
        example:1
    })
    @IsOptional()
    @Transform(({value})=>value||1)
    @Type(()=>Number)
    @Min(1)
    page?: number = 1;

    @ApiProperty({
        description: "Page number",
        type: Number,
        example:1
    })
    @Transform(({value})=>value || 10)
    @Type(()=> Number)
    @Min(10)
    @Max(50)
    pageSize?: number = 10;


}