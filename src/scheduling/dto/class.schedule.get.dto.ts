import { ApiProperty, ApiHideProperty, OmitType } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsBoolean, IsDate, IsEmpty, IsEnum, IsMongoId, IsNotEmpty, IsNumber, IsOptional, IsString, Matches, Min } from "class-validator";
import { PaginationListDto } from "src/common/pagination/dtos/pagination.list.dto";

export class ClassScheduleGetUserForEnrollmentDto extends PaginationListDto {

    @ApiProperty({
        description: "Scheduling ID for the enrollment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    @IsMongoId({ message: "Invalid scheduling id" })
    scheduleId: string;

}
