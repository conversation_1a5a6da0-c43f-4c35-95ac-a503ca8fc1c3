import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { <PERSON>Array, IsDate, IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString, Matches, ValidateNested } from "class-validator";
import { ClassType } from "src/utils/enums/class-type.enum";

class TimeSlotsDTO {
    @ApiProperty({
        description: "Start time for scheduling on specified days (HH:mm)",
        example: "08:00",
    })
    @IsOptional()
    @IsString({ message: "From time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "From time must be in the format HH:mm" })
    from: string;

    @ApiProperty({
        description: "End time for scheduling on specified days (HH:mm)",
        example: "17:00",
    })
    @IsOptional()
    @IsString({ message: "To time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "To time must be in the format HH:mm" })
    to: string;
}

class ScheduleDTO {
    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [TimeSlotsDTO],
        required: true,
    })
    @IsArray({ message: "Monday must be an array of time slots" })
    @ValidateNested({ each: true })
    @Type(() => TimeSlotsDTO)
    mon: TimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [TimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: "Tuesday must be an array of time slots" })
    @ValidateNested({ each: true })
    @Type(() => TimeSlotsDTO)
    tue: TimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [TimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: "Wednesday must be an array of time slots" })
    @ValidateNested({ each: true })
    @Type(() => TimeSlotsDTO)
    wed: TimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [TimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: "Thursday must be an array of time slots" })
    @ValidateNested({ each: true })
    @Type(() => TimeSlotsDTO)
    thu: TimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [TimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: "Friday must be an array of time slots" })
    @ValidateNested({ each: true })
    @Type(() => TimeSlotsDTO)
    fri: TimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [TimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: "Saturday must be an array of time slots" })
    @ValidateNested({ each: true })
    @Type(() => TimeSlotsDTO)
    sat: TimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [TimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: "Sunday must be an array of time slots" })
    @ValidateNested({ each: true })
    @Type(() => TimeSlotsDTO)
    sun: TimeSlotsDTO[];
}

export class CheckStaffAvailabilityDto {
    @ApiProperty({
        description: "Type of the Class.",
        enum: ClassType,
        example: ClassType.PERSONAL_APPOINTMENT,
    })
    @IsEnum(ClassType, { message: "Invalid class type" })
    classType: ClassType;

    @ApiProperty({
        description: "The Id of the facility",
        example: "66cedf7a731d1269a4157a2d",
    })
    @IsMongoId({ message: "Invalid facility details" })
    facilityId: string;

    @ApiProperty({
        description: "The Id of the service category",
        example: "66cedf7a731d1269a4157a2d",
    })
    @IsMongoId({ message: "Invalid service category details" })
    serviceId: string;

    @ApiProperty({
        description: "Start date from when the scheduling will get created",
        example: "2024-10-01T00:00:00.000+00:00",
        required: true,
    })
    @IsDate({ message: "Required valid start date" })
    @Type(() => Date)
    @IsNotEmpty({ message: "Invalid start date" })
    fromDate: Date;

    @ApiProperty({
        description: "End date from when the scheduling can be done",
        example: "2024-10-01T00:00:00.000+00:00",
        required: true,
    })
    @IsDate({ message: "Required valid end date" })
    @Type(() => Date)
    @IsNotEmpty({ message: "Invalid end date" })
    endDate: Date;

    @ApiProperty({
        description: "The schedule for classes booking",
        type: ScheduleDTO,
    })
    @Type(() => ScheduleDTO)
    @IsNotEmpty({ message: "schedule is required" })
    schedule: ScheduleDTO;
}
