import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString } from "class-validator";

export class InstantSingleCheckinDto {
    @ApiProperty({ type: String, description: "User ID" })
    @IsNotEmpty({ message: "User ID is required." })
    @IsString({ message: "User ID must be a string." })
    userId: string;

    @ApiProperty({ type: String, description: "Package ID" })
    @IsNotEmpty({ message: "Package ID is required." })
    @IsString({ message: "Package ID must be a string." })
    packageId: string;

    @ApiProperty({ type: String, description: "Invoice ID" })
    @IsNotEmpty({ message: "Invoice ID is required." })
    @IsString({ message: "Invoice ID must be a string." })
    invoiceId: string;

    @ApiProperty({ type: String, description: "Organization ID" })
    @IsNotEmpty({ message: "Organization ID is required." })
    @IsString({ message: "Organization ID must be a string." })
    organizationId: string;

    @ApiProperty({ type: String, description: "Facility ID" })
    @IsNotEmpty({ message: "Facility ID is required." })
    @IsString({ message: "Facility ID must be a string." })
    facilityId: string;
}