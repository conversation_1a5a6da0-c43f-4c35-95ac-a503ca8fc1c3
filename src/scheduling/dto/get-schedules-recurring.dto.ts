import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
    IsNotEmpty,
    IsString,
    IsDate,
    Validate,
    ValidatorConstraint,
    ValidatorConstraintInterface,
    ValidationArguments,
    IsOptional,
    IsEnum,
    ValidateIf
} from "class-validator";
import { Type } from "class-transformer";
import { DateRange } from "src/utils/enums/date-range-enum";
import { MarkAvailabilityType } from "src/utils/enums/mark-availabilityType.enum";

@ValidatorConstraint({ name: "IsEndDateAfterStartDate", async: false })
export class IsEndDateAfterStartDate implements ValidatorConstraintInterface {
    validate(endDate: Date, args: ValidationArguments): boolean {
        const object = args.object as any;
        const startDate: Date = object[args.constraints[0]];
        return startDate && endDate > startDate;
    }

    defaultMessage(args: ValidationArguments): string {
        return "End Date must be greater than Start Date";
    }
}

export class GetScheduleDetailsDto {

    @ApiPropertyOptional({
        description: "Type of date range: 'single' or 'multiple'",
        enum: DateRange,
        default: DateRange.SINGLE
    })
    @IsOptional()
    @IsEnum(DateRange, { message: "Date range must be either 'single' or 'multiple'" })
    dateRange?: DateRange;

    @ValidateIf(o => o.dateRange === DateRange.MULTIPLE)
    @ApiProperty({ description: "Start date of the recurring session to cancel", example: "2025-07-23", required: false })
    @IsNotEmpty({ message: "Start Date is required for 'multiple' dateRange" })
    @Type(() => Date)
    @IsDate({ message: "Start Date must be a valid date" })
    startDate?: Date;

    @ValidateIf(o => o.dateRange === DateRange.MULTIPLE && o.markType === MarkAvailabilityType.CUSTOM)
    @ApiProperty({ description: "End date of the recurring session to cancel", example: "2025-07-29", required: false })
    @IsNotEmpty({ message: "End Date is required for 'multiple' dateRange" })
    @Type(() => Date)
    @IsDate({ message: "End Date must be a valid date" })
    @Validate(IsEndDateAfterStartDate, ["startDate"])
    endDate?: Date;

    @ApiProperty({
        description: "Marking type for recurrence — WEEKLY, DAILY, or CUSTOM. Required for MULTIPLE date range.",
        enum: MarkAvailabilityType,
        example: MarkAvailabilityType.WEEKLY,
        required: false
    })
    @ValidateIf(o => o.dateRange === DateRange.MULTIPLE)
    @IsNotEmpty({ message: "Mark Type is required for MULTIPLE Date Range" })
    @IsEnum(MarkAvailabilityType, { message: "Mark Type must be WEEKLY, DAILY or CUSTOM" })
    markType?: MarkAvailabilityType;
}

export class GetCancellationScheduleDetailsDto {
    @ApiProperty({ description: "Start date of the recurring session to cancel", example: "2025-07-23", required: true })
    @IsNotEmpty({ message: "Start Date is required for 'multiple' dateRange" })
    @Type(() => Date)
    @IsDate({ message: "Start Date must be a valid date" })
    startDate: Date;

    @ApiProperty({ description: "End date of the recurring session to cancel", example: "2025-07-29", required: true })
    @IsNotEmpty({ message: "End Date is required for 'multiple' dateRange" })
    @Type(() => Date)
    @IsDate({ message: "End Date must be a valid date" })
    @Validate(IsEndDateAfterStartDate, ["startDate"])
    endDate: Date;

    @ApiProperty({ description: "Unique identifier for the schedule", example: "64b8f8f8e4b0c8a1d2e3f4g5", required: true })
    @IsString({ message: "Schedule ID must be a string" })
    @IsNotEmpty({ message: "Schedule ID is required" })
    scheduleId: string;
}