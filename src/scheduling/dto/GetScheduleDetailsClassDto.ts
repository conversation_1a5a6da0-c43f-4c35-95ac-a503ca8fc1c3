import { ApiProperty } from "@nestjs/swagger";
import {
    IsNotEmpty,
    IsString,
    IsEnum,
    Matches,
    ValidateIf,
    <PERSON>
} from "class-validator";
import { Type } from "class-transformer";
import { ScheduleStatusType } from "../enums/schedule-status.enum";
import { MarkAvailabilityType } from "src/utils/enums/mark-availabilityType.enum";

export class GetScheduleDetailsClassDto {
    @ApiProperty({
        description: "Organization ID in which scheduling was done",
        example: "615c2f8e2c1ae9123cbb3c1b",
        required: true
    })
    @IsNotEmpty({ message: "Organization ID is required" })
    @IsString({ message: "Organization ID must be a string" })
    organizationId: string;

    @ApiProperty({
        description: "Facility ID where class is scheduled",
        example: "615c2f8e2c1ae9123cbb3c1b",
        required: true
    })
    @IsNotEmpty({ message: "Facility ID is required" })
    @IsString({ message: "Facility ID must be a string" })
    facilityId: string;

    @ApiProperty({
        description: "Trainer ID conducting the class",
        example: "615c2f8e2c1ae9123cbb3c1b",
        required: true
    })
    @IsNotEmpty({ message: "Trainer ID is required" })
    @IsString({ message: "Trainer ID must be a string" })
    trainerId: string;

    @ApiProperty({
        description: "Service category under which class falls",
        example: "615c2f8e2c1ae9123cbb3c1b",
        required: true
    })
    @IsNotEmpty({ message: "Service Category is required" })
    @IsString({ message: "Service Category must be a string" })
    serviceCategory: string;

    @ApiProperty({
        description: "SubType under selected service category",
        example: "615c2f8e2c1ae9123cbb3c1b",
        required: true
    })
    @IsNotEmpty({ message: "SubType is required" })
    @IsString({ message: "SubType must be a string" })
    subType: string;

    @ApiProperty({
        description: "Start date from which scheduling begins (for recurring class)",
        type: Date,
        example: new Date()
    })
    @IsNotEmpty({ message: "Start Date is required" })
    @Type(() => Date)
    startDate: Date;

    @ApiProperty({
        description: "End date up to which scheduling is done (only for CUSTOM Mark Type)",
        type: Date,
        example: new Date()
    })
    @ValidateIf(o => o.markType === MarkAvailabilityType.CUSTOM)
    @IsNotEmpty({ message: "End Date is required for CUSTOM Mark Type" })
    @Type(() => Date)
    endDate: Date;

    @ApiProperty({ description: "From time (HH:mm)", example: "08:00" })
    @IsNotEmpty({ message: "From Time is required" })
    @IsString({ message: "From Time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, {
        message: "From time must be in the format HH:mm"
    })
    from: string;

    @ApiProperty({
        description: "Schedule Status",
        enum: ScheduleStatusType,
        default: ScheduleStatusType.BOOKED,
        example: ScheduleStatusType.BOOKED,
        required: true
    })
    @IsNotEmpty({ message: "Schedule Status is required" })
    @IsEnum(ScheduleStatusType, {
        message: "Schedule Status must be a valid enum value"
    })
    scheduleStatus: ScheduleStatusType;

    @ApiProperty({
        description: "Marking type for recurrence — WEEKLY, DAILY, or CUSTOM",
        enum: MarkAvailabilityType,
        example: MarkAvailabilityType.WEEKLY,
        required: true
    })
    @IsNotEmpty({ message: "Mark Type is required" })
    @IsEnum(MarkAvailabilityType, {
        message: "Mark Type must be WEEKLY, DAILY or CUSTOM"
    })
    markType: MarkAvailabilityType;

    // @ApiProperty({
    //     description: "Capacity of the class",
    //     example: 20,
    // })
    // @Min(1, { message: "Capacity must be at least 1" })
    // @Type(() => Number)
    // capacity: number;
}