import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsArray, IsOptional, ValidateNested } from "class-validator";
import { RecurringTimeSlotsDTO } from "./time-slot.dto";

export class RecurringScheduleDTO {
    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [RecurringTimeSlotsDTO],
        required: false,
    })
    @IsOptional()
    @IsArray({ message: "Monday must be an array of time slots" })
    @ValidateNested({ each: true })
    @Type(() => RecurringTimeSlotsDTO)
    mon: RecurringTimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [RecurringTimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: "Tuesday must be an array of time slots" })
    @ValidateNested({ each: true })
    @Type(() => RecurringTimeSlotsDTO)
    tue: RecurringTimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [RecurringTimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: "Wednesday must be an array of time slots" })
    @ValidateNested({ each: true })
    @Type(() => RecurringTimeSlotsDTO)
    wed: RecurringTimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [RecurringTimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: "Thursday must be an array of time slots" })
    @ValidateNested({ each: true })
    @Type(() => RecurringTimeSlotsDTO)
    thu: RecurringTimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [RecurringTimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: "Friday must be an array of time slots" })
    @ValidateNested({ each: true })
    @Type(() => RecurringTimeSlotsDTO)
    fri: RecurringTimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [RecurringTimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: "Saturday must be an array of time slots" })
    @ValidateNested({ each: true })
    @Type(() => RecurringTimeSlotsDTO)
    sat: RecurringTimeSlotsDTO[];

    @ApiProperty({
        description: "Days of the week the staff is available",
        type: [RecurringTimeSlotsDTO],
        required: false,
    })
    @IsArray({ message: "Sunday must be an array of time slots" })
    @ValidateNested({ each: true })
    @Type(() => RecurringTimeSlotsDTO)
    sun: RecurringTimeSlotsDTO[];
}