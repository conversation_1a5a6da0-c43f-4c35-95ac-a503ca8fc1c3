import { ApiHideProperty, ApiProperty, OmitType, PickType } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsString } from "class-validator";

export class ClassScheduleEnrollDto {
    @ApiProperty({
        description: "Ids of selected schedules",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsNotEmpty({ message: "ScheduleIds is required." })
    scheduleId: string;

    @ApiProperty({
        description: "Ids of selected customers",
        example: ["66cecb432351713ae4447a6b", "66cecb432351713ae4447a6b"],
        required: true,
    })
    @IsNotEmpty({ message: "Users are required." })
    @IsString({ each: true })
    users: string[];
}

export class ClassCancelEnrollDto extends ClassScheduleEnrollDto { }

export class ClassScheduleEnrollPublicDto extends OmitType(ClassScheduleEnrollDto, ['users'] as const) { }

export class ClassCancelEnrollPublicDto extends ClassScheduleEnrollPublicDto { }