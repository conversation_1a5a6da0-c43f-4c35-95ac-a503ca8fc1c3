import { BadRequestException, Body, Controller, Delete, Get, Param, Patch, Post, Put, Query, UseGuards } from "@nestjs/common";
import { Types } from "mongoose";
import { ClassesSchedulingService } from "../services/classes.scheduling.service";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { PolicyAbilityProtected, PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { Response, ResponsePaging } from "src/common/response/decorators/response.decorator";
import { GetOrganizationId } from "src/organization/decorators/organization.decorator";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { ClassScheduleCreateDto, ClassScheduleCreateDtoV1 } from "../dto/class.schedule.create.dto";
import { CheckedInDto, CustomerListDto } from "src/courses/dto/courses.dto";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { ClassScheduleListDto } from "../dto/class.schedule.list.dto";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { PaginationQuery } from "src/common/pagination/decorators/pagination.decorator";
import { PaginationListDto } from "src/common/pagination/dtos/pagination.list.dto";
import { ENUM_PAGINATION_ORDER_DIRECTION_TYPE } from "src/common/pagination/enums/pagination.enum";
import { ClassCancelEnrollDto, ClassScheduleEnrollDto } from "../dto/class.schedule.enroll.dto";
import { ClassScheduleGetUserForEnrollmentDto } from "../dto/class.schedule.get.dto";
import { MongoIdPipeTransform } from "src/common/database/pipes/mongo-id.pipe";
import { ClassScheduleUpdateDto, ClassScheduleUpdateDtoV1 } from "../dto/class.schedule.update.dto";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { ScheduleStatusType } from "../enums/schedule-status.enum";
import { MarkAvailabilityType } from "src/utils/enums/mark-availabilityType.enum";
import { GetScheduleDetailsClassDto } from "../dto/GetScheduleDetailsClassDto";
import { CancelRecurringScheduleDto } from "../dto/CancelRecurringScheduleDto";
import { GetCancellationScheduleDetailsDto, GetScheduleDetailsDto } from "../dto/get-schedules-recurring.dto";

@ApiTags("modules.scheduling.class")
@ApiBearerAuth()
@Controller("scheduling/classes")
export class ClassSchedulingController {
    constructor(
        private classesSchedulingService: ClassesSchedulingService,
        private readonly paginationService: PaginationService,
    ) { }

    @ApiOperation({ summary: "Create Class Schedule" })
    @Response('scheduling.create.class')
    @Post("/create")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async createClassScheduling(
        @GetUser() user: IUserDocument,
        @Body() body: ClassScheduleCreateDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const output = await this.classesSchedulingService.createClassScheduling(organizationId, user, body);
        return {
            data: output,
        };
    }

    @ApiOperation({ summary: "Update Class Schedule" })
    @Response('scheduling.update.class')
    @Put("/update")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async getCustomerList(
        @Body() body: ClassScheduleUpdateDtoV1,
        @GetOrganizationId() organizationId: IDatabaseObjectId,
        @GetUser() user: IUserDocument,
    ): Promise<any> {
        const output = await this.classesSchedulingService.updateClassSchedulingV1(organizationId, user, body);
        return {
            data: output,
        };
    }

    @ApiOperation({ summary: "Update Class Schedule" })
    @Response('scheduling.update.class')
    @Put("/v2/update")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async getCustomerListV1(
        @Body() body: ClassScheduleUpdateDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId,
        @GetUser() user: IUserDocument,
    ): Promise<any> {
        const output = await this.classesSchedulingService.updateClassScheduling(organizationId, user, body);
        return {
            data: output,
        };
    }

    @ApiOperation({ summary: "Class list" })
    @ResponsePaging('scheduling.list')
    @Get("/list")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async getClassList(
        @PaginationQuery({
            defaultOrderBy: 'date',
            defaultOrderDirection: ENUM_PAGINATION_ORDER_DIRECTION_TYPE.ASC,
            availableOrderBy: ['date']
        }) { _limit, _offset, _order }: PaginationListDto,
        @Query() ClassListDto: ClassScheduleListDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId

    ): Promise<any> {
        const { facilityId, trainerId, from, to, roomId, classType, page } = ClassListDto;

        const find: Record<string, any> = {
            classType,
            organizationId
        };

        if (facilityId) {
            find["facilityId"] = {
                $in: facilityId.split(',').map((id) => new Types.ObjectId(id))
            }
        };

        if (trainerId) {
            find["trainerId"] = {
                $in: [trainerId.split(',').map((id) => new Types.ObjectId(id))]
            };
        }

        if (from && to) {
            find["date"] = {
                $gte: new Date(new Date(from).setHours(0, 0, 0, 0)),
                $lte: new Date(new Date(to).setHours(23, 59, 59, 999)),
            };
        } else if (from) {
            find["date"] = {
                $gte: new Date(new Date(from).setHours(0, 0, 0, 0)),
            };
        } else if (to) {
            find["date"] = {
                $lte: new Date(new Date(to).setHours(23, 59, 59, 999)),
            };
        }

        if (roomId) {
            find["roomId"] = { $in: roomId.split(',').map((id) => new Types.ObjectId(id)) };
        }

        if (ClassListDto.status) {
            find["scheduleStatus"] = { $in: ClassListDto.status.split(',').map((status) => status) };
        }

        const { data, count } = await this.classesSchedulingService.getClassesList(find, {
            paging: {
                limit: _limit,
                offset: _offset,
            },
            order: _order,
        });
        return {
            _pagination: {
                total: count,
                totalPage: this.paginationService.totalPage(count, _limit),
                page,
                pageSize: _limit,
            },
            data: data
        };
    }

    @ApiOperation({ summary: "Enroll Schedule" })
    @Response('scheduling.enroll')
    @Post("/enroll")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async enrollScheduling(
        @Body() body: ClassScheduleEnrollDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const output = await this.classesSchedulingService.enrollScheduling(organizationId, body);
        return {
            data: output,
        };
    }

    @ApiOperation({ summary: "Enroll Schedule" })
    @Response('scheduling.enroll')
    @Post("/enroll/cancel")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async cancelEnrollScheduling(
        @Body() body: ClassCancelEnrollDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const output = await this.classesSchedulingService.cancelEnrollScheduling(organizationId, body);
        return {
            data: output,
        };
    }

    @ApiOperation({ summary: "Customer list for enrollment" })
    @ResponsePaging('scheduling.customer.list')
    @Get("/customer/list")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async getCustomerListForScheduling(
        @PaginationQuery({
            defaultOrderBy: 'name',
            defaultOrderDirection: ENUM_PAGINATION_ORDER_DIRECTION_TYPE.DESC,
            availableOrderBy: ['name'],
            availableSearch: ['name', 'email', 'mobile']
        }) { _limit, _offset, _order, _search }: PaginationListDto,
        @Query() customerListDto: ClassScheduleGetUserForEnrollmentDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId,
    ): Promise<any> {
        const output = await this.classesSchedulingService.getCustomerList(organizationId, {
            ...customerListDto,
            _search: _search
        }, {
            paging: {
                limit: _limit,
                offset: _offset,
            },
            order: _order,
        });
        return {
            _pagination: {
                total: output.count,
                totalPage: this.paginationService.totalPage(output.count, _limit),
                page: customerListDto.page,
                pageSize: _limit,
            },
            data: output.list,
        };
    }

    @ApiOperation({ summary: "Customer list for enrollment" })
    @ResponsePaging('scheduling.customer.list')
    @Get("/participant/list")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async getParticipantList(
        @PaginationQuery({
            defaultOrderBy: 'name',
            defaultOrderDirection: ENUM_PAGINATION_ORDER_DIRECTION_TYPE.DESC,
            availableOrderBy: ['name'],
            availableSearch: ['name', 'email', 'mobile']
        }) { _limit, _offset, _order, _search }: PaginationListDto,
        @Query() customerListDto: ClassScheduleGetUserForEnrollmentDto,
    ): Promise<any> {
        const output = await this.classesSchedulingService.getParticipantList({
            ...customerListDto,
            _search: _search
        }, {
            paging: {
                limit: _limit,
                offset: _offset,
            },
            order: _order,
        });
        return {
            _pagination: {
                total: output.count,
                totalPage: this.paginationService.totalPage(output.count, _limit),
                page: customerListDto.page,
                perPage: customerListDto.perPage,
            },
            data: output.list,
        };
    }

    @ApiOperation({ summary: "Scheduling Details" })
    @Response('scheduling.get')
    @Get("/:scheduleId/get")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async getSchedulingDetails(
        @Param("scheduleId", MongoIdPipeTransform) scheduleId: IDatabaseObjectId,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const data = await this.classesSchedulingService.getScheduleDetails(scheduleId, organizationId);
        return {
            data,
        };
    }

    @Post("/get-recurring/:scheduleId")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get Recurring Scheduling Details" })
    async getSchedulingRecurringDetails(
        @GetUser() user: any,
        @Param("scheduleId", MongoIdPipeTransform) scheduleId: IDatabaseObjectId,
        @Body() getScheduleDetailsDto: GetScheduleDetailsDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const data = await this.classesSchedulingService.getScheduleDetailsClass(user, organizationId, scheduleId, getScheduleDetailsDto);
        return {
            message: "Schedule fetched successfully",
            data,
        };
    }

    @Post("/getCancellationDetails")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get Recurring Scheduling Details" })
    async getCancellationDetails(
        @GetUser() user: any,
        @Body() getScheduleDetailsDto: GetCancellationScheduleDetailsDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const data = await this.classesSchedulingService.getCancellationDetails(user, organizationId, getScheduleDetailsDto);
        return {
            message: "Schedule fetched successfully",
            data,
        };
    }
    

    @ApiOperation({ summary: "Cancel Schedule" })
    @Response('scheduling.cancel')
    @Patch("/:scheduleId/cancel")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async cancelSchedule(
        @GetUser() user: any,
        @Param("scheduleId", MongoIdPipeTransform) scheduleId: IDatabaseObjectId,
        @Body() cancelDto: CancelRecurringScheduleDto,
    ): Promise<any> {
        let output = await this.classesSchedulingService.cancelSchedule(user, scheduleId,cancelDto);
        return {
            data: output
        };
    }

    @ApiOperation({ summary: "Delete Schedule" })
    @Delete("/:scheduleId/delete")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async deleteSchedule(
        @Param("scheduleId", MongoIdPipeTransform) scheduleId: IDatabaseObjectId,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        await this.classesSchedulingService.deleteSchedule(organizationId, scheduleId);
        return true;
    }

    @ApiOperation({ summary: "Check In" })
    @Response('scheduling.checkin')
    @Patch("/checkin")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    async checkIn(
        @GetOrganizationId() organizationId: IDatabaseObjectId,
        @Body() body: CheckedInDto,
    ): Promise<any> {
        const output = await this.classesSchedulingService.checkIn(organizationId, body);
        return {
            data: output,
        };
    }

}
