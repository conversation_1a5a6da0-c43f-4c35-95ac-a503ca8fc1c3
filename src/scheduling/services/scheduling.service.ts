import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { ClientSession, Model, PipelineStage, Types } from "mongoose";
import { FacilityAvailability } from "src/facility/schemas/facility-availability.schema";
import { Facility } from "src/facility/schemas/facility.schema";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { User } from "src/users/schemas/user.schema";
import { TransactionService } from "src/utils/services/transaction.service";
import { CheckStaffAvailabilityDto } from "../dto/check-staff-availability.dto";
import { CreateSchedulingDto } from "../dto/create-scheduling.dto";
import { Pricing, PricingDocument, Services } from "src/organization/schemas/pricing.schema";
import { Purchase, PurchaseDocument, Suspensions } from "src/users/schemas/purchased-packages.schema";
import { Scheduling } from "../schemas/scheduling.schema";
import { Room } from "src/room/schema/room.schema";
import { SessionType } from "src/utils/enums/session-type.enum";
import { UpdateSchedulingDto } from "../dto/update-scheduling.dto";
import { ScheduleStatusType } from "../enums/schedule-status.enum";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { GetSchedulesDto } from "../dto/get-scheduling.dto";
import { format, addDays, isAfter } from "date-fns";
import { StaffAvailability } from "src/staff/schemas/staff-availability";
import { StaffAvailabilityEnum } from "src/utils/enums/staff-availability.enum";
import { ClassType } from "src/utils/enums/class-type.enum";
import { PayRate } from "src/staff/schemas/pay-rate.schema";
import { MailService } from "src/mail/services/mail.service";
import { Clients } from "src/users/schemas/clients.schema";
import moment from "moment";
import { WaitTimeGatewayService } from "src/wait-time/gateway/wait-time-gateway.service";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { DurationUnit } from "src/utils/enums/duration-unit.enum";
import { GetSchedulesByUserDto } from "../dto/get-scheduling-user.dto";
import { ActiveTimeFrame } from "src/utils/schemas/active-time-frame.schema";
import { InstantCheckinDto } from "../dto/instant-checkin.dto";
import { DateRange } from "src/utils/enums/date-range-enum";
import { DateTime } from "luxon";
import { InstantSingleCheckinDto } from "../dto/instant-single-checkin.dto";
import { TimeSlots } from "src/facility/dto/update-facility.dto";
import { ScheduleDayDto } from "../dto/time-slots.schedule.dto";
import { IScheduleDates } from "../interfaces/class.schedule.interface";
import { Enrollment } from "src/courses/schemas/enrollment.schema";
import { MarkAvailabilityType } from "src/utils/enums/mark-availabilityType.enum";
import { RecurringScheduleDTO } from "../dto/schedule-week.dto";
import { RecurringTimeSlotsDTO } from "../dto/time-slot.dto";
import { DayIndexToWeekMap } from "src/utils/enums/days-of-week.enum";
import { GetScheduleDetailsDto } from "../dto/get-schedules-recurring.dto";
import { CancelRecurringScheduleDto } from "../dto/CancelRecurringScheduleDto";
import { base } from "@faker-js/faker/.";


@Injectable()
export class SchedulingService {
    constructor(
        @InjectModel(User.name) private UserModel: Model<User>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        @InjectModel(FacilityAvailability.name) private FacilityAvailabilityModel: Model<FacilityAvailability>,
        @InjectModel(StaffProfileDetails.name) private StaffProfileModel: Model<StaffProfileDetails>,
        @InjectModel(StaffAvailability.name) private StaffAvailabilityModel: Model<StaffAvailability>,
        @InjectModel(Enrollment.name) private EnrollmentModel: Model<Enrollment>,
        @InjectModel(Services.name) private ServicesModel: Model<Services>,
        @InjectModel(Pricing.name) private PricingModel: Model<Pricing>,
        @InjectModel(Purchase.name) private PurchaseModel: Model<Purchase>,
        @InjectModel(PayRate.name) private PayRateModel: Model<PayRate>,
        @InjectModel(Room.name) private RoomModel: Model<Room>,
        @InjectModel(Scheduling.name) private SchedulingModel: Model<Scheduling>,
        @InjectModel(Clients.name) private ClientsModel: Model<Clients>,
        private readonly transactionService: TransactionService,
        private readonly mailService: MailService,
        private waitTimeGatewayService: WaitTimeGatewayService,
    ) { }


    private validateSchedule(schedule: RecurringScheduleDTO, pricingDuration: number) {
        for (const [day, slots] of Object.entries(schedule)) {
            // Sort by 'from' time to make overlap checking reliable
            const sortedSlots = slots.sort((a, b) => a.from.localeCompare(b.from));

            for (let i = 0; i < sortedSlots.length; i++) {
                const slot = sortedSlots[i];

                const fromTime = new Date(`1970-01-01T${slot.from}:00Z`);
                const toTime = new Date(`1970-01-01T${slot.to}:00Z`);
                const diffInMinutes = (toTime.getTime() - fromTime.getTime()) / 60000;

                // 1. Check if time range and duration match
                if (diffInMinutes !== slot.durationInMinutes) {
                    throw new BadRequestException(
                        `Mismatch in time range and duration on ${day.toUpperCase()}: From ${slot.from} to ${slot.to} is ${diffInMinutes} mins, but durationInMinutes is ${slot.durationInMinutes} mins.`
                    );
                }

                // 2. Check duration is at least pricingDuration
                if (slot.durationInMinutes < pricingDuration) {
                    throw new BadRequestException(
                        `On ${day.toUpperCase()}, slot ${slot.from}–${slot.to}: Duration must be at least ${pricingDuration} minutes`
                    );
                }

                // 3. Check duration is a multiple of pricingDuration
                if (slot.durationInMinutes % pricingDuration !== 0) {
                    throw new BadRequestException(
                        `On ${day.toUpperCase()}, slot ${slot.from}–${slot.to}: Duration must be a multiple of ${pricingDuration} minutes`
                    );
                }

                // 4. Check for overlaps with next slot
                if (i < sortedSlots.length - 1) {
                    const next = sortedSlots[i + 1];
                    if (this.isTimeOverlap(slot.from, slot.to, next.from, next.to)) {
                        throw new BadRequestException(
                            `On ${day.toUpperCase()}, overlapping slots between ${slot.from}–${slot.to} and ${next.from}–${next.to}`
                        );
                    }
                }
            }
        }
    }

    private isTimeOverlap(from1: string, to1: string, from2: string, to2: string): boolean {
        const timeToMinutes = (time: string) => {
            const [h, m] = time.split(':').map(Number);
            return h * 60 + m;
        };

        const start1 = timeToMinutes(from1);
        const end1 = timeToMinutes(to1);
        const start2 = timeToMinutes(from2);
        const end2 = timeToMinutes(to2);

        return Math.max(start1, start2) < Math.min(end1, end2);
    }

    async findOrganization(checkStaffAvailabilityDto: CheckStaffAvailabilityDto): Promise<any> {
        let facilityDetails = await this.FacilityModel.findOne({ _id: checkStaffAvailabilityDto.facilityId }, { organizationId: 1 });
        if (!facilityDetails) throw new BadRequestException("Facility not found");
        return facilityDetails["organizationId"];
    }

    async availableStaff(checkStaffAvailabilityDto: CheckStaffAvailabilityDto, _organizationId: string): Promise<any> {
        let availableDates = await this.findAvailableDatesInRange(checkStaffAvailabilityDto);
        if (availableDates.length == 0) throw new BadRequestException("Incorrect dates selected");
    }

    async findAvailableDatesInRange(checkStaffAvailabilityDto: CheckStaffAvailabilityDto) {
        const result: string[] = [];
        const start = new Date(checkStaffAvailabilityDto.fromDate);
        const end = new Date(checkStaffAvailabilityDto.endDate);

        const maxEndDate = new Date(start);
        maxEndDate.setMonth(start.getMonth() + 1);

        if (end > maxEndDate) {
            throw new BadRequestException("Date range cannot exceed 1 month limit");
        }

        // Map days of the week to keys in ScheduleDTO
        const daysMap = {
            0: checkStaffAvailabilityDto.schedule.sun,
            1: checkStaffAvailabilityDto.schedule.mon,
            2: checkStaffAvailabilityDto.schedule.tue,
            3: checkStaffAvailabilityDto.schedule.wed,
            4: checkStaffAvailabilityDto.schedule.thu,
            5: checkStaffAvailabilityDto.schedule.fri,
            6: checkStaffAvailabilityDto.schedule.sat,
        };

        // Normalize start and end times to 00:00:00 and 23:59:59
        start.setUTCHours(0, 0, 0, 0);
        end.setUTCHours(23, 59, 59, 999);

        while (start <= end) {
            const dayOfWeek = start.getUTCDay(); // Get day of the week (0=Sunday, ..., 6=Saturday)
            const timeSlots = daysMap[dayOfWeek];

            // Check if the array for the day is non-empty
            if (timeSlots && timeSlots.length > 0) {
                result.push(new Date(start).toISOString()); // Add the date in YYYY-MM-DD format
            }

            start.setUTCDate(start.getUTCDate() + 1); // Move to the next day
        }

        return result;
    }

    async buildScheduleDates(startDate: Date, endDate: Date, schedule: ScheduleDayDto, appointmentDuration?: number): Promise<IScheduleDates[]> {
        const result: IScheduleDates[] = [];
        const start = moment(startDate).utc(true).startOf('day').toDate();
        const end = moment(endDate).utc(true).endOf('day').toDate();

        while (start <= end) {
            const dayKey = start.toLocaleString("en-US", { weekday: "short" }).toLowerCase();
            const timeSlots: RecurringTimeSlotsDTO[] = schedule[dayKey];
            if (timeSlots && timeSlots.length > 0) {
                for (const slot of timeSlots) {
                    const slotDateTime = moment(start).add(slot.from, 'hours');
                    const nowIST = moment();
                    if (slotDateTime.isBefore(nowIST)) {
                        throw new BadRequestException("Class cannot start in the past.");
                    }

                    const fromTime = moment(slot.from, 'HH:mm');
                    const toTime = moment(slot.to, 'HH:mm');
                    const calculatedDuration = toTime.diff(fromTime, 'minutes');

                    let requestedSessions = 0
                    if (appointmentDuration) {
                        requestedSessions = calculatedDuration / (appointmentDuration);
                        const remainder = calculatedDuration % (appointmentDuration);
                        if (remainder !== 0) {
                            throw new BadRequestException(`Duration must be a multiple of ${appointmentDuration} minutes on ${DayIndexToWeekMap[start.getUTCDay()]}`);
                        };
                    }

                    result.push({
                        date: new Date(start),
                        day: dayKey,
                        from: slot.from,
                        to: slot.to,
                        duration: slot.durationInMinutes,
                        sessions: requestedSessions,
                        capacity: slot?.classCapacity,
                    });
                }
            }
            start.setUTCDate(start.getUTCDate() + 1);
        }

        return result;
    }

    validateActiveTimeFrame(activeTimeFrames: ActiveTimeFrame[], from: Date, to: Date): void {
        // If no time frames are defined, the item is always available
        if (!activeTimeFrames || activeTimeFrames?.length === 0) {
            return;
        }

        const fromDay = from.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
        const fromTime = from.toTimeString().substring(0, 5); // Format: HH:MM
        const toTime = to.toTimeString().substring(0, 5)

        // Check if current time falls within any of the active time frames
        const isWithinActiveTimeFrame = activeTimeFrames.some(timeFrame => {
            // Check specific date
            if (timeFrame.date) {
                const frameDate = new Date(timeFrame.date);
                frameDate.setHours(0, 0, 0, 0);
                const compareDate = new Date(from);
                compareDate.setHours(0, 0, 0, 0);
                // If dates don't match, this frame doesn't apply
                if (frameDate.getTime() !== compareDate.getTime()) {
                    return false;
                }
            }
            // Check day of week
            else if (timeFrame.dayOfWeek && timeFrame.dayOfWeek.toLowerCase() !== fromDay) {
                return false;
            }

            // Check time range
            return timeFrame.startTime <= fromTime && toTime <= timeFrame.endTime;
        });

        if (!isWithinActiveTimeFrame) {
            const now = new Date();

            let nextAvailable: { day: string; startTime: string; endTime: string } | null = null;

            for (let i = 0; i < 7; i++) {
                const checkDate = new Date(now);
                checkDate.setDate(now.getDate() + i);
                const checkDay = checkDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();

                const matchingSlot = activeTimeFrames.find(tf => {
                    if (tf.date) {
                        const tfDate = new Date(tf.date);
                        tfDate.setHours(0, 0, 0, 0);
                        checkDate.setHours(0, 0, 0, 0);
                        return tfDate.getTime() === checkDate.getTime();
                    } else {
                        return tf.dayOfWeek?.toLowerCase() === checkDay;
                    }
                });

                if (matchingSlot) {
                    nextAvailable = {
                        day: matchingSlot.dayOfWeek
                            ? matchingSlot.dayOfWeek.charAt(0).toUpperCase() + matchingSlot.dayOfWeek.slice(1)
                            : new Date(matchingSlot.date!).toLocaleDateString('en-US', { weekday: 'long' }),
                        startTime: matchingSlot.startTime,
                        endTime: matchingSlot.endTime
                    };
                    break;
                }
            }

            const message = nextAvailable
                ? `This package is only available between ${nextAvailable.day}, ${nextAvailable.startTime} - ${nextAvailable.endTime}`
                : `This package is not available at the current time.`;

            throw new BadRequestException(message);
        }
    }

    private isTimeOverlapping(slot1: { from: string; to: string }, slot2: { from: String; to: String }): boolean {
        return slot1.from < slot2.to && slot1.to > slot2.from;
    }

    private isTimeWithinAvailableSlot(requestedSlot: { from: string; to: string }, availableSlot: { from: string; to: string }): boolean {
        // return requestedSlot.from >= availableSlot.from && requestedSlot.to <= availableSlot.to;
        return (
            new Date(`1970-01-01T${requestedSlot.from}:00`) >= new Date(`1970-01-01T${availableSlot.from}:00`) &&
            new Date(`1970-01-01T${requestedSlot.to}:00`) <= new Date(`1970-01-01T${availableSlot.to}:00`)
        );
    }

    async validateIsStaffBusy(trainerId: string, date: Date, from: string, to: string, scheduleId?: string) {
        // Check if trainer busy with another appointment
        const isTrainerBusy = await this.SchedulingModel.count({
            _id: { $ne: scheduleId },
            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
            trainerId: trainerId,
            date: date,
            $or: [{ from: { $gte: from, $lt: to } }, { to: { $gt: from, $lte: to } }],
        });
        if (isTrainerBusy) {
            throw new BadRequestException("Staff already occupied with another appointment");
        }
        return false;
    }

    // Common method to validate staff availability
    async validateStaffAvailability(
        serviceCategoryId: any,
        appointmentType: any,
        trainerId: string,
        date: Date,
        from: string,
        to: string,
        facilityId: string,
        scheduleId?: string,
    ): Promise<boolean> {
        // Fetch staff availability for the given date
        const staff = await this.UserModel.findOne({ _id: new Types.ObjectId(trainerId) }, { firstName: 1, lastName: 1 });
        if (!staff) throw new BadRequestException("Staff not found");
        const staffAvailabilities = await this.StaffAvailabilityModel.findOne({ userId: new Types.ObjectId(trainerId), date: moment(date).utc(true).startOf('day').toDate(), facilityId: new Types.ObjectId(facilityId) });
        if (!staffAvailabilities) {
            throw new BadRequestException(`${staff.firstName} ${staff.lastName || ""} is not available for the selected services`);
        }

        // Get the available and unavailable time slots
        const availableSlots = staffAvailabilities.timeSlots.filter(
            (item) => item.availabilityStatus === StaffAvailabilityEnum.AVAILABLE && item.classType === ClassType.PERSONAL_APPOINTMENT,
        );
        const unavailableSlots = staffAvailabilities.timeSlots.filter((item) => item.availabilityStatus === StaffAvailabilityEnum.UNAVAILABLE);

        const reqFromDate = new Date(`1970-01-01T${from}:00`);
        const reqToDate = new Date(`1970-01-01T${to}:00`);

        // Check for conflicts with unavailable slots
        for (let slot of unavailableSlots) {
            const fromDate = new Date(`1970-01-01T${slot.from}:00`);
            const toDate = new Date(`1970-01-01T${slot.to}:00`);
            if (fromDate <= reqFromDate && toDate >= reqToDate) {
                throw new BadRequestException(`${staff.firstName} ${staff.lastName || ""} is not available at the given time`);
            }
        }

        await this.validateIsStaffBusy(trainerId, date, from, to, scheduleId);

        // Collect all unique payRateIds from available slots
        const payRateIds = new Set<string>();
        for (let slot of availableSlots) {
            const fromDate = new Date(`1970-01-01T${slot.from}:00`);
            const toDate = new Date(`1970-01-01T${slot.to}:00`);
            if (fromDate <= reqFromDate && toDate >= reqToDate) {
                if (Array.isArray(slot.payRateIds)) {
                    slot.payRateIds.forEach((id: string) => payRateIds.add(id));
                }
            }
        }

        // If no payRateIds were found, staff is not available
        if (payRateIds.size === 0) {
            throw new BadRequestException(`${staff.firstName} ${staff.lastName || ""} is not available at the given time`);
        }

        // Query pay rates for all collected payRateIds
        const payRates = await this.PayRateModel.find({
            _id: { $in: Array.from(payRateIds) },
            serviceCategory: serviceCategoryId,
            appointmentType: appointmentType,
        });

        if (payRates.length > 0) {
            return true; // Staff is available
        }

        throw new BadRequestException(`${staff.firstName} ${staff.lastName || ""} is not available for the given sub type`);
    }

    // Common method to validate staff availability
    async validateStaffSpecialization(
        trainerId: string,
        serviceCategoryId: any,
        appointmentType: any,
    ): Promise<boolean> {
        // Query pay rates for all collected payRateIds
        const payRates = await this.PayRateModel.exists({
            userId: trainerId,
            serviceCategory: serviceCategoryId,
            appointmentType: appointmentType,
        }).sort({ updatedAt: -1 });

        if (!payRates) {
            throw new BadRequestException("Trainer is not specialized in the selected service");
        }

        return true;
    }

    private async consumeSessionUpdateInPurchase(purchaseId, sessions: number, session?: ClientSession) {
        await this.PurchaseModel.updateOne(
            { _id: purchaseId },
            {
                $inc: {
                    sessionConsumed: sessions,
                },
            },
        ).session(session);
        return true;
    }

    private async consumeSessionUpdateInPurchaseMultiple(purchaseId: Types.ObjectId, sessions: number, session: ClientSession) {
        await this.PurchaseModel.updateOne(
            { _id: purchaseId },
            {
                $inc: {
                    sessionConsumed: sessions,
                },
            },
            { session }
        );
        return true;
    }

    // Common Method to Validate Facility Availability
    public async validateFacilityAvailabilityV1(requestedDate: Date, requestedSlot: TimeSlots, facilitiesId: string) {
        const date = new Date(requestedDate);
        const dayOfWeek = date.toLocaleString("en-US", { weekday: "short" }).toLowerCase();
        const startOfDay = new Date(date.setHours(0, 0, 0, 0));
        const endOfDay = new Date(date.setHours(23, 59, 59, 999));
        const days = {
            mon: "Monday",
            tue: "Tuesday",
            wed: "Wednesday",
            thu: "Thursday",
            fri: "Friday",
            sat: "Saturday",
            sun: "Sunday",
        };
        const facilityRecords = await this.FacilityAvailabilityModel.find({
            facilityId: new Types.ObjectId(facilitiesId),
            $or: [
                {
                    type: "unavailable",
                    fromDate: { $lte: endOfDay },
                    endDate: { $gte: startOfDay },
                },
                {
                    type: "available",
                },
            ],
        });

        const unavailableRecord = facilityRecords.find((record) => record.type === "unavailable");
        const facilityAvailability = facilityRecords.find((record) => record.type === "available");

        if (unavailableRecord) {
            for (const unavailableSlot of unavailableRecord.time) {
                if (this.isTimeOverlapping(requestedSlot, unavailableSlot)) {
                    throw new BadRequestException(`Facility is unavailable from ${unavailableSlot.from} to ${unavailableSlot.to}`);
                }
            }
        }

        if (!facilityAvailability) {
            throw new BadRequestException("No available working hours found for the facility.");
        }

        const facilityWorkingHours = facilityAvailability.workingHours;

        const availableSlots = facilityWorkingHours[dayOfWeek];
        if (!availableSlots) {
            throw new BadRequestException(`Facility is not available on ${days[dayOfWeek.toLowerCase()]}.`);
        }

        const slotIsWithinAvailability = availableSlots.some((availableSlot: any) => this.isTimeWithinAvailableSlot(requestedSlot, availableSlot));

        if (!slotIsWithinAvailability) {
            const availableSlotsText = availableSlots.map((slot) => `${slot.from} to ${slot.to}`).join(", ");
            throw new BadRequestException(
                `Requested slot from ${requestedSlot.from} to ${requestedSlot.to} on ${days[dayOfWeek.toLowerCase()]
                } is outside facility's available hours. (Working hours for this day are: ${availableSlotsText}).`,
            );
        }
        return true;
    }

    public async validateFacilityAvailability(startDate: Date, endDate: Date, schedule: ScheduleDayDto, facilitiesId: string) {
        const startOfDay = new Date(startDate.setHours(0, 0, 0, 0));
        const endOfDay = new Date(endDate.setHours(23, 59, 59, 999));
        const days = {
            mon: "Monday",
            tue: "Tuesday",
            wed: "Wednesday",
            thu: "Thursday",
            fri: "Friday",
            sat: "Saturday",
            sun: "Sunday",
        };
        const facilityRecords = await this.FacilityAvailabilityModel.find({
            facilityId: new Types.ObjectId(facilitiesId),
            $or: [
                {
                    type: "unavailable",
                    fromDate: { $lte: endOfDay },
                    endDate: { $gte: startOfDay },
                },
                {
                    type: "available",
                },
            ],
        }).sort({ createdAt: 1 });

        const unavailableRecord = facilityRecords.find((record) => record.type === "unavailable");
        const facilityAvailability = facilityRecords.find((record) => record.type === "available");

        if (unavailableRecord) {
            for (const unavailableSlot of unavailableRecord.time) {
                for (const dayOfWeek in schedule) {
                    const requestedSlots = schedule[dayOfWeek];
                    for (const requestedSlot of requestedSlots) {
                        if (this.isTimeOverlapping(requestedSlot, unavailableSlot)) {
                            throw new BadRequestException(`Facility is unavailable from ${unavailableSlot.from} to ${unavailableSlot.to} on ${days[dayOfWeek.toLowerCase()]} `);
                        }
                    }
                }
            }
        }

        if (!facilityAvailability) {
            throw new BadRequestException("No available working hours found for the facility.");
        }

        const facilityWorkingHours = facilityAvailability.workingHours;

        for (const dayOfWeek in schedule) {
            const requestedSlots = schedule[dayOfWeek];
            const availableSlots = facilityWorkingHours[dayOfWeek];

            if (!availableSlots && requestedSlots.length > 0) {
                throw new BadRequestException(`Facility is not available on ${days[dayOfWeek.toLowerCase()]}.`);
            }

            for (const requestedSlot of requestedSlots) {
                const slotIsWithinAvailability = availableSlots.some((availableSlot: any) => this.isTimeWithinAvailableSlot(requestedSlot, availableSlot));

                if (!slotIsWithinAvailability) {
                    const availableSlotsText = availableSlots.map((slot) => `${slot.from} to ${slot.to}`).join(", ");
                    throw new BadRequestException(
                        `Requested slot from ${requestedSlot.from} to ${requestedSlot.to} on ${days[dayOfWeek.toLowerCase()]} is outside facility's available hours. (Working hours for this day are: ${availableSlotsText}).`,
                    );
                }
            }
        }
        return true;
    }

    // Common Method to Validate Package Eligibility
    public async validatePackageEligibility(
        userId: string,
        date: Date,
        from: string,
        to: string,
        pricing: PricingDocument,
        purchase: PurchaseDocument,
        requestedSessions: number,
        scheduleId?: string,
        scheduledSession: number = 0,
        session?: any,
    ): Promise<PurchaseDocument> {
        if (!purchase || !pricing || (purchase?.consumers && !purchase?.consumers.find((consumer) => consumer.toString() === userId.toString()))) {
            throw new BadRequestException("Access Denied as the user is not eligible for this service");
        }

        const packageStart = new Date(purchase.startDate);
        packageStart.setHours(0, 0, 0, 0);

        const packageEnd = new Date(purchase.endDate);
        packageEnd.setHours(23, 59, 59, 999); // Set to end of end date
        const bookingFromDateTime = new Date(`${date.toISOString().split("T")[0]}T${from}`);
        const bookingToDateTime = new Date(`${date.toISOString().split("T")[0]}T${to}`);

        if (bookingFromDateTime < packageStart) {
            throw new BadRequestException(
                `This package is valid only from ${moment(packageStart).format('Do MMM')}${moment(packageStart).year() !== moment().year() ? ' ' + moment(packageStart).format('YYYY') : ''
                }.`
            );
        }

        if (bookingToDateTime > packageEnd) {
            throw new BadRequestException(
                `This package is valid only till ${moment(packageEnd).format('Do MMM')}${moment(packageEnd).year() !== moment().year() ? ' ' + moment(packageEnd).format('YYYY') : ''
                }.`
            );
        }

        this.validateActiveTimeFrame(pricing.activeTimeFrames, bookingFromDateTime, bookingToDateTime)

        // Check if the package is suspended
        const isSuspended =
            purchase.suspensions &&
            purchase.suspensions.find((suspension: Suspensions) => {
                return suspension.fromDate <= date && suspension.endDate >= date && !suspension.isResumed;
            });
        if (isSuspended) {
            throw new BadRequestException(`This membership is suspended from ${format(isSuspended.fromDate, "dd/MM/yyyy")} to ${format(isSuspended.endDate, "dd/MM/yyyy")}`);
        }

        // Check the consumed sessions
        const userConsumedSessions = purchase.sessionConsumed;

        let availableSessions: number = 0;

        switch (purchase.sessionType) {
            case SessionType.SINGLE:
                availableSessions = 1 - (userConsumedSessions - scheduledSession); // Only 1 session available for SINGLE session type
                break;
            case SessionType.ONCEADAY:
                const sessionsCounts = await this.SchedulingModel.aggregate([
                    {
                        $match: {
                            _id: { $ne: scheduleId },
                            purchaseId: purchase._id,
                            clientId: new Types.ObjectId(userId),
                            date: date,
                            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                        },
                    },
                    { $group: { _id: null, sessions: { $sum: "$sessions" } } },
                ], { session });
                const userSessionsForTheDay = sessionsCounts.length ? sessionsCounts[0]["sessions"] : 0;
                if (userSessionsForTheDay > 0 || requestedSessions > 1) {
                    throw new BadRequestException("Only one booking allowed per day with this package");
                }
                availableSessions = 1;
                break;
            case SessionType.MULTIPLE:
                availableSessions = purchase?.totalSessions - (userConsumedSessions - scheduledSession);
                break;
            case SessionType.DAY_PASS:
                const sessionsCount = await this.SchedulingModel.aggregate([
                    {
                        $match: {
                            _id: { $ne: scheduleId },
                            purchaseId: purchase._id,
                            clientId: new Types.ObjectId(userId),
                            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                        },
                    },
                    {
                        $group: {
                            _id: "$date",
                        },
                    },
                ], { session });
                const bookedDates = sessionsCount.map((item) => item._id.toISOString().split("T")[0]);
                const requestDate = new Date(date).toISOString().split("T")[0];
                const isDateAlreadyUsed = bookedDates.includes(requestDate);
                const consumedDayPassLimit = bookedDates.length;
                const assignedDayPassLimit = purchase?.dayPassLimit;
                const isEligibleForScheduling = assignedDayPassLimit - consumedDayPassLimit;

                if (isEligibleForScheduling > 0) {
                    availableSessions = Number.POSITIVE_INFINITY;
                } else if (isEligibleForScheduling <= 0) {
                    if (isDateAlreadyUsed) {
                        availableSessions = Number.POSITIVE_INFINITY;
                    } else {
                        throw new BadRequestException(
                            `Day pass limit reached ${assignedDayPassLimit} day(s). Please select an existing scheduled date to book a session.`,);
                    }
                }
                break;
            case SessionType.UNLIMITED:
                availableSessions = 0; // Unlimited sessions
                const sessionPerDay = purchase?.sessionPerDay || 0;
                if (sessionPerDay) {
                    const userSessionsForTheDay = await this.SchedulingModel.count({
                        _id: { $ne: scheduleId },
                        purchaseId: purchase._id,
                        clientId: new Types.ObjectId(userId),
                        date: date,
                        scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                    });
                    // const userSessionsForTheDay = sessionsCount.length ? sessionsCount[0]['sessions'] : 0;
                    if (userSessionsForTheDay >= sessionPerDay) {
                        throw new BadRequestException(`Only ${sessionPerDay} booking allowed per day with this package`);
                    }
                    availableSessions = sessionPerDay - userSessionsForTheDay;
                }
                break;
            default:
                throw new BadRequestException("Invalid session type");
        }

        if (!availableSessions) {
            throw new BadRequestException(`Your session for the assigned package are exhausted`);
        }
        // Check if the requested sessions exceed the available sessions
        if (requestedSessions > availableSessions) {
            throw new BadRequestException(`Number of sessions requested is more than those available in this package`);
        }
        return purchase;
    }

    public async validatePackageEligibilityMultiple(
        userId: string,
        date: Date,
        from: string,
        to: string,
        pricing: PricingDocument,
        purchase: PurchaseDocument,
        requestedSessions: number,
        sessionConsumed: number,
        session?: any,
        scheduleId?: string,
        scheduledSession: number = 0,
    ): Promise<PurchaseDocument> {
        if (!purchase || !pricing || (purchase?.consumers && !purchase?.consumers.find((consumer) => consumer.toString() === userId.toString()))) {
            throw new BadRequestException("Access Denied as the user is not eligible for this service");
        }

        const packageStart = new Date(purchase.startDate);
        packageStart.setHours(0, 0, 0, 0);

        const packageEnd = new Date(purchase.endDate);
        packageEnd.setHours(23, 59, 59, 999); // Set to end of end date
        const bookingFromDateTime = new Date(`${date.toISOString().split("T")[0]}T${from}`);
        const bookingToDateTime = new Date(`${date.toISOString().split("T")[0]}T${to}`);

        if (bookingFromDateTime < packageStart) {
            throw new BadRequestException(
                `This package is valid only from ${moment(packageStart).format('Do MMM')}${moment(packageStart).year() !== moment().year() ? ' ' + moment(packageStart).format('YYYY') : ''
                }.`
            );
        }

        if (bookingToDateTime > packageEnd) {
            throw new BadRequestException(
                `This package is valid only till ${moment(packageEnd).format('Do MMM')}${moment(packageEnd).year() !== moment().year() ? ' ' + moment(packageEnd).format('YYYY') : ''
                }.`
            );
        }

        this.validateActiveTimeFrame(pricing.activeTimeFrames, bookingFromDateTime, bookingToDateTime)

        // Check if the package is suspended
        const isSuspended =
            purchase.suspensions &&
            purchase.suspensions.find((suspension: Suspensions) => {
                return suspension.fromDate <= date && suspension.endDate >= date && !suspension.isResumed;
            });
        if (isSuspended) {
            throw new BadRequestException(`This membership is suspended from ${format(isSuspended.fromDate, "dd/MM/yyyy")} to ${format(isSuspended.endDate, "dd/MM/yyyy")}`);
        }

        // Check the consumed sessions
        const userConsumedSessions = sessionConsumed;

        let availableSessions: number = 0;

        switch (purchase.sessionType) {
            case SessionType.SINGLE:
                availableSessions = 1 - (userConsumedSessions - scheduledSession); // Only 1 session available for SINGLE session type
                break;
            case SessionType.ONCEADAY:
                const sessionsCounts = await this.SchedulingModel.aggregate([
                    {
                        $match: {
                            _id: { $ne: scheduleId },
                            purchaseId: purchase._id,
                            clientId: new Types.ObjectId(userId),
                            date: date,
                            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                        },
                    },
                    { $group: { _id: null, sessions: { $sum: "$sessions" } } },
                ], { session });
                const userSessionsForTheDay = sessionsCounts.length ? sessionsCounts[0]["sessions"] : 0;
                if (userSessionsForTheDay > 0 || requestedSessions > 1) {
                    throw new BadRequestException("Only one booking allowed per day with this package");
                }
                availableSessions = 1;
                break;
            case SessionType.MULTIPLE:
                availableSessions = purchase?.totalSessions - (userConsumedSessions + scheduledSession);
                break;
            case SessionType.DAY_PASS:
                const sessionsCount = await this.SchedulingModel.aggregate([
                    {
                        $match: {
                            _id: { $ne: scheduleId },
                            purchaseId: purchase._id,
                            clientId: new Types.ObjectId(userId),
                            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                        },
                    },
                    {
                        $group: {
                            _id: "$date",
                        },
                    },
                ], { session });
                const bookedDates = sessionsCount.map((item) => item._id.toISOString().split("T")[0]);
                const requestDate = new Date(date).toISOString().split("T")[0];
                const isDateAlreadyUsed = bookedDates.includes(requestDate);
                const consumedDayPassLimit = bookedDates.length;
                const assignedDayPassLimit = purchase?.dayPassLimit;
                const isEligibleForScheduling = assignedDayPassLimit - consumedDayPassLimit;

                if (isEligibleForScheduling > 0) {
                    availableSessions = Number.POSITIVE_INFINITY;
                } else if (isEligibleForScheduling <= 0) {
                    if (isDateAlreadyUsed) {
                        availableSessions = Number.POSITIVE_INFINITY;
                    } else {
                        throw new BadRequestException(
                            `Day pass limit reached ${assignedDayPassLimit} day(s). Please select an existing scheduled date to book a session.`,);
                    }
                }
                break;
            case SessionType.UNLIMITED:
                availableSessions = 0; // Unlimited sessions
                const sessionPerDay = purchase?.sessionPerDay || 0;
                if (sessionPerDay) {
                    const userSessionsForTheDay = await this.SchedulingModel.count({
                        _id: { $ne: scheduleId },
                        purchaseId: purchase._id,
                        clientId: new Types.ObjectId(userId),
                        date: date,
                        scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                    })
                    // const userSessionsForTheDay = sessionsCount.length ? sessionsCount[0]['sessions'] : 0;
                    if (userSessionsForTheDay >= sessionPerDay) {
                        throw new BadRequestException(`Only ${sessionPerDay} booking allowed per day with this package`);
                    }
                    availableSessions = sessionPerDay - userSessionsForTheDay;
                }
                break;
            default:
                throw new BadRequestException("Invalid session type");
        }

        if (!availableSessions) {
            throw new BadRequestException(`Your session for the assigned package are exhausted`);
        }

        // Check if the requested sessions exceed the available sessions
        if (requestedSessions > availableSessions) {
            throw new BadRequestException(`Number of sessions requested is more than those available in this package`);
        }
        return purchase;
    }

    public async validatePackageOwnership(organizationId: Types.ObjectId, packageId: Types.ObjectId) {
        const pricingData: any = await this.PricingModel.findOne({ _id: packageId, organizationId }).select("_id expiredInDays durationUnit createdAt services");
        if (!pricingData) throw new BadRequestException("Access Denied: Package does not belong to this organization.");

        const startDate = new Date(pricingData.createdAt);
        const endDate = new Date(startDate);

        switch (pricingData.durationUnit) {
            case DurationUnit.DAYS:
                endDate.setDate(endDate.getDate() + pricingData.expiredInDays);
                break;
            case DurationUnit.MONTHS:
                endDate.setMonth(endDate.getMonth() + pricingData.expiredInDays);
                break;
            case DurationUnit.YEARS:
                endDate.setFullYear(endDate.getFullYear() + pricingData.expiredInDays);
                break;
        }
        if (new Date() > endDate) throw new BadRequestException("Access Denied: Package has expired.");

        return pricingData;
    }

    public async validateFacilityOwnership(facility: Facility, user: IUserDocument) {
        const { role } = user
        let organizationId: any
        if (role.type == ENUM_ROLE_TYPE.ORGANIZATION) {
            organizationId = user._id
        }
        if (role.type == ENUM_ROLE_TYPE.WEB_MASTER || role.type == ENUM_ROLE_TYPE.FRONT_DESK_ADMIN || role.type == ENUM_ROLE_TYPE.TRAINER) {
            const staffDetails = await this.StaffProfileModel.findOne(
                { userId: user._id },
                { organizationId: 1 }
            );
            organizationId = staffDetails["organizationId"];
        }
        if (role.type == ENUM_ROLE_TYPE.USER) {
            const client = await this.ClientsModel.findOne({ userId: user["_id"] }).exec();
            organizationId = client["organizationId"];
        }

        if (!facility || facility.organizationId.toString() !== organizationId.toString()) {
            throw new BadRequestException("Access Denied as the Facility is not a part of this Organization");
        }
    }

    // Common Method to Validate Room Availability
    public async validateRoomAvailabilityV1(selectedRoom: any, date: Date, from: string, to: string, scheduledId?: any) {
        // if (!selectedRoom) return false;
        if (!selectedRoom) throw new BadRequestException(`Selected room is not available`);

        const currentRoomStrength = await this.SchedulingModel.count({
            _id: { $ne: scheduledId },
            roomId: selectedRoom._id,
            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
            date,
            from: { $lt: to },
            to: { $gt: from },
        });

        if (currentRoomStrength >= selectedRoom.capacity) {
            throw new BadRequestException(`Selected room is at full capacity at the selected time`);
        }
    }

    // Common Method to Validate Room Availability
    public async validateRoomAvailability(selectedRoom: any, startDate: Date, endDate: Date, requestedScheduleDateList: IScheduleDates[], scheduledIds?: any) {
        // if (!selectedRoom) return false;
        if (!selectedRoom) throw new BadRequestException(`Selected room is not available`);

        const currentRoomStrength = await this.SchedulingModel.aggregate([
            {
                $match: {
                    _id: { $nin: scheduledIds ?? [] },
                    roomId: selectedRoom._id,
                    scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                    date: {
                        $gte: startDate,
                        $lte: endDate
                    },
                },
            },
            {
                $group: {
                    _id: {
                        date: "$date",
                        from: "$from",
                        to: "$to",
                    },
                    from: { $first: "$from" },
                    to: { $first: "$to" },
                    count: { $sum: 1 },
                },
            },
            {
                $project: {
                    _id: 0,
                    from: 1,
                    to: 1,
                    date: { $dateToString: { format: "%Y-%m-%d", date: "$_id.date" } },
                    count: 1,
                },
            },
        ]);

        const existingBookingMap = new Map<Date, { date: Date; from: string; to: string; count: number }[]>();
        for (const booking of currentRoomStrength) {
            const date = new Date(booking.date);
            if (!existingBookingMap.has(date)) {
                existingBookingMap.set(date, []);
            }
            existingBookingMap.get(date)!.push(booking);
        }

        for (const requestedSlot of requestedScheduleDateList) {
            const date = requestedSlot.date;
            const from = requestedSlot.from;
            const to = requestedSlot.to;

            const existingBookingsForDate = existingBookingMap.get(date) || [];
            for (const existingBooking of existingBookingsForDate) {
                const existingFrom = existingBooking.from;
                const existingTo = existingBooking.to;
                const count = existingBooking.count;

                if (
                    existingBooking &&
                    ((from >= existingFrom && from < existingTo) || // New start time falls within existing booking
                        (to > existingFrom && to <= existingTo) || // New end time falls within existing booking
                        (from <= existingFrom && to >= existingTo)) &&
                    count >= selectedRoom.capacity
                ) {
                    // New booking completely overlaps existing booking
                    throw new BadRequestException(`Selected room is at full capacity at the selected time at ${date.toDateString().split(" ")[0]} from ${existingFrom} to ${existingTo}`);
                }
            }
        }
    }

    async getAvailableRoomsMultiple(facilityId: string, classType: ClassType, serviceCategory: string, schedules: IScheduleDates[], scheduleIds: string[], session?: ClientSession) {
        // const startDate = schedule[0].date;
        // const endDate = schedule[schedule.length - 1].date;
        const dates = [...new Set(schedules.map(item => item.date))];
        const from = [...new Set(schedules.map(item => item.from))];

        const rooms = await this.RoomModel.find({
            facilityId: new Types.ObjectId(facilityId),
            classType: { $in: [classType] },
            serviceCategory: { $in: [new Types.ObjectId(serviceCategory)] },
            status: true,
        }).lean();

        const roomIds = rooms.map(room => room._id);

        const scheduleCounts = await this.SchedulingModel.aggregate([
            {
                $match: {
                    _id: { $nin: scheduleIds ?? [] },
                    roomId: { $in: roomIds },
                    scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                    date: { $in: dates },
                    from: { $in: from },
                },
            },
            {
                $group: {
                    _id: {
                        roomId: "$roomId",
                        date: "$date",
                        from: "$from",
                    },
                    count: { $sum: 1 },
                },
            },
            {
                $project: {
                    _id: 0,
                    roomId: "$_id.roomId",
                    date: "$_id.date",
                    from: "$_id.from",
                    count: 1,
                },
            },
        ]);

        const countMap = new Map<string, number>();
        for (const { roomId, date, from, count } of scheduleCounts) {
            const key = `${roomId.toHexString()}-${moment(date).utc(true).startOf('day').toDate().getTime()}-${from}`;
            countMap.set(key, count);
        }

        const roomsOnDates = new Map<number, Map<string, IDatabaseObjectId[]>>();

        for (const { date, from } of schedules) {
            const dateKey = moment(date).utc(true).startOf('day').toDate().getTime();
            if (!roomsOnDates.has(dateKey)) {
                roomsOnDates.set(dateKey, new Map());
            }
            const fromMap = roomsOnDates.get(dateKey)!;
            if (!fromMap.has(from)) {
                fromMap.set(from, []);
            }
            const availableList = fromMap.get(from)!;
            for (const { _id, capacity } of rooms) {
                const lookupKey = `${_id.toHexString()}-${dateKey}-${from}`;
                const booked = countMap.get(lookupKey) ?? 0;

                if (booked < capacity) {
                    availableList.push(_id);
                }
            }
        }

        return roomsOnDates;
    }

    // Common Method to Check for Existing Booking Conflict
    /**
     * TODO:
     * - remove checkForExistingBookingConflict later
     */
    private async checkForExistingScheduleConflict(clientId: string, date: Date, from: string, to: string, scheduleId?: string) {
        const existingBooking = await this.SchedulingModel.find({
            _id: { $ne: scheduleId },
            classType: { $nin: [ClassType.BOOKINGS] },
            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] }, // skip canceled
            clientId,
            date,
        });

        for (let booking of existingBooking) {
            // Check for overlapping time slots
            const existingFrom = booking.from;
            const existingTo = booking?.to;

            if (
                existingBooking &&
                ((from >= existingFrom && from < existingTo) || // New start time falls within existing booking
                    (to > existingFrom && to <= existingTo) || // New end time falls within existing booking
                    (from <= existingFrom && to >= existingTo))
            ) {
                // New booking completely overlaps existing booking
                throw new BadRequestException(`Client has an overlapping appointment from ${existingFrom} to ${existingTo}`);
            }
        }
        return true;
    }


    private async sendConfirmationEmail(dataForMail: any, isEditEmail: boolean) {
        if (dataForMail?.clientEmail) {
            dataForMail["date"] = moment(dataForMail.date).format("DD/MM/YYYY");
            await this.mailService.sendMail({
                to: dataForMail["clientEmail"].toString(),
                subject: isEditEmail
                    ? `${dataForMail.classType === "bookings" ? "Booking Updated Successfully" : "Appointment Updated Successfully"}`
                    : `${dataForMail.classType === "bookings" ? "Your Booking is Confirmed" : "Your Appointment is Confirmed"}`,
                template: "booking-creation",
                context: {
                    facilityName: dataForMail.facilityName || "Default Facility Name",
                    isEditEmail,
                    ...dataForMail,
                    dateOfBooking: moment().format("YYYY-MM-DD HH:mm:ss"),
                },
            });
        }

        if (dataForMail?.organizationEmail) {
            await this.mailService.sendMail({
                to: dataForMail["organizationEmail"].toString(),
                subject: isEditEmail
                    ? `${dataForMail.classType === "bookings" ? "Booking Updated Successfully" : "Appointment Updated Successfully"}`
                    : `${dataForMail.classType === "bookings" ? "New Booking Received" : "New Appointment Scheduled"}`,
                template: "organization-booking-creation", // Template for the organization email
                context: {
                    facilityName: dataForMail.facilityName || "Default Facility Name", // Add a fallback if undefined
                    isEditEmail,
                    ...dataForMail, // Spread other properties
                    dateOfBooking: moment().format("YYYY-MM-DD HH:mm:ss"),
                },
            });
        }
        if (dataForMail?.trainerEmail) {
            await this.mailService.sendMail({
                to: dataForMail["trainerEmail"].toString(),
                subject: isEditEmail
                    ? `${dataForMail.classType === "bookings" ? "Booking Edited Successfully" : "Appointment Edited Successfully"}`
                    : `${dataForMail.classType === "bookings" ? "New Booking Received" : "New Appointment Scheduled"}`,
                template: "organization-booking-creation", // Template for the organization email
                context: {
                    facilityName: dataForMail.facilityName || "Default Facility Name", // Add a fallback if undefined
                    isEditEmail,
                    ...dataForMail, // Spread other properties
                    dateOfBooking: moment().format("YYYY-MM-DD HH:mm:ss"),
                },
            });
        }
        const staffsList = await this.getALltheStaffs(dataForMail?.facilityId);
        for (const staff of staffsList) {
            await this.mailService.sendMail({
                to: staff.email.toString(),
                subject: isEditEmail
                    ? `${dataForMail.classType === "bookings" ? "Booking Edited Successfully" : "Appointment Edited Successfully"}`
                    : `${dataForMail.classType === "bookings" ? "New Booking Received" : "New Appointment Scheduled"}`,
                template: "organization-booking-creation",
                context: {
                    facilityName: dataForMail.facilityName || "Default Facility Name", // Add a fallback if undefined
                    isEditEmail,
                    ...dataForMail, // Spread other properties
                    dateOfBooking: moment().format("YYYY-MM-DD HH:mm:ss"),
                },
            })
        }
    }

    private async fetchNecessaryDocuments(body: CreateSchedulingDto | UpdateSchedulingDto, _clientId: string, _subType: string) {
        return await Promise.all([
            this.FacilityModel.findOne({ _id: new Types.ObjectId(body.facilityId) }),
            this.ServicesModel.findById(body.serviceCategory),
            // this.PayRteModel.findOne({_id: body.payRate}),
            // this.PricingModel.findById(body.packageId),
            this.PurchaseModel.findOne({ _id: body.purchaseId, isActive: true }),
            this.RoomModel.findOne({ _id: body.roomId, status: true }),
        ]);
    }

    async scheduleSession(body: CreateSchedulingDto, user: IUserDocument): Promise<any> {
        const { clientId, date, from, to, duration } = body;
        let roomId = body.roomId;
        const [checkFacility, servicePackage, purchase, selectedRoom] = await this.fetchNecessaryDocuments(body, clientId, body.subType);

        await this.validateFacilityOwnership(checkFacility, user);

        if (!purchase) {
            throw new BadRequestException("Access Denied as the user is not eligible for this service");
        }

        const pricing = await this.PricingModel.findById(purchase.packageId);
        if (!pricing) {
            throw new BadRequestException("Access Denied as the user is not eligible for this service");
        }

        if (!servicePackage) {
            throw new NotFoundException({ message: "Selected service not found" });
        }

        // Calculate Session Count
        let serviceSubType = servicePackage.appointmentType.find((item) => item._id.toString() === body.subType) as any;
        if (!serviceSubType) {
            const serviceIdFromPricing = pricing.services.serviceCategory;
            const subTypeIdsFromPricing = [...pricing.services.appointmentType];
            const serviceFromPricing = await this.ServicesModel.findOne({
                _id: serviceIdFromPricing,
                organizationId: body.organizationId,
                classType: body.classType,
                "appointmentType._id": { $in: subTypeIdsFromPricing },
            });
            serviceSubType = serviceFromPricing ? serviceFromPricing.appointmentType.find((item) => item._id.toString() === body.subType) : null;
        }
        if (!serviceSubType) {
            throw new BadRequestException({ message: `This package is not eligible for this ${body.classType} service` });
        }

        const appointmentDuration = serviceSubType.durationInMinutes;
        const requestedSessions = Math.floor(duration / appointmentDuration);

        await this.validatePackageEligibility(clientId, date, from, to, pricing, purchase, requestedSessions);
        if (user.role.type === ENUM_ROLE_TYPE.USER) {
            const rooms = await this.roomListForScheduling(body);
            if (!rooms.length) throw new BadRequestException(`No available rooms at the selected time`);
            roomId = rooms[0]?._id;
        } else await this.validateRoomAvailabilityV1(selectedRoom, date, from, to);
        await this.validateFacilityAvailabilityV1(date, { from, to }, body.facilityId);
        // await this.checkForExistingScheduleConflict(clientId, date, from, to);
        // await this.checkForExistingSessionScheduleConflict(clientId, date, from, to);

        // Create a New Booking
        const newSchedule = await new this.SchedulingModel({
            organizationId: body.organizationId,
            scheduledBy: user._id,
            facilityId: body.facilityId,
            subTypeId: body.subType,
            serviceCategoryId: servicePackage._id,
            packageId: purchase.packageId,
            purchaseId: purchase._id,
            clientId,
            classType: body.classType,
            roomId,
            dateRange: body.dateRange,
            duration,
            sessions: requestedSessions,
            date,
            from,
            to,
            scheduleStatus: body.checkIn ? ScheduleStatusType.CHECKEDIN : ScheduleStatusType.BOOKED,
            notes: body.notes,
        }).save();

        // update consumed sessions
        await this.consumeSessionUpdateInPurchase(purchase._id, requestedSessions);

        const schedule = await this.SchedulingModel.findById(newSchedule._id);

        // Send Confirmation Email if Requested
        const formattedSchedule = await this.formatSchedule(schedule);
        this.waitTimeGatewayService.sendWaitingTimeUpdate(body.facilityId);
        const mailData = await this.formatScheduleMailData(schedule);
        let isEditEmail = false;
        await this.sendConfirmationEmail(mailData, isEditEmail);
        return formattedSchedule;
    }


    async updateSession(body: UpdateSchedulingDto, user: IUserDocument): Promise<any> {
        const scheduledSession = await this.SchedulingModel.findById(body.scheduleId);
        if (!scheduledSession) throw new NotFoundException("Schedule not found");
        if (scheduledSession.scheduleStatus != ScheduleStatusType.BOOKED && scheduledSession.scheduleStatus != ScheduleStatusType.CHECKEDIN)
            throw new BadRequestException(`This session has already been ${scheduledSession.scheduleStatus} and cannot update`);

        const sessionStartDate = new Date(scheduledSession.date).setHours(0, 0, 0, 0);
        const currentDate = new Date().setHours(0, 0, 0, 0);
        const timeString = new Date().toLocaleTimeString("en-GB", { hour: "2-digit", minute: "2-digit" });
        if (currentDate > sessionStartDate || (currentDate == sessionStartDate && timeString > scheduledSession.to)) {
            throw new BadRequestException("You can only update it before or at the scheduled start time");
        }

        const { clientId: scheduledClient, purchaseId: scheduledPurchaseId } = scheduledSession;
        const clientId = body.clientId;
        const { facilityId, date, from, to, duration } = body;
        // const [checkFacility, payRate, pricing, purchase, selectedRoom] = await this.fetchNecessaryDocuments(body, clientId.toString(), body.subType);
        const [checkFacility, servicePackage, purchase, selectedRoom] = await this.fetchNecessaryDocuments(body, clientId, body.subType);

        await this.validateFacilityOwnership(checkFacility, user);

        if (!purchase) {
            throw new BadRequestException("Access Denied as the user is not eligible for this service");
        }

        const pricing = await this.PricingModel.findById(purchase.packageId);
        if (!pricing) {
            throw new BadRequestException("Access Denied as selected package not found");
        }

        if (!servicePackage) {
            throw new NotFoundException({ message: "Selected service not found" });
        }

        // Calculate Session Count
        let serviceSubType = servicePackage.appointmentType.find((item) => item._id.toString() === body.subType) as any;
        if (!serviceSubType) {
            const serviceIdFromPricing = pricing.services.serviceCategory;
            const subTypeIdsFromPricing = [...pricing.services.appointmentType];
            const serviceFromPricing = await this.ServicesModel.findOne({
                _id: serviceIdFromPricing,
                organizationId: scheduledSession.organizationId,
                classType: body.classType,
                "appointmentType._id": { $in: subTypeIdsFromPricing },
            });
            serviceSubType = serviceFromPricing ? serviceFromPricing.appointmentType.find((item) => item._id.toString() === body.subType) : null;
        }
        if (!serviceSubType) {
            throw new BadRequestException({ message: `This package is not eligible for this ${body.classType} service` });
        }

        const appointmentDuration = serviceSubType.durationInMinutes;
        const requestedSessions = Math.floor(duration / appointmentDuration);

        await this.validatePackageEligibility(clientId.toString(), date, from, to, pricing, purchase, requestedSessions, scheduledSession._id, scheduledSession.sessions);
        await this.validateRoomAvailabilityV1(selectedRoom, date, from, to, scheduledSession._id);
        await this.validateFacilityAvailabilityV1(date, { from, to }, facilityId);
        // await this.checkForExistingSessionScheduleConflict(clientId, date, from, to, scheduledSession._id);

        // update consumed sessions
        if (clientId != scheduledClient.toString()) {
            // Deduct in new package
            await this.consumeSessionUpdateInPurchase(purchase._id, requestedSessions);
            // Add session in older package
            await this.consumeSessionUpdateInPurchase(scheduledPurchaseId, -1 * scheduledSession.sessions);
        } else {
            const deductSession = requestedSessions - scheduledSession.sessions;
            await this.consumeSessionUpdateInPurchase(purchase._id, deductSession);
        }

        // Update the Schedule
        await this.SchedulingModel.updateOne(
            { _id: body.scheduleId },
            {
                $set: {
                    scheduledBy: user._id,
                    facilityId: body.facilityId,
                    subTypeId: body.subType,
                    serviceCategoryId: servicePackage._id,
                    packageId: purchase.packageId,
                    purchaseId: purchase._id,
                    clientId,
                    classType: body.classType,
                    roomId: body.roomId,
                    dateRange: body.dateRange,
                    duration,
                    sessions: requestedSessions,
                    date,
                    from,
                    to,
                    notes: body.notes || scheduledSession.notes,
                },
            },
        );

        const updatedSchedule = await this.SchedulingModel.findById(body.scheduleId);

        let formatSchedule = await this.formatSchedule(updatedSchedule);
        this.waitTimeGatewayService.sendWaitingTimeUpdate(body.facilityId);
        const mailData = await this.formatScheduleMailData(updatedSchedule);
        let isEditEmail = true;
        await this.sendConfirmationEmail(mailData, isEditEmail);
        return formatSchedule;
    }

    private async roomListForScheduling(body: any): Promise<any> {
        const { facilityId, classType, serviceCategory, date, from, to } = body;
        let startDate = new Date(date);
        const startOfDay = new Date(startDate.setHours(0, 0, 0, 0));
        const endOfDay = new Date(startDate.setHours(23, 59, 59, 999));
        if (from > to) {
            throw new BadRequestException("StartTime must be greater than end time");
        }
        try {
            const bookedRoomsPipeline = [
                {
                    $match: {
                        facilityId: new Types.ObjectId(facilityId),
                        classType,
                        serviceCategory: new Types.ObjectId(serviceCategory),
                        status: true,
                    },
                },
                {
                    $lookup: {
                        from: "schedulings",
                        localField: "_id",
                        foreignField: "roomId",
                        as: "scheduleDetails",
                    },
                },
                { $unwind: { path: "$scheduleDetails", preserveNullAndEmptyArrays: true } },
                {
                    $match: {
                        "scheduleDetails.facilityId": new Types.ObjectId(facilityId),
                        "scheduleDetails.scheduleStatus": {
                            $in: [ScheduleStatusType.BOOKED, ScheduleStatusType.CHECKEDIN],
                        },
                        "scheduleDetails.date": { $gte: startOfDay, $lte: endOfDay },
                        "scheduleDetails.from": { $lt: to },
                        "scheduleDetails.to": { $gt: from },
                    },
                },
                {
                    $group: {
                        _id: "$_id",
                        roomName: { $first: "$roomName" },
                        capacity: { $first: "$capacity" },
                        bookings: { $push: "$scheduleDetails" },
                    },
                },
                {
                    $addFields: {
                        bookingsCount: { $size: "$bookings" },
                    },
                },
                {
                    $project: {
                        _id: 1,
                        roomId: 1,
                        roomName: 1,
                        capacity: 1,
                        bookingsCount: 1,
                    },
                },
            ];
            const bookedRooms = await this.RoomModel.aggregate(bookedRoomsPipeline);

            const roomIds = [];
            const bookedRoomsMap = new Map();

            for (const room of bookedRooms) {
                if (room.bookingsCount >= room?.capacity) {
                    roomIds.push(new Types.ObjectId(room._id));
                }
                bookedRoomsMap.set(room._id.toString(), room.bookingsCount);
            }

            const availableRoomsPipeline = [
                {
                    $match: {
                        facilityId: new Types.ObjectId(facilityId),
                        _id: { $nin: roomIds },
                        classType: { $in: [classType] },
                        serviceCategory: { $in: [new Types.ObjectId(serviceCategory)] },
                        status: true,
                    },
                },
                {
                    $project: {
                        _id: 1,
                        roomId: "$_id",
                        roomName: 1,
                        capacity: 1,
                    },
                },
            ];

            const availableRooms = await this.RoomModel.aggregate(availableRoomsPipeline);

            const finalResult = availableRooms
                .map((room) => ({
                    ...room,
                    bookingsCount: bookedRoomsMap.get(room._id.toString()) || 0,
                }))
                .sort((a, b) => a._id.toString().localeCompare(b._id.toString()));

            return finalResult;
        } catch (error) {
            console.error("Error in roomList aggregation:", error.message);
            throw new BadRequestException("Failed to fetch rooms");
        }
    }
    public async validateFacilityAvailabilityMultiple(startDate: Date, endDate: Date, schedule: RecurringScheduleDTO, facilityId: string, organizationId: string) {
        const Sdate = new Date(startDate);
        const Edate = new Date(endDate);
        const facilityRecords = await this.FacilityAvailabilityModel.find({
            facilityId: new Types.ObjectId(facilityId),
            organizationId: new Types.ObjectId(organizationId),
            $or: [
                { type: "unavailable", fromDate: { $lte: Edate }, endDate: { $gte: Sdate } },
                { type: "available" }
            ]
        });
        const facilityAvailability = facilityRecords.find(r => r.type === "available");
        if (!facilityAvailability) {
            throw new BadRequestException("No available working hours found for the facility.");
        }
        let unavailableRecord = facilityRecords.find(r => r.type === "unavailable");

        if (unavailableRecord && schedule) {
            let checkDate = new Date(Sdate);
            while (checkDate <= Edate) {
                const dayOfWeek = checkDate.toLocaleDateString("en-US", { weekday: "short" }).toLowerCase();
                unavailableRecord = facilityRecords.find(r => r.fromDate <= checkDate && r.endDate >= checkDate && r.type === "unavailable");
                const requestedSchedule = schedule[dayOfWeek];
                if (unavailableRecord && requestedSchedule?.length > 0) {
                    for (const requestedSlot of requestedSchedule) {
                        for (const unavailableSlot of unavailableRecord.time) {
                            if (this.isTimeOverlapping(requestedSlot, unavailableSlot)) {
                                throw new BadRequestException(`Facility is unavailable from ${unavailableSlot.from} to ${unavailableSlot.to} on ${Sdate.toDateString()}`);
                            }
                        }
                    }
                }
                Sdate.setUTCDate(Sdate.getUTCDate() + 1);
            }
        }

        const facilityWorkingHours = facilityAvailability.workingHours;
        for (const day in schedule) {
            const incomingRequestedSlots = schedule[day];
            const availableSlots = facilityWorkingHours[day];

            if (!availableSlots && incomingRequestedSlots?.length > 0) {
                throw new BadRequestException(`Facility is not available on ${day}.`);
            }

            for (const incomingRequestedSlot of incomingRequestedSlots) {
                const slotIsWithinAvailability = availableSlots.some((availableSlot: any) => this.isTimeWithinAvailableSlot(incomingRequestedSlot, availableSlot));

                if (!slotIsWithinAvailability) {
                    const availableSlotsText = availableSlots.map((slot) => `${slot.from} to ${slot.to}`).join(", ");
                    throw new BadRequestException(
                        `Requested slot from ${incomingRequestedSlot.from} to ${incomingRequestedSlot.to} on ${day} is outside facility's available hours. (Working hours for this day are: ${availableSlotsText}).`,
                    );
                }
            }
        }
        return true;
    }
    private async checkForExistingScheduleConflictMultiple(clientId: string, startDate: Date, endDate: Date, requestedSlots: RecurringScheduleDTO, scheduleId?: Types.ObjectId[], session?: ClientSession) {
        const Sdate = new Date(startDate);
        const Edate = new Date(endDate);
        const existingBooking = await this.SchedulingModel.find({
            _id: { $nin: scheduleId },
            classType: { $nin: [ClassType.BOOKINGS] },
            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] }, // skip canceled
            clientId,
            date: { $gte: Sdate, $lte: Edate },
        }).sort({ date: 1 }).session(session);

        if (existingBooking.length > 0) {
            for (const booking of existingBooking) {
                const dayOfWeek = booking.date.toLocaleDateString("en-US", { weekday: "short" }).toLowerCase();
                if (booking && requestedSlots[dayOfWeek]?.length > 0) {
                    for (const requestedSlot of requestedSlots[dayOfWeek]) {
                        // if (this.isTimeOverlapping(requestedSlot, existingSlot)) {
                        //     throw new BadRequestException(
                        //         `Unable to create slot: On ${singleAvailability.date.toDateString()}, the requested time overlaps with an existing slot from ${existingSlot.from} to ${existingSlot.to}.`
                        //     );
                        // }
                        const existingFrom = booking.from;
                        const existingTo = booking?.to;

                        if (booking &&
                            ((requestedSlot.from >= existingFrom && requestedSlot.from < existingTo) ||
                                (requestedSlot.to > existingFrom && requestedSlot.to <= existingTo) ||
                                (requestedSlot.from <= existingFrom && requestedSlot.to >= existingTo))
                        ) {
                            throw new BadRequestException(`Client has an overlapping appointment from ${existingFrom} to ${existingTo} on ${booking.date.toDateString()}`);
                        }
                    }
                }
            }
        }

        return true;
    }

    private async roomListForSchedulingForMultiple(facilityId: string, classType: ClassType, serviceCategory: string, date: Date, from: string, to: string, scheduleId?: Types.ObjectId[]): Promise<any[]> {
        const startDate = new Date(date);
        const startOfDay = new Date(startDate.setHours(0, 0, 0, 0));
        const endOfDay = new Date(startDate.setHours(23, 59, 59, 999));

        if (from >= to) {
            throw new BadRequestException("Start time must be less than end time");
        }

        try {
            const bookedRoomsPipeline = [
                {
                    $match: {
                        facilityId: new Types.ObjectId(facilityId),
                        classType,
                        serviceCategory: new Types.ObjectId(serviceCategory),
                        status: true,
                    },
                },
                {
                    $lookup: {
                        from: "schedulings",
                        localField: "_id",
                        foreignField: "roomId",
                        as: "scheduleDetails",
                    },
                },
                { $unwind: { path: "$scheduleDetails", preserveNullAndEmptyArrays: true } },
                {
                    $match: {
                        "scheduleDetails._id": { $nin: scheduleId },
                        "scheduleDetails.facilityId": new Types.ObjectId(facilityId),
                        "scheduleDetails.scheduleStatus": {
                            $in: [ScheduleStatusType.BOOKED, ScheduleStatusType.CHECKEDIN],
                        },
                        "scheduleDetails.date": { $gte: startOfDay, $lte: endOfDay },
                        "scheduleDetails.from": { $lt: to },
                        "scheduleDetails.to": { $gt: from },
                    },
                },
                {
                    $group: {
                        _id: "$_id",
                        roomName: { $first: "$roomName" },
                        capacity: { $first: "$capacity" },
                        bookings: { $push: "$scheduleDetails" },
                    },
                },
                {
                    $addFields: {
                        bookingsCount: { $size: "$bookings" },
                    },
                },
                {
                    $project: {
                        _id: 1,
                        roomName: 1,
                        capacity: 1,
                        bookingsCount: 1,
                    },
                },
            ];

            const bookedRooms = await this.RoomModel.aggregate(bookedRoomsPipeline);

            const fullyBookedRoomIds = bookedRooms
                .filter((room) => room.bookingsCount >= room.capacity)
                .map((room) => new Types.ObjectId(room._id));

            const bookedRoomsMap = new Map(
                bookedRooms.map((room) => [room._id.toString(), room.bookingsCount])
            );

            const availableRoomsPipeline = [
                {
                    $match: {
                        facilityId: new Types.ObjectId(facilityId),
                        _id: { $nin: fullyBookedRoomIds },
                        classType: { $in: [classType] },
                        serviceCategory: { $in: [new Types.ObjectId(serviceCategory)] },
                        status: true,
                    },
                },
                {
                    $project: {
                        _id: 1,
                        roomName: 1,
                        capacity: 1,
                    },
                },
            ];

            const availableRooms = await this.RoomModel.aggregate(availableRoomsPipeline);

            const finalRooms = availableRooms
                .map((room) => ({
                    _id: room._id,
                    roomName: room.roomName,
                    capacity: room.capacity,
                    bookingsCount: bookedRoomsMap.get(room._id.toString()) || 0,
                }))
                .sort((a, b) => a._id.toString().localeCompare(b._id.toString()));

            return finalRooms;
        } catch (error) {
            console.error("Error in roomList aggregation:", error.message);
            throw new BadRequestException("Failed to fetch rooms");
        }
    }

    async validateStaffAvailabilityMultiple(serviceCategoryId: string, appointmentType: string, trainerId: string, startDate: Date, endDate: Date, schedule: RecurringScheduleDTO, facilityId: string, scheduleId?: Types.ObjectId[], session?: ClientSession): Promise<boolean> {
        const staff = await this.UserModel.findById(trainerId, { firstName: 1, lastName: 1 });
        if (!staff) throw new BadRequestException("Staff not found");

        const staffName = `${staff.firstName} ${staff.lastName || ""}`.trim();

        const availabilities = await this.StaffAvailabilityModel.find({
            userId: trainerId,
            facilityId,
            date: { $gte: startDate, $lte: endDate },
        });

        if (!availabilities.length) {
            throw new BadRequestException(`${staffName} is not available for the selected dates`);
        }
        const payRates = await this.PayRateModel.find({
            userId: trainerId,
            serviceCategory: serviceCategoryId,
            appointmentType: appointmentType,
        });

        if (!payRates.length) {
            throw new BadRequestException(`${staffName} does not have pay rate configured for the selected service and subtype`);
        }
        const allPayRateIds = payRates.map((payRate) => new Types.ObjectId(payRate._id));


        const availabilityMap = new Map<string, typeof availabilities[0]>();
        for (const entry of availabilities) {
            availabilityMap.set(entry.date.toISOString().split("T")[0], entry);
        }
        let current = new Date(startDate);

        while (current <= endDate) {
            const isoDate = current.toISOString().split("T")[0];
            const dayOfWeek = current.toLocaleDateString("en-US", { weekday: "short" }).toLowerCase();
            const requestedSlots = schedule[dayOfWeek];

            if (requestedSlots?.length) {
                if (availabilityMap.has(isoDate)) {
                    const dayAvailability = availabilityMap.get(isoDate)!;

                    for (const slot of requestedSlots) {

                        for (const unSlot of dayAvailability.timeSlots) {
                            if (unSlot.availabilityStatus === StaffAvailabilityEnum.UNAVAILABLE && this.isTimeOverlapping(slot, unSlot)) {
                                throw new BadRequestException(`${staffName} is unavailable on ${current.toDateString()} between ${slot.from} and ${slot.to}`);
                            }
                        }

                        const validSlots = dayAvailability.timeSlots.filter((s) =>
                            s.availabilityStatus === StaffAvailabilityEnum.AVAILABLE &&
                            s.classType === ClassType.PERSONAL_APPOINTMENT &&
                            this.isTimeWithin(slot, s) &&
                            Array.isArray(s.payRateIds) &&
                            s.payRateIds.some((slotPayRateId) =>
                                allPayRateIds.some((validId) => validId.equals(slotPayRateId))
                            )
                        );

                        if (!validSlots.length) {
                            throw new BadRequestException(`${staffName} is not available on ${current.toDateString()} between ${slot.from} and ${slot.to}`);
                        }

                        const existingSchedules = await this.SchedulingModel.countDocuments({
                            _id: { $nin: scheduleId },
                            trainerId,
                            facilityId,
                            date: current,
                            scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
                            $or: [{ from: { $gte: slot.from, $lt: slot.to } }, { to: { $gt: slot.from, $lte: slot.to } }],
                        });

                        if (existingSchedules > 0) {
                            throw new BadRequestException(`${staffName} already has an appointment on ${current.toDateString()} between ${slot.from} and ${slot.to}`);
                        }
                    }
                }
                else {
                    throw new BadRequestException(`${staffName} is not available on ${current.toDateString()} for the requested slots`);
                }
            }


            current.setUTCDate(current.getUTCDate() + 1);
        }

        return true;
    }

    private isTimeWithin(inner: { from: string; to: string }, outer: { from: string; to: string }) {
        const innerFrom = new Date(`1970-01-01T${inner.from}:00Z`).getTime();
        const innerTo = new Date(`1970-01-01T${inner.to}:00Z`).getTime();
        const outerFrom = new Date(`1970-01-01T${outer.from}:00Z`).getTime();
        const outerTo = new Date(`1970-01-01T${outer.to}:00Z`).getTime();

        return innerFrom >= outerFrom && innerTo <= outerTo;
    }

    // public async validatePackageEligibilityFroMultiple(userId: string, startDate: Date, endDate: Date, schedule: RecurringScheduleDTO, pricing: PricingDocument, purchase: PurchaseDocument,totalRequestedSessions: number): Promise<any> {
    //     const packageStart = new Date(purchase.startDate);
    //     packageStart.setHours(0, 0, 0, 0);
    //     const packageEnd = new Date(purchase.endDate);
    //     packageEnd.setHours(23, 59, 59, 999); 


    //     const bookingFromDateTime = new Date(`${date.toISOString().split("T")[0]}T${from}`);
    //     const bookingToDateTime = new Date(`${date.toISOString().split("T")[0]}T${to}`);

    //     if (bookingFromDateTime < packageStart || bookingToDateTime > packageEnd) {
    //         throw new BadRequestException(
    //             `This package is only valid till ${moment(packageEnd).format('Do MMM')}${moment(packageEnd).year() !== moment().year() ? ' ' + moment(packageEnd).format('YYYY') : ''}.`,
    //         );
    //     }

    //     if (bookingFromDateTime < packageStart || bookingFromDateTime > packageEnd) {
    //         throw new BadRequestException(
    //             `This package is only valid from ${moment(packageEnd).format('Do MMM')} till ${moment(packageEnd).format('Do MMM')}.`,
    //         );
    //     }

    //     this.validateActiveTimeFrame(pricing.activeTimeFrames, bookingFromDateTime, bookingToDateTime)

    //     // Check if the package is suspended
    //     const isSuspended =
    //         purchase.suspensions &&
    //         purchase.suspensions.find((suspension: Suspensions) => {
    //             return suspension.fromDate <= date && suspension.endDate >= date && !suspension.isResumed;
    //         });
    //     if (isSuspended) {
    //         throw new BadRequestException(`This membership is suspended from ${format(isSuspended.fromDate, "dd/MM/yyyy")} to ${format(isSuspended.endDate, "dd/MM/yyyy")}`);
    //     }

    //     // Check the consumed sessions
    //     const userConsumedSessions = purchase.sessionConsumed;

    //     let availableSessions: number = 0;

    //     switch (purchase.sessionType) {
    //         case SessionType.SINGLE:
    //             availableSessions = 1 - (userConsumedSessions - scheduledSession); // Only 1 session available for SINGLE session type
    //             break;
    //         case SessionType.ONCEADAY:
    //             const sessionsCounts = await this.SchedulingModel.aggregate([
    //                 {
    //                     $match: {
    //                         _id: { $ne: scheduleId },
    //                         purchaseId: purchase._id,
    //                         clientId: new Types.ObjectId(userId),
    //                         date: date,
    //                         scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
    //                     },
    //                 },
    //                 { $group: { _id: null, sessions: { $sum: "$sessions" } } },
    //             ]);
    //             const userSessionsForTheDay = sessionsCounts.length ? sessionsCounts[0]["sessions"] : 0;
    //             if (userSessionsForTheDay > 0 || requestedSessions > 1) {
    //                 throw new BadRequestException("Only one booking allowed per day with this package");
    //             }
    //             availableSessions = 1;
    //             break;
    //         case SessionType.MULTIPLE:
    //             availableSessions = purchase?.totalSessions - (userConsumedSessions + scheduledSession);
    //             break;
    //         case SessionType.DAY_PASS:
    //             const sessionsCount = await this.SchedulingModel.aggregate([
    //                 {
    //                     $match: {
    //                         _id: { $ne: scheduleId },
    //                         purchaseId: purchase._id,
    //                         clientId: new Types.ObjectId(userId),
    //                         scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
    //                     },
    //                 },
    //                 {
    //                     $group: {
    //                         _id: "$date",
    //                     },
    //                 },
    //             ]);
    //             const bookedDates = sessionsCount.map((item) => item._id.toISOString().split("T")[0]);
    //             const requestDate = new Date(date).toISOString().split("T")[0];
    //             const isDateAlreadyUsed = bookedDates.includes(requestDate);
    //             const consumedDayPassLimit = bookedDates.length;
    //             const assignedDayPassLimit = purchase?.dayPassLimit;
    //             const isEligibleForScheduling = assignedDayPassLimit - consumedDayPassLimit;

    //             if (isEligibleForScheduling > 0) {
    //                 availableSessions = Number.POSITIVE_INFINITY;
    //             } else if (isEligibleForScheduling <= 0) {
    //                 if (isDateAlreadyUsed) {
    //                     availableSessions = Number.POSITIVE_INFINITY;
    //                 } else {
    //                     throw new BadRequestException(
    //                         `Day pass limit reached ${assignedDayPassLimit} day(s). Please select an existing scheduled date to book a session.`,);
    //                 }
    //             }
    //             break;
    //         case SessionType.UNLIMITED:
    //             availableSessions = 0; // Unlimited sessions
    //             const sessionPerDay = purchase?.sessionPerDay || 0;
    //             if (sessionPerDay) {
    //                 const userSessionsForTheDay = await this.SchedulingModel.count({
    //                     _id: { $ne: scheduleId },
    //                     purchaseId: purchase._id,
    //                     clientId: new Types.ObjectId(userId),
    //                     date: date,
    //                     scheduleStatus: { $nin: [ScheduleStatusType.CANCELED] },
    //                 });
    //                 // const userSessionsForTheDay = sessionsCount.length ? sessionsCount[0]['sessions'] : 0;
    //                 if (userSessionsForTheDay >= sessionPerDay) {
    //                     throw new BadRequestException(`Only ${sessionPerDay} booking allowed per day with this package`);
    //                 }
    //                 availableSessions = sessionPerDay - userSessionsForTheDay;
    //             }
    //             break;
    //         default:
    //             throw new BadRequestException("Invalid session type");
    //     }

    //     if (!availableSessions) {
    //         throw new BadRequestException(`Your session for the assigned package are exhausted`);
    //     }

    //     // Check if the requested sessions exceed the available sessions
    //     if (requestedSessions > availableSessions) {
    //         throw new BadRequestException(`Number of sessions requested is more than those available in this package`);
    //     }
    //     return purchase;
    // }
    async schedulePersonalAppointment(body: CreateSchedulingDto, user: IUserDocument,) {
        if (body.dateRange === DateRange.SINGLE) {
            return this.handleSingleAppointmentScheduling(body, user);
        } else if (body.dateRange === DateRange.MULTIPLE) {
            return this.handleMultipleAppointmentScheduling(body, user);
        }
        throw new BadRequestException("Invalid date range specified.");
    }

    async handleSingleAppointmentScheduling(body: CreateSchedulingDto, user: IUserDocument) {
        const { clientId, date, from, to, duration, sendConfirmation } = body;
        let roomId = body.roomId;
        const [checkFacility, servicePackage, purchase, selectedRoom] = await this.fetchNecessaryDocuments(body, clientId, body.subType);

        if (!purchase) {
            throw new BadRequestException("Access Denied as the user is not eligible for this service");
        }

        const pricing = await this.PricingModel.findById(purchase.packageId);
        if (!pricing) {
            throw new BadRequestException("Access Denied as the user is not eligible for this service");
        }

        if (!servicePackage) {
            throw new NotFoundException({ message: "Selected service not found" });
        }

        // Calculate Session Count
        let serviceSubType = servicePackage.appointmentType.find((item) => item._id.toString() === body.subType) as any;
        if (!serviceSubType) {
            const serviceIdFromPricing = pricing.services.serviceCategory;
            const subTypeIdsFromPricing = [...pricing.services.appointmentType];
            const serviceFromPricing = await this.ServicesModel.findOne({
                _id: serviceIdFromPricing,
                organizationId: body.organizationId,
                classType: body.classType,
                "appointmentType._id": { $in: subTypeIdsFromPricing },
            });
            serviceSubType = serviceFromPricing ? serviceFromPricing.appointmentType.find((item) => item._id.toString() === body.subType) : null;
        }
        if (!serviceSubType) {
            throw new BadRequestException({ message: `This package is not eligible for this ${body.classType} service` });
        }

        const appointmentDuration = serviceSubType.durationInMinutes;
        const requestedSessions = Math.floor(duration / appointmentDuration);

        // await this.validateFacilityOwnership(checkFacility, user);
        await this.validatePackageEligibility(clientId, date, from, to, pricing, purchase, requestedSessions);

        if (user.role.type === ENUM_ROLE_TYPE.USER && roomId) {
            const rooms = await this.roomListForScheduling(body);
            if (!rooms.length) throw new BadRequestException(`No available rooms at the selected time`);
            roomId = rooms[0]?._id;
        } else if (roomId) {
            await this.validateRoomAvailabilityV1(selectedRoom, date, from, to);

        }
        await this.validateFacilityAvailabilityV1(date, { from, to }, body.facilityId);
        await this.checkForExistingScheduleConflict(clientId, date, from, to);

        // validate staff availability by time slot
        const staff = await this.StaffProfileModel.findOne({ userId: body.trainerId, facilityId: body.facilityId });
        if (!staff) {
            throw new BadRequestException("Staff is not part of this facility");
        }
        const istNow = DateTime.now().setZone("Asia/Kolkata");
        const istDateStartOfDay = istNow.startOf("day").toJSDate();
        istDateStartOfDay.setHours(0, 0, 0, 0);
        istDateStartOfDay.setMinutes(istDateStartOfDay.getMinutes() + 330);
        if (body.date >= istDateStartOfDay) {
            await this.validateStaffAvailability(body.serviceCategory, body.subType, body.trainerId, body.date, from, to, body.facilityId);
        }
        const newSchedule = await new this.SchedulingModel({
            organizationId: body.organizationId,
            scheduledBy: user._id,
            facilityId: body.facilityId,
            trainerId: staff.userId,
            subTypeId: body.subType,
            serviceCategoryId: servicePackage._id,
            packageId: purchase.packageId,
            purchaseId: purchase._id,
            clientId,
            classType: body.classType,
            roomId,
            dateRange: body.dateRange,
            duration,
            sessions: requestedSessions,
            date,
            from,
            to,
            scheduleStatus: body.checkIn ? ScheduleStatusType.CHECKEDIN : ScheduleStatusType.BOOKED,
            notes: body.notes,
        }).save();

        // update consumed sessions
        await this.consumeSessionUpdateInPurchase(purchase._id, requestedSessions);

        const schedule = await this.SchedulingModel.findById(newSchedule._id);

        const formattedSchedule = await this.formatSchedule(schedule);
        this.waitTimeGatewayService.sendWaitingTimeUpdate(body.facilityId);
        const mailData = await this.formatScheduleMailData(schedule);
        let isEditEmail = false;
        await this.sendConfirmationEmail(mailData, isEditEmail);

        return formattedSchedule;

    }
    async handleMultipleAppointmentScheduling(body: CreateSchedulingDto, user: IUserDocument) {
        const session = await this.transactionService.startTransaction();
        try {
            let { organizationId, facilityId, trainerId, clientId, purchaseId, classType, subType, payRate, serviceCategory, schedule, startDate, endDate, sendConfirmation, notes, checkIn, markType, dateRange } = body;
            for (const day in schedule) {
                if (Array.isArray(schedule[day])) {
                    schedule[day] = schedule[day].filter(slot =>
                        slot.from && slot.to && slot.durationInMinutes != null
                    );
                } else {
                    schedule[day] = [];
                }
            }
            const [checkFacility, servicePackage, purchase] = await this.fetchNecessaryDocuments(body, clientId, subType);

            if (!checkFacility) {
                throw new NotFoundException({ message: "Selected facility does not exist" });
            }
            if (!servicePackage) {
                throw new NotFoundException({ message: "Selected service is not available" });
            }
            if (!purchase) {
                throw new BadRequestException("No Purchase Found");
            }
            if (!startDate) {
                throw new BadRequestException("Start date is required for multiple Scheduling");
            }
            if (purchase.sessionType === SessionType.DAY_PASS) {
                throw new BadRequestException("Day pass cannot be scheduled for multiple dates");
            }
            let packageStart = new Date(purchase.startDate);
            let packageStartSplit= new Date(packageStart).toISOString().split("T")[0];
            let startDatesplit=new Date(startDate).toISOString().split("T")[0];
            if (startDatesplit < packageStartSplit) {
                throw new BadRequestException(`This package is only valid from ${moment(packageStart).format('Do MMM')} till ${moment(purchase.endDate).format('Do MMM')}.`);
            }

            // let istNow = DateTime.now().setZone("Asia/Kolkata");

            // const istDateStartOfDay = new Date(istNow);

            // istDateStartOfDay.setHours(0, 0, 0, 0);
            // istDateStartOfDay.setMinutes(istDateStartOfDay.getMinutes() + 330);

            // if (!(startDate >= istDateStartOfDay)) {
            //     throw new BadRequestException("Start date should be greater than or equal to current date");
            // }
            if (markType === MarkAvailabilityType.CUSTOM) {
                if (!endDate) {
                    throw new BadRequestException("End date is required for custom date range");
                }
                if (endDate > purchase.endDate) {
                    endDate = new Date(purchase.endDate);
                    endDate.setMinutes(endDate.getMinutes() + 330);
                }
            } else if (markType === MarkAvailabilityType.WEEKLY) {
                endDate = new Date(startDate);
                endDate.setFullYear(endDate.getFullYear() + 1);
                if (endDate > purchase.endDate) {
                    endDate = new Date(purchase.endDate);
                    endDate.setMinutes(endDate.getMinutes() + 330);
                }
            }
            const pricing = await this.PricingModel.findOne({
                _id: new Types.ObjectId(purchase?.packageId),
                "services.type": classType,
                $or: [
                    {
                        $and: [
                            { "services.serviceCategory": new Types.ObjectId(serviceCategory) },
                            { "services.appointmentType": new Types.ObjectId(subType) }
                        ]
                    },
                    {
                        $and: [
                            {
                                "services.relationShip": {
                                    $elemMatch: {
                                        serviceCategory: new Types.ObjectId(serviceCategory),
                                        subTypeIds: new Types.ObjectId(subType)
                                    }
                                }
                            }
                        ]
                    }
                ]
            }).session(session);

            if (!pricing) {
                throw new NotFoundException({ message: "Pricing does not found " });
            }

            const staff = await this.StaffProfileModel.findOne({ userId: trainerId, facilityId: facilityId }).session(session);

            if (!staff) {
                throw new BadRequestException("Staff is not part of this facility");
            }

            let serviceSubType = servicePackage.appointmentType.find((item) => item._id.toString() === subType.toString()) as any;
            if (!serviceSubType) {
                const serviceIdFromPricing = pricing.services.serviceCategory;
                const subTypeIdsFromPricing = [...pricing.services.appointmentType];
                const serviceFromPricing = await this.ServicesModel.findOne({
                    _id: serviceIdFromPricing,
                    organizationId: organizationId,
                    classType: classType,
                    "appointmentType._id": { $in: subTypeIdsFromPricing },
                }).session(session);
                serviceSubType = serviceFromPricing ? serviceFromPricing.appointmentType.find((item) => item._id.toString() === subType) : null;
            }
            if (!serviceSubType) {
                throw new BadRequestException({ message: `This package is not eligible for this ${classType} service` });
            }

            const sessionType = purchase.sessionType;
            const pricingDuration = serviceSubType.durationInMinutes;
            const start = new Date(startDate);
            this.validateSchedule(schedule, pricingDuration);


            let finalSchedule: RecurringScheduleDTO = {
                mon: [],
                tue: [],
                wed: [],
                thu: [],
                fri: [],
                sat: [],
                sun: []
            };

            if (sessionType === SessionType.SINGLE) {
                const sessionLimit = purchase?.totalSessions || 0;
                const alreadyConsumed = purchase?.sessionConsumed || 0;
                const remainingSessionsGoal = sessionLimit - alreadyConsumed;
                if (remainingSessionsGoal <= 0) {
                    throw new BadRequestException("Your sessions for the assigned package are exhausted");
                }
                const day = start.toLocaleDateString("en-US", { weekday: "short" }).toLowerCase() as keyof RecurringScheduleDTO;
                if (schedule[day] && schedule[day].length > 0) {
                    const firstSlot = schedule[day][0];
                    finalSchedule[day] = [{
                        from: firstSlot.from,
                        to: firstSlot.to,
                        durationInMinutes: pricingDuration
                    }];
                }

                schedule = finalSchedule;
                endDate = new Date(start);
                endDate.setHours(23, 59, 59, 999);
            }

            else if (sessionType === SessionType.UNLIMITED) {
                const sessionPerDay = purchase.sessionPerDay || 1;
                const sDate = new Date(startDate);
                const eDate = new Date(endDate);

                const daysUsed = new Set<string>();

                while (sDate <= eDate) {
                    const day = sDate.toLocaleDateString("en-US", { weekday: "short" }).toLowerCase();
                    if (daysUsed.has(day)) {
                        sDate.setDate(sDate.getDate() + 1);
                        continue;
                    }

                    if (schedule[day] && schedule[day].length > 0) {
                        const slots = schedule[day];
                        let totalDuration = 0;
                        let allowedSlots = [];

                        for (const slot of slots) {
                            if (totalDuration + slot.durationInMinutes <= sessionPerDay * pricingDuration) {
                                totalDuration += slot.durationInMinutes;
                                allowedSlots.push({
                                    from: slot.from,
                                    to: slot.to,
                                    durationInMinutes: slot.durationInMinutes
                                });
                            } else {
                                break;
                            }
                        }

                        if (allowedSlots.length) {
                            finalSchedule[day] = allowedSlots;
                            daysUsed.add(day);
                        }
                    }

                    sDate.setDate(sDate.getDate() + 1);
                }

                schedule = finalSchedule;
            }

            else if (sessionType === SessionType.MULTIPLE) {
                const sessionLimit = purchase?.totalSessions || 0;
                const alreadyConsumed = purchase?.sessionConsumed || 0;
                const remainingSessionsGoal = sessionLimit - alreadyConsumed;

                const sDate = new Date(startDate);
                const eDate = new Date(endDate);

                let totalScheduledSessions = 0;
                let lastSessionDate: Date | null = null;
                if (remainingSessionsGoal <= 0) {
                    throw new BadRequestException("Your sessions for the assigned package are exhausted");
                }
                if (sDate > eDate) {
                    throw new BadRequestException("Start date is after end date. No scheduling will occur.");
                } else {
                    while (sDate <= eDate && totalScheduledSessions < remainingSessionsGoal) {
                        const day = sDate.toLocaleDateString("en-US", { weekday: "short" }).toLowerCase();
                        const slotsForDay = schedule[day];

                        let dayContributedToScheduling = false;

                        if (slotsForDay?.length) {
                            for (const slot of slotsForDay) {
                                const sessionsInThisSlot = Math.floor(slot.durationInMinutes / pricingDuration);
                                const currentSessionsNeeded = remainingSessionsGoal - totalScheduledSessions;

                                if (sessionsInThisSlot === 0) {
                                    continue;
                                }

                                if (sessionsInThisSlot > currentSessionsNeeded) {
                                    break;
                                }

                                totalScheduledSessions += sessionsInThisSlot;
                                lastSessionDate = new Date(sDate);
                                dayContributedToScheduling = true;

                                const slotExists = finalSchedule[day]?.some(
                                    (s) => s.from === slot.from && s.to === slot.to
                                );
                                if (!slotExists) {
                                    if (!finalSchedule[day]) finalSchedule[day] = [];
                                    finalSchedule[day].push({
                                        from: slot.from,
                                        to: slot.to,
                                        durationInMinutes: slot.durationInMinutes,
                                    });
                                }

                                if (totalScheduledSessions >= remainingSessionsGoal) {
                                    break;
                                }
                            }

                            if (totalScheduledSessions >= remainingSessionsGoal) {
                                break;
                            }
                        }

                        if (totalScheduledSessions < remainingSessionsGoal) {
                            sDate.setDate(sDate.getDate() + 1);
                        }
                    }
                }

                if (lastSessionDate) {
                    endDate = new Date(lastSessionDate);
                    endDate.setHours(23, 59, 59, 999);
                } else {
                }

                schedule = finalSchedule;
            }

            // let totalDurationInMinutes = 0;
            // let sDate = new Date(startDate);
            // const eDate = new Date(endDate);

            // while (sDate <= eDate) {
            //     const dayOfWeek = sDate.toLocaleDateString("en-US", { weekday: "short" }).toLowerCase();
            //     if (schedule[dayOfWeek]) {
            //         const scheduleForDay = schedule[dayOfWeek];
            //         for (const sched of scheduleForDay) {
            //             const fromTime = new Date(`1970-01-01T${sched.from}:00Z`);
            //             const toTime = new Date(`1970-01-01T${sched.to}:00Z`);
            //             const diffInMinutes = (toTime.getTime() - fromTime.getTime()) / 60000;
            //             if (diffInMinutes !== sched.durationInMinutes) {
            //                 throw new BadRequestException(
            //                     `Mismatch in time range and duration on ${dayOfWeek.toUpperCase()}: From ${sched.from} to ${sched.to} is ${diffInMinutes} mins, but durationInMinutes is ${sched.durationInMinutes} mins.`
            //                 );
            //             }
            //             totalDurationInMinutes += sched.durationInMinutes;
            //         }
            //     }
            //     sDate.setUTCDate(sDate.getUTCDate() + 1);
            // }
            // const totalRequestedSession = totalDurationInMinutes / serviceSubType.durationInMinutes;
            // if (totalRequestedSession > purchase.totalSessions- purchase.sessionConsumed) {
            //     throw new BadRequestException(`Total requested sessions (${totalRequestedSession}) exceed available sessions (${remainingSessions}) in the package.`);
            // }
            const isScheduleEmpty = Object.values(finalSchedule).every(slots => slots.length === 0);
            if (isScheduleEmpty) {
                throw new BadRequestException("Your sessions for the assigned package are exhausted");
            }
            await this.validateFacilityAvailabilityMultiple(startDate, endDate, finalSchedule, facilityId, organizationId);
            await this.checkForExistingScheduleConflictMultiple(clientId, startDate, endDate, finalSchedule);
            const istNow = DateTime.now().setZone("Asia/Kolkata");
            const istDateStartOfDay = istNow.startOf("day").toJSDate();
            istDateStartOfDay.setHours(0, 0, 0, 0);
            istDateStartOfDay.setMinutes(istDateStartOfDay.getMinutes() + 330);

            let availStartDate: Date | null = null;
            let availEndDate: Date | null = null;

            availStartDate = startDate < istDateStartOfDay ? istDateStartOfDay : startDate;

            availEndDate = endDate;
            if (availStartDate && availEndDate && availStartDate <= availEndDate) {
                await this.validateStaffAvailabilityMultiple(
                    serviceCategory,
                    subType,
                    trainerId,
                    availStartDate,
                    availEndDate,
                    finalSchedule,
                    facilityId
                );
            }
            const insertPromises = [];
            let currentDate = new Date(startDate);
            const end = new Date(endDate);
            let actualSessionConsumed = 0
            let sessionConsumed = purchase?.sessionConsumed || 0;

            while (currentDate <= end) {
                const dayOfWeek = currentDate.toLocaleDateString("en-US", { weekday: "short" }).toLowerCase();
                if (finalSchedule[dayOfWeek]) {
                    const scheduleForDay = finalSchedule[dayOfWeek];
                    for (const sched of scheduleForDay) {
                        const appointmentDuration = serviceSubType.durationInMinutes;
                        if (sched.durationInMinutes < appointmentDuration) {
                            throw new BadRequestException(`Duration acnnot be less then ${sched.durationInMinutes} minutes`);
                        }
                        const requestedSessions = Math.floor(sched.durationInMinutes / appointmentDuration);
                        await this.validatePackageEligibilityMultiple(
                            clientId,
                            new Date(currentDate),
                            sched.from,
                            sched.to,
                            pricing,
                            purchase,
                            requestedSessions,
                            sessionConsumed,
                            session
                        );
                        const rooms = await this.roomListForSchedulingForMultiple(facilityId, classType, serviceCategory, new Date(currentDate), sched.from, sched.to,[]);
                        if (!rooms.length)
                            throw new BadRequestException(
                                `No rooms are available on ${dayOfWeek.toUpperCase()}, ${new Date(currentDate).toDateString()} between ${sched.from} and ${sched.to}. Please choose another time.`
                            );
                        let roomId = rooms[0]?._id;

                        const newSchedule = new this.SchedulingModel({
                            organizationId: organizationId,
                            scheduledBy: user._id,
                            facilityId: facilityId,
                            trainerId: staff.userId,
                            subTypeId: subType,
                            serviceCategoryId: servicePackage._id,
                            packageId: purchase.packageId,
                            purchaseId: purchase._id,
                            clientId,
                            classType: classType,
                            roomId,
                            dateRange: dateRange,
                            duration: sched.durationInMinutes,
                            sessions: requestedSessions,
                            date: new Date(currentDate),
                            from: sched.from,
                            to: sched.to,
                            scheduleStatus: checkIn ? ScheduleStatusType.CHECKEDIN : ScheduleStatusType.BOOKED,
                            notes: notes ? notes : "",
                        });
                        insertPromises.push(newSchedule.save({ session }));
                        sessionConsumed = sessionConsumed + requestedSessions;
                        actualSessionConsumed = actualSessionConsumed + requestedSessions;

                    }
                }
                currentDate.setUTCDate(currentDate.getUTCDate() + 1);
            }
            await Promise.all(insertPromises);
            await this.consumeSessionUpdateInPurchaseMultiple(purchase._id, actualSessionConsumed, session);

            this.waitTimeGatewayService.sendWaitingTimeUpdate(body.facilityId);

            await session.commitTransaction();
            session.endSession();

            return {
                message: "Schedule created successfully"
            };
        } catch (error) {
            await session.abortTransaction();
            session.endSession();
            throw new BadRequestException(error.message);
        }
    }

    async updatePersonalAppointment(body: UpdateSchedulingDto, user: IUserDocument,) {
        if (body.dateRange === DateRange.SINGLE) {
            return this.updateSinglePersonalAppointment(body, user);
        }
        else if (body.dateRange === DateRange.MULTIPLE) {
            return this.updateMultiplePersonalAppointment(body, user);
        }
        throw new BadRequestException("Invalid date range specified.");
    }
    async updateSinglePersonalAppointment(body: UpdateSchedulingDto, user: IUserDocument): Promise<any> {
        const scheduledAppointment = await this.SchedulingModel.findById(body.scheduleId);
        if (!scheduledAppointment) throw new NotFoundException("Schedule not found");
        if (scheduledAppointment.scheduleStatus != ScheduleStatusType.BOOKED && scheduledAppointment.scheduleStatus != ScheduleStatusType.CHECKEDIN)
            throw new BadRequestException(`This session has already been ${scheduledAppointment.scheduleStatus} and cannot update`);
        // if (role.type === ENUM_ROLE_TYPE.TRAINER && (!scheduledAppointment.trainerId || scheduledAppointment.trainerId.toString() !== user._id.toString())) {
        //     throw new BadRequestException("Access denied - Trainer can only update their own scheduled appointments");
        // }

        const sessionStartDate = new Date(scheduledAppointment.date).setHours(0, 0, 0, 0);
        const currentDate = new Date().setHours(0, 0, 0, 0);
        const timeString = new Date().toLocaleTimeString("en-GB", { hour: "2-digit", minute: "2-digit" });
        // if (currentDate > sessionStartDate || (currentDate == sessionStartDate && timeString > scheduledAppointment.to)) {
        //     throw new BadRequestException("You can only update it before or at the scheduled start time");
        // }

        const { clientId: scheduledClient, purchaseId: scheduledPurchaseId } = scheduledAppointment;
        const clientId = body.clientId;
        const { facilityId, date, from, to, duration } = body;
        // const [checkFacility, payRate, pricing, purchase, selectedRoom] = await this.fetchNecessaryDocuments(body, clientId.toString(), body.subType);
        const [checkFacility, servicePackage, purchase, selectedRoom] = await this.fetchNecessaryDocuments(body, clientId, body.subType);

        if (!purchase) {
            throw new BadRequestException("Access Denied as the user is not eligible for this service");
        }

        const pricing = await this.PricingModel.findById(purchase.packageId);
        if (!pricing) {
            throw new BadRequestException("Access Denied as the user is not eligible for this service");
        }

        if (!servicePackage) {
            throw new NotFoundException({ message: "Selected service not found" });
        }

        // Calculate Session Count
        let serviceSubType = servicePackage.appointmentType.find((item) => item._id.toString() === body.subType) as any;
        if (!serviceSubType) {
            const serviceIdFromPricing = pricing.services.serviceCategory;
            const subTypeIdsFromPricing = [...pricing.services.appointmentType];
            const serviceFromPricing = await this.ServicesModel.findOne({
                _id: serviceIdFromPricing,
                organizationId: scheduledAppointment.organizationId,
                classType: body.classType,
                "appointmentType._id": { $in: subTypeIdsFromPricing },
            });
            serviceSubType = serviceFromPricing ? serviceFromPricing.appointmentType.find((item) => item._id.toString() === body.subType) : null;
        }
        if (!serviceSubType) {
            throw new BadRequestException({ message: `This package is not eligible for this ${body.classType} service` });
        }

        const appointmentDuration = serviceSubType.durationInMinutes;
        const requestedSessions = Math.floor(duration / appointmentDuration);
        const updatedSessions = requestedSessions - scheduledAppointment.sessions;

        await this.validateFacilityOwnership(checkFacility, user);
        await this.validatePackageEligibility(clientId.toString(), date, from, to, pricing, purchase, updatedSessions, scheduledAppointment._id, scheduledAppointment.sessions);
        if (selectedRoom) {
            await this.validateRoomAvailabilityV1(selectedRoom, date, from, to, scheduledAppointment._id);
        }
        await this.validateFacilityAvailabilityV1(date, { from, to }, facilityId);
        await this.checkForExistingScheduleConflict(clientId, date, from, to, scheduledAppointment._id);

        // validate staff availability by time slot
        const staff = await this.StaffProfileModel.findOne({ userId: body.trainerId, facilityId: body.facilityId });
        if (!staff) {
            throw new BadRequestException("Staff is not part of this facility");
        }
        const istNow = DateTime.now().setZone("Asia/Kolkata");
        const istDateStartOfDay = istNow.startOf("day").toJSDate();
        istDateStartOfDay.setHours(0, 0, 0, 0);
        istDateStartOfDay.setMinutes(istDateStartOfDay.getMinutes() + 330);
        if (body.date >= istDateStartOfDay) {
            await this.validateStaffAvailability(body.serviceCategory, body.subType, body.trainerId, body.date, from, to, body.facilityId, scheduledAppointment._id);
        }

        // update consumed sessions
        if (clientId != scheduledClient.toString()) {
            // Deduct in new package
            await this.consumeSessionUpdateInPurchase(purchase._id, requestedSessions);
            // Add session in older package
            await this.consumeSessionUpdateInPurchase(scheduledPurchaseId, -1 * scheduledAppointment.sessions);
        } else {
            const deductSession = requestedSessions - scheduledAppointment.sessions;
            await this.consumeSessionUpdateInPurchase(purchase._id, deductSession);
        }

        // Update the Schedule
        await this.SchedulingModel.updateOne(
            { _id: body.scheduleId },
            {
                $set: {
                    scheduledBy: user._id,
                    facilityId: body.facilityId,
                    trainerId: staff.userId,
                    subTypeId: body.subType,
                    serviceCategoryId: servicePackage._id,
                    packageId: purchase.packageId,
                    purchaseId: purchase._id,
                    clientId,
                    classType: body.classType,
                    roomId: body.roomId,
                    dateRange: body.dateRange,
                    duration,
                    sessions: requestedSessions,
                    date,
                    from,
                    to,
                    notes: body.notes || scheduledAppointment.notes,
                },
            },
        );

        const updatedSchedule = await this.SchedulingModel.findById(body.scheduleId);

        // Send Confirmation Email if Requested

        let formatedSchedule = await this.formatSchedule(updatedSchedule);
        this.waitTimeGatewayService.sendWaitingTimeUpdate(body.facilityId);
        const mailData = await this.formatScheduleMailData(updatedSchedule);
        let isEditEmail = true;
        await this.sendConfirmationEmail(mailData, isEditEmail);

        return formatedSchedule;
    }
    private async checkRoles(user: IUserDocument) {
        const { role } = user;

        let obj = {}
        switch (role.type) {
            case ENUM_ROLE_TYPE.USER:
                obj['clientId'] = user._id;
                break;
            case ENUM_ROLE_TYPE.ORGANIZATION:
                obj['organizationId'] = user._id;
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                const staffDetails = await this.StaffProfileModel.findOne({ userId: user._id }, { organizationId: 1 });
                if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                obj['organizationId'] = staffDetails.organizationId;
                break;
            case ENUM_ROLE_TYPE.TRAINER:
                const trainerDetails = await this.StaffProfileModel.findOne({ userId: user._id }, { organizationId: 1 });
                if (!trainerDetails) throw new BadRequestException("Access denied, Staff does not have access");
                obj["organizationId"] = trainerDetails.organizationId;
                obj["trainerId"] = user._id;
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        return obj;

    }
    async updateMultiplePersonalAppointment(body: UpdateSchedulingDto, user: IUserDocument): Promise<any> {
        const session = await this.transactionService?.startTransaction();
        try {
            let { scheduleId, clientId, facilityId, trainerId, purchaseId, classType, subType, serviceCategory, payRate, roomId, dateRange, sendConfirmation, notes, markType, startDate, endDate, schedule } = body;
            const [checkFacility, servicePackage, purchase] = await this.fetchNecessaryDocuments?.(body, clientId, subType);

            let query = { _id: new Types.ObjectId(scheduleId) };
            let filter = await this.checkRoles?.(user);
            query = { ...query, ...filter };

            if (purchase?.sessionType === SessionType.DAY_PASS) throw new BadRequestException("Day pass cannot be scheduled for multiple dates");
            if (!checkFacility) throw new NotFoundException({ message: "Selected facility does not exist" });
            if (!servicePackage) throw new NotFoundException({ message: "Selected service is not available" });
            if (!purchase) throw new BadRequestException("No Purchase Found");
            let packageStart = new Date(purchase.startDate);
            let packageStartSplit= new Date(packageStart).toISOString().split("T")[0];
            let startDatesplit=new Date(startDate).toISOString().split("T")[0];

            if (startDatesplit < packageStartSplit) {
                throw new BadRequestException(`This package is only valid from ${moment(packageStart).format('Do MMM')} till ${moment(purchase.endDate).format('Do MMM')}.`);
            }
            let scheduleData: any = await this.SchedulingModel?.findOne(query)?.session(session);
            if (!scheduleData) throw new NotFoundException("Schedule not found");

            const recurringQuery: any = {
                facilityId: new Types.ObjectId(scheduleData?.facilityId),
                serviceCategoryId: new Types.ObjectId(scheduleData?.serviceCategoryId),
                subTypeId: new Types.ObjectId(scheduleData?.subTypeId),
                purchaseId: new Types.ObjectId(scheduleData?.purchaseId),
                packageId: new Types.ObjectId(scheduleData?.packageId),
                trainerId: new Types.ObjectId(scheduleData?.trainerId),
                classType: ClassType.PERSONAL_APPOINTMENT,
                scheduleStatus: ScheduleStatusType.BOOKED,
                from: scheduleData?.from,
                date: { $gte: new Date(startDate), }
            };

            // const istNow = DateTime.now()?.setZone("Asia/Kolkata");
            // const istToday = new Date(istNow);
            // istToday?.setHours(0, 0, 0, 0);
            // istToday?.setMinutes(istToday?.getMinutes() + 330);
            // const currentISTTime = moment()?.tz("Asia/Kolkata")?.format("HH:mm");
            // const scheduleDateStr = scheduleData?.date?.toISOString()?.split("T")[0];
            // const todayStr = istToday?.toISOString()?.split("T")[0];

            // if (!(startDate >= istToday)) throw new BadRequestException("Start date should be greater than or equal to current date");
            // if (currentISTTime >= scheduleData?.to && scheduleDateStr === todayStr) throw new BadRequestException(`You cannot update past schedules for ${moment(scheduleData?.date)?.format("YYYY-MM-DD")}`);

            if (markType === MarkAvailabilityType.CUSTOM) {
                if (!endDate) throw new BadRequestException("End date is required for custom date range");
                if (endDate > purchase?.endDate) {
                    endDate = new Date(purchase?.endDate);
                    //endDate?.setMinutes(endDate?.getMinutes() + 330);
                }
            } else if (markType === MarkAvailabilityType.WEEKLY) {
                endDate = new Date(startDate);
                endDate?.setFullYear(endDate?.getFullYear() + 1);
                if (endDate > purchase?.endDate) {
                    endDate = new Date(purchase?.endDate);
                    endDate?.setMinutes(endDate?.getMinutes() + 330);
                }
            }
            recurringQuery["date"]["$lt"] = new Date(endDate);
            const schedules = await this.SchedulingModel?.find(recurringQuery)?.sort({ date: 1 })?.session(session);

            const sessionsToDecrement = schedules?.reduce((sum, s) => sum + s?.sessions, 0);
            let cancelledIds: Types.ObjectId[] = schedules?.map(s => new Types.ObjectId(s?._id));
            await this.SchedulingModel?.updateMany(
                { _id: { $in: schedules?.map(s => s?._id) } },
                { $set: { scheduleStatus: ScheduleStatusType.CANCELED, canceledBy: user?._id } },
            )?.session(session);

            await this.PurchaseModel?.updateOne(
                { _id: purchaseId },
                { $inc: { sessionConsumed: -1 * sessionsToDecrement } },
            )?.session(session);

            for (const day in schedule) {
                if (Array?.isArray(schedule[day])) {
                    schedule[day] = schedule[day]?.filter(slot =>
                        slot?.from && slot?.to && slot?.durationInMinutes != null
                    );
                } else {
                    schedule[day] = [];
                }
            }

            const pricing = await this.PricingModel?.findOne({
                _id: new Types.ObjectId(purchase?.packageId),
                "services.type": classType,
                $or: [
                    {
                        $and: [
                            { "services.serviceCategory": new Types.ObjectId(serviceCategory) },
                            { "services.appointmentType": new Types.ObjectId(subType) }
                        ]
                    },
                    {
                        $and: [
                            {
                                "services.relationShip": {
                                    $elemMatch: {
                                        serviceCategory: new Types.ObjectId(serviceCategory),
                                        subTypeIds: new Types.ObjectId(subType)
                                    }
                                }
                            }
                        ]
                    }
                ]
            })?.session(session);

            if (!pricing) throw new NotFoundException({ message: "Pricing does not found " });

            const staff = await this.StaffProfileModel?.findOne({ userId: trainerId, facilityId: facilityId })?.session(session);
            if (!staff) throw new BadRequestException("Staff is not part of this facility");

            let serviceSubType = servicePackage?.appointmentType?.find((item) => item?._id?.toString() === subType?.toString()) as any;
            if (!serviceSubType) {
                const serviceIdFromPricing = pricing?.services?.serviceCategory;
                const subTypeIdsFromPricing = [...pricing?.services?.appointmentType];
                const serviceFromPricing = await this.ServicesModel?.findOne({
                    _id: serviceIdFromPricing,
                    organizationId: scheduleData?.organizationId,
                    classType: classType,
                    "appointmentType._id": { $in: subTypeIdsFromPricing },
                })?.session(session);
                serviceSubType = serviceFromPricing ? serviceFromPricing?.appointmentType?.find((item) => item?._id?.toString() === subType) : null;
            }
            if (!serviceSubType) throw new BadRequestException({ message: `This package is not eligible for this ${classType} service` });

            const sessionType = purchase?.sessionType;
            const pricingDuration = serviceSubType?.durationInMinutes;
            const start = new Date(startDate);
            this.validateSchedule?.(body?.schedule, pricingDuration);

            let finalSchedule: RecurringScheduleDTO = {
                mon: [],
                tue: [],
                wed: [],
                thu: [],
                fri: [],
                sat: [],
                sun: []
            };

            if (sessionType === SessionType.SINGLE) {
                const sessionLimit = purchase?.totalSessions || 0;
                const alreadyConsumed = purchase?.sessionConsumed || 0;
                const cancelledSessions = sessionsToDecrement || 0;
                const remainingSessionsGoal = sessionLimit - (alreadyConsumed - cancelledSessions);
                if (remainingSessionsGoal <= 0) {
                    throw new BadRequestException("Your sessions for the assigned package are exhausted");
                }

                while (startDate <= endDate) {
                    const day = startDate?.toLocaleDateString("en-US", { weekday: "short" })?.toLowerCase() as keyof RecurringScheduleDTO;

                    if (schedule[day] && schedule[day]?.length > 0) {
                        const firstSlot = schedule[day][0];
                        finalSchedule[day] = [{
                            from: firstSlot?.from,
                            to: firstSlot?.to,
                            durationInMinutes: pricingDuration
                        }];

                        schedule = finalSchedule;

                        startDate = new Date(startDate);
                        endDate?.setHours(23, 59, 59, 999);
                        break;
                    }

                    startDate?.setDate(startDate?.getDate() + 1);
                }
            }

            else if (sessionType === SessionType.UNLIMITED) {
                const sessionPerDay = purchase?.sessionPerDay || 1;
                const usedDays = new Set<string>();

                let lastScheduledDate: Date | null = null;
                const workingStartDate = new Date(startDate);
                const originalEndDate = new Date(endDate);

                while (workingStartDate <= originalEndDate) {
                    const day = workingStartDate
                        ?.toLocaleDateString("en-US", { weekday: "short" })
                        ?.toLowerCase() as keyof RecurringScheduleDTO;

                    if (usedDays?.has(day)) {
                        workingStartDate?.setDate(workingStartDate?.getDate() + 1);
                        lastScheduledDate = new Date(workingStartDate);
                        continue;
                    }

                    const slots = schedule[day];
                    if (Array?.isArray(slots) && slots?.length > 0) {
                        let totalDuration = 0;
                        const allowedSlots: {
                            from: string;
                            to: string;
                            durationInMinutes: number;
                        }[] = [];

                        for (const slot of slots) {
                            if (
                                totalDuration + slot?.durationInMinutes <= sessionPerDay * pricingDuration
                            ) {
                                totalDuration += slot?.durationInMinutes;
                                allowedSlots?.push({
                                    from: slot?.from,
                                    to: slot?.to,
                                    durationInMinutes: slot?.durationInMinutes,
                                });
                            } else {
                                break;
                            }
                        }

                        if (allowedSlots?.length > 0) {
                            finalSchedule[day] = allowedSlots;
                            usedDays?.add(day);
                            lastScheduledDate = new Date(workingStartDate);
                        }
                    }

                    workingStartDate?.setDate(workingStartDate?.getDate() + 1);
                }

                if (lastScheduledDate) {
                    endDate = new Date(lastScheduledDate);
                    endDate?.setHours(23, 59, 59, 999);
                } else {
                    throw new BadRequestException("No valid slots found in provided schedule");
                }

                schedule = finalSchedule;
            }

            else if (sessionType === SessionType.MULTIPLE) {
                const sessionLimit = purchase?.totalSessions || 0;
                const alreadyConsumed = purchase?.sessionConsumed || 0;
                const cancelledSessions = sessionsToDecrement || 0;
                const remainingSessionsGoal = sessionLimit - (alreadyConsumed - cancelledSessions);

                const sDate = new Date(startDate);
                const eDate = new Date(endDate);

                let totalScheduledSessions = 0;
                let lastSessionDate: Date | null = null;
                if (remainingSessionsGoal <= 0) {
                    throw new BadRequestException("Your sessions for the assigned package are exhausted");
                }
                if (sDate > eDate) {
                    throw new BadRequestException("Start date is after end date. No scheduling will occur.");
                } else {
                    while (sDate <= eDate && totalScheduledSessions < remainingSessionsGoal) {
                        const day = sDate?.toLocaleDateString("en-US", { weekday: "short" })?.toLowerCase();
                        const slotsForDay = schedule[day];

                        let dayContributedToScheduling = false;

                        if (slotsForDay?.length) {
                            for (const slot of slotsForDay) {
                                const sessionsInThisSlot = Math?.floor(slot?.durationInMinutes / pricingDuration);
                                const currentSessionsNeeded = remainingSessionsGoal - totalScheduledSessions;

                                if (sessionsInThisSlot === 0) {
                                    continue;
                                }

                                if (sessionsInThisSlot > currentSessionsNeeded) {
                                    break;
                                }

                                totalScheduledSessions += sessionsInThisSlot;
                                lastSessionDate = new Date(sDate);
                                dayContributedToScheduling = true;

                                const slotExists = finalSchedule[day]?.some(
                                    (s) => s?.from === slot?.from && s?.to === slot?.to
                                );
                                if (!slotExists) {
                                    if (!finalSchedule[day]) finalSchedule[day] = [];
                                    finalSchedule[day]?.push({
                                        from: slot?.from,
                                        to: slot?.to,
                                        durationInMinutes: slot?.durationInMinutes,
                                    });
                                }

                                if (totalScheduledSessions >= remainingSessionsGoal) {
                                    break;
                                }
                            }

                            if (totalScheduledSessions >= remainingSessionsGoal) {
                                break;
                            }
                        }

                        if (totalScheduledSessions < remainingSessionsGoal) {
                            sDate?.setDate(sDate?.getDate() + 1);
                        }
                    }
                }

                if (lastSessionDate) {
                    endDate = new Date(lastSessionDate);
                    endDate?.setHours(23, 59, 59, 999);
                } else {
                }

                schedule = finalSchedule;
            }

            const isScheduleEmpty = Object?.values(finalSchedule)?.every(slots => slots?.length === 0);
            if (isScheduleEmpty) {
                throw new BadRequestException("Your sessions for the assigned package are exhausted");
            }
            await this.validateFacilityAvailabilityMultiple?.(startDate, endDate, finalSchedule, facilityId, scheduleData?.organizationId);
            await this.checkForExistingScheduleConflictMultiple?.(clientId, startDate, endDate, finalSchedule, cancelledIds, session);
            const istNow = DateTime.now().setZone("Asia/Kolkata");
            const istDateStartOfDay = istNow.startOf("day").toJSDate();
            istDateStartOfDay.setHours(0, 0, 0, 0);
            istDateStartOfDay.setMinutes(istDateStartOfDay.getMinutes() + 330);

            let availStartDate: Date | null = null;
            let availEndDate: Date | null = null;

            availStartDate = startDate < istDateStartOfDay ? istDateStartOfDay : startDate;

            availEndDate = endDate;
            if (availStartDate && availEndDate && availStartDate <= availEndDate) {
                await this.validateStaffAvailabilityMultiple?.(serviceCategory, subType, trainerId, availStartDate, availEndDate, finalSchedule, facilityId, cancelledIds, session);

            }

            const insertPromises = [];
            let currentDate = new Date(startDate);
            const end = new Date(endDate);
            let actualSessionConsumed = 0
            let sessionConsumed = (purchase?.sessionConsumed - sessionsToDecrement) || 0;

            while (currentDate <= end) {
                const dayOfWeek = currentDate?.toLocaleDateString("en-US", { weekday: "short" })?.toLowerCase();
                if (finalSchedule[dayOfWeek]) {
                    const scheduleForDay = finalSchedule[dayOfWeek];
                    for (const sched of scheduleForDay) {
                        const appointmentDuration = serviceSubType?.durationInMinutes;
                        if (sched?.durationInMinutes < appointmentDuration) {
                            throw new BadRequestException(`Duration acnnot be less then ${sched?.durationInMinutes} minutes`);
                        }
                        const requestedSessions = Math?.floor(sched?.durationInMinutes / appointmentDuration);
                        await this.validatePackageEligibilityMultiple?.(
                            clientId,
                            new Date(currentDate),
                            sched?.from,
                            sched?.to,
                            pricing,
                            purchase,
                            requestedSessions,
                            sessionConsumed,
                            session
                        );
                        const rooms = await this.roomListForSchedulingForMultiple?.(facilityId, classType, serviceCategory, new Date(currentDate), sched?.from, sched?.to, cancelledIds);
                        if (!rooms?.length)
                            throw new BadRequestException(
                                `No rooms are available on ${dayOfWeek?.toUpperCase()}, ${new Date(currentDate)?.toDateString()} between ${sched?.from} and ${sched?.to}. Please choose another time.`
                            );
                        let roomId = rooms[0]?._id;

                        const newSchedule = new this.SchedulingModel({
                            organizationId: scheduleData?.organizationId,
                            scheduledBy: user?._id,
                            facilityId: facilityId,
                            trainerId: staff?.userId,
                            subTypeId: subType,
                            serviceCategoryId: servicePackage?._id,
                            packageId: purchase?.packageId,
                            purchaseId: purchase?._id,
                            clientId,
                            classType: classType,
                            roomId,
                            dateRange: dateRange,
                            duration: sched?.durationInMinutes,
                            sessions: requestedSessions,
                            date: new Date(currentDate),
                            from: sched?.from,
                            to: sched?.to,
                            scheduleStatus: ScheduleStatusType.BOOKED,
                            notes: notes ? notes : "",
                        });
                        insertPromises?.push(newSchedule?.save({ session }));
                        sessionConsumed = sessionConsumed + requestedSessions;
                        actualSessionConsumed = actualSessionConsumed + requestedSessions;
                    }
                }
                currentDate?.setUTCDate(currentDate?.getUTCDate() + 1);
            }
            await Promise?.all(insertPromises);
            await this.consumeSessionUpdateInPurchaseMultiple?.(purchase?._id, actualSessionConsumed, session);

            this.waitTimeGatewayService?.sendWaitingTimeUpdate?.(body?.facilityId);

            await session?.commitTransaction();
            session?.endSession();

            return {
                message: "Schedule created successfully"
            };
        } catch (error) {
            await session?.abortTransaction();
            session?.endSession();
            throw new BadRequestException(error?.message);
        }
    }

    async getScheduleDetails(user: Record<string, any>, organizationId: IDatabaseObjectId, scheduleId: string, dto: GetScheduleDetailsDto): Promise<Record<string, any>> {
        const { role } = user;
        let { dateRange, startDate, endDate, markType } = dto;
        const query = { _id: new Types.ObjectId(scheduleId) };
        if (!dateRange) {
            dateRange = DateRange.SINGLE;
        }

        switch (role.type) {
            case ENUM_ROLE_TYPE.USER:
                query['clientId'] = user._id;
                break;
            case ENUM_ROLE_TYPE.ORGANIZATION:
                query['organizationId'] = user._id;
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                const staffDetails = await this.StaffProfileModel.findOne({ userId: user._id }, { organizationId: 1 });
                if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                query['organizationId'] = staffDetails.organizationId;
                break;
            case ENUM_ROLE_TYPE.TRAINER:
                const trainerDetails = await this.StaffProfileModel.findOne({ userId: user._id }, { organizationId: 1 });
                if (!trainerDetails) throw new BadRequestException("Access denied, Staff does not have access");
                query["organizationId"] = trainerDetails.organizationId;
                break;
            default:
                throw new BadRequestException("Access denied");
        }

        let schedule: any = await this.SchedulingModel.findOne(query);
        if (!schedule) {
            throw new NotFoundException("Schedule not found");
        }
        if (dateRange === DateRange.MULTIPLE) {
            const recurringQuery: any = {
                facilityId: new Types.ObjectId(schedule.facilityId),
                serviceCategoryId: new Types.ObjectId(schedule.serviceCategoryId),
                subTypeId: new Types.ObjectId(schedule.subTypeId),
                purchaseId: new Types.ObjectId(schedule.purchaseId),
                packageId: new Types.ObjectId(schedule.packageId),
                trainerId: new Types.ObjectId(schedule.trainerId),
                classType: ClassType.PERSONAL_APPOINTMENT,
                scheduleStatus: schedule.scheduleStatus,
                from: schedule.from,
                date: { $gte: new Date(startDate), }
            };


            if (markType === MarkAvailabilityType.CUSTOM) {
                if (!endDate) {
                    throw new BadRequestException("End date is required for CUSTOM mark type.");
                }

                const maxAllowedEndDate = addDays(new Date(startDate), 6);
                if (isAfter(endDate, maxAllowedEndDate)) {
                    endDate = maxAllowedEndDate;
                }
            } else if (markType === MarkAvailabilityType.WEEKLY) {
                endDate = addDays(new Date(startDate), 6);
            }
            recurringQuery["date"]["$lte"] = new Date(endDate)
            const schedules = await this.SchedulingModel.find(recurringQuery).sort({ date: 1 });
            if (!schedules || schedules.length === 0) {
                return [];

            }
            const weekDays: any = {
                mon: {},
                tue: {},
                wed: {},
                thu: {},
                fri: {},
                sat: {},
                sun: {}
            };

            const seenDays = new Set<string>();

            const schedulesMap = new Map<string, any>();
            schedules.forEach(item => { schedulesMap.set(new Date(item.date).toDateString(), item); });

            let current = new Date(startDate);
            const end = new Date(endDate);

            while (current <= end) {
                const dayName = current.toLocaleString("en-US", { weekday: "short" }).toLowerCase();
                if (!seenDays.has(dayName)) {
                    const dateKey = current.toDateString();
                    const item = schedulesMap.get(dateKey);
                    if (item) {
                        weekDays[dayName] = {
                            from: item.from,
                            to: item.to,
                            durationInMinutes: item.duration,
                        };
                    }

                    seenDays.add(dayName);
                }
                current.setDate(current.getDate() + 1);
            }
            const formatted = await this.formatScheduleMultipleUsingAggregation(schedules[0]._id)
            formatted["slots"] = weekDays
            return formatted;
        }
        else if (dateRange === DateRange.SINGLE) {
            return await this.formatSchedule(schedule);
        }

    }

    async formatSchedule(schedule) {
        const [service, client] = await Promise.all([
            this.ServicesModel.findOne({ _id: schedule.serviceCategoryId }, { appointmentType: { $elemMatch: { _id: schedule.subTypeId } } }),
            this.ClientsModel.findOne({ userId: schedule.clientId }, { userId: 1, clientId: 1 }),
        ]);

        const usedSubType: any = service?.appointmentType?.[0] || {};
        const populatedSchedule = await schedule.populate([
            { path: "scheduledBy", select: "_id name firstName lastName email" },
            { path: "facilityId", select: "_id facilityName address profilePicture billingDetails" },
            { path: "trainerId", select: "_id name firstName lastName email" },
            { path: "organizationId", select: "_id name email" },
            { path: "clientId", select: "_id firstName lastName email mobile isActive" },
            { path: "packageId", select: "_id name price services expiredInDays durationUnit" },
            { path: "purchaseId", select: "_id sessionConsumed totalSessions sharePass" },
            // { path: "subTypeId", select: "_id name description classType" },
            { path: "serviceCategoryId", select: "_id name classType appointmentType isActive" },
            { path: "roomId", select: "_id roomName capacity description" },
        ]);

        const { packageId, purchaseId } = populatedSchedule;
        const sessionType = packageId?.services?.sessionType;

        const remainingSessions =
            sessionType === SessionType.SINGLE
                ? 0
                : sessionType === SessionType.MULTIPLE
                    ? (purchaseId?.sharePass ? purchaseId.totalSessions : packageId.services.sessionCount) - purchaseId.sessionConsumed
                    : 9999;

        return {
            _id: populatedSchedule?._id,
            scheduledBy: populatedSchedule?.scheduledBy?._id,
            facilityId: populatedSchedule?.facilityId?._id,
            organizationId: populatedSchedule?.organizationId?._id,
            clientId: populatedSchedule?.clientId?._id,
            clientName: `${populatedSchedule?.clientId?.firstName ?? ""} ${populatedSchedule?.clientId?.lastName ?? ""}`.trim(),
            clientFirstName: populatedSchedule?.clientId?.firstName ?? "",
            clientLastName: populatedSchedule?.clientId?.lastName ?? "",
            clientEmail: populatedSchedule?.clientId?.email ?? "",
            clientPhone: populatedSchedule?.clientId?.mobile ?? "",
            customerId: client?.clientId ?? "",
            trainerId: populatedSchedule?.trainerId?._id,
            trainerName: populatedSchedule?.trainerId ? `${populatedSchedule?.trainerId?.firstName ?? ""} ${populatedSchedule?.trainerId?.lastName ?? ""}`.trim() : "",
            purchaseId: purchaseId?._id ?? "",
            packageId: packageId?._id ?? "",
            classType: populatedSchedule?.classType ?? "",
            subTypeId: populatedSchedule?.subTypeId?._id ?? "",
            subTypeList: populatedSchedule?.serviceCategoryId?.appointmentType ?? [],
            serviceCategoryId: populatedSchedule?.serviceCategoryId?._id ?? "",
            roomId: populatedSchedule?.roomId?._id ?? null,
            roomName: populatedSchedule?.roomId?.roomName ?? "",
            remainingSessions: remainingSessions ?? 0,
            subTypeDuration: usedSubType?.durationInMinutes ?? 0,
            duration: populatedSchedule?.duration ?? 0,
            sessions: populatedSchedule?.sessions ?? 0,
            date: populatedSchedule?.date ?? null,
            from: populatedSchedule?.from ?? null,
            to: populatedSchedule?.to ?? null,
            scheduleStatus: populatedSchedule?.scheduleStatus ?? "",
            notes: populatedSchedule?.notes ?? "",
            facilityName: populatedSchedule?.facilityId?.facilityName ?? "",
            packageName: packageId?.name ?? "",
            totalSession: purchaseId?.sharePass ? purchaseId?.totalSessions ?? 0 : packageId?.services?.sessionCount ?? 0,
            sessionConsumed: purchaseId?.sessionConsumed ?? 0
        };
    }

    async formatScheduleMultipleUsingAggregation(scheduleId: Types.ObjectId) {
        const data = await this.SchedulingModel.aggregate([
            { $match: { _id: scheduleId } },
            {
                $lookup: {
                    from: "services",
                    localField: "serviceCategoryId",
                    foreignField: "_id",
                    as: "serviceData"
                }
            },
            { $unwind: { path: "$serviceData", preserveNullAndEmptyArrays: true } },
            {
                $addFields: {
                    usedSubType: {
                        $arrayElemAt: [
                            {
                                $filter: {
                                    input: "$serviceData.appointmentType",
                                    as: "item",
                                    cond: { $eq: ["$$item._id", "$subTypeId"] }
                                }
                            },
                            0
                        ]
                    }
                }
            },
            {
                $lookup: {
                    from: "clients",
                    localField: "clientId",
                    foreignField: "userId",
                    as: "clientData"
                }
            },
            { $unwind: { path: "$clientData", preserveNullAndEmptyArrays: true } },
            {
                $lookup: {
                    from: "users",
                    localField: "clientData.userId",
                    foreignField: "_id",
                    as: "clientUserData"
                }
            },
            { $unwind: { path: "$clientUserData", preserveNullAndEmptyArrays: true } },
            {
                $lookup: {
                    from: "users",
                    localField: "trainerId",
                    foreignField: "_id",
                    as: "trainerData"
                }
            },
            { $unwind: { path: "$trainerData", preserveNullAndEmptyArrays: true } },
            {
                $lookup: {
                    from: "facilities",
                    localField: "facilityId",
                    foreignField: "_id",
                    as: "facilityData"
                }
            },
            { $unwind: { path: "$facilityData", preserveNullAndEmptyArrays: true } },
            {
                $lookup: {
                    from: "organizations",
                    localField: "organizationId",
                    foreignField: "_id",
                    as: "organizationData"
                }
            },
            { $unwind: { path: "$organizationData", preserveNullAndEmptyArrays: true } },
            {
                $lookup: {
                    from: "packages",
                    localField: "packageId",
                    foreignField: "_id",
                    as: "packageData"
                }
            },
            { $unwind: { path: "$packageData", preserveNullAndEmptyArrays: true } },
            {
                $lookup: {
                    from: "purchases",
                    localField: "purchaseId",
                    foreignField: "_id",
                    as: "purchaseData"
                }
            },
            { $unwind: { path: "$purchaseData", preserveNullAndEmptyArrays: true } },
            {
                $project: {
                    facilityId: 1,
                    organizationId: 1,
                    clientId: 1,
                    clientName: {
                        $concat: [
                            { $ifNull: ["$clientUserData.firstName", ""] },
                            " ",
                            { $ifNull: ["$clientUserData.lastName", ""] }
                        ]
                    },
                    clientFirstName: "$clientUserData.firstName",
                    clientLastName: "$clientUserData.lastName",
                    clientEmail: "$clientUserData.email",
                    clientPhone: "$clientUserData.mobile",
                    customerId: "$clientData.clientId",
                    trainerId: "$trainerId",
                    trainerName: {
                        $concat: [
                            { $ifNull: ["$trainerData.firstName", ""] },
                            " ",
                            { $ifNull: ["$trainerData.lastName", ""] }
                        ]
                    },
                    purchaseId: "$purchaseId",
                    packageId: "$packageId",
                    classType: "$classType",
                    subTypeId: "$subTypeId",
                    subTypeList: "$serviceData.appointmentType",
                    serviceCategoryId: "$serviceCategoryId",
                    remainingSessions: {
                        $cond: [
                            { $eq: ["$packageData.services.sessionType", "SINGLE"] },
                            0,
                            {
                                $cond: [
                                    { $eq: ["$purchaseData.sharePass", true] },
                                    { $subtract: ["$purchaseData.totalSessions", "$purchaseData.sessionConsumed"] },
                                    { $subtract: ["$packageData.services.sessionCount", "$purchaseData.sessionConsumed"] }
                                ]
                            }
                        ]
                    },
                    subTypeDuration: "$usedSubType.durationInMinutes",
                    duration: "$duration",
                    sessions: "$sessions",
                    scheduleStatus: "$scheduleStatus",
                    notes: "$notes",
                    facilityName: "$facilityData.facilityName",
                    packageName: "$packageData.name",
                    totalSession: {
                        $cond: [
                            { $eq: ["$purchaseData.sharePass", true] },
                            "$purchaseData.totalSessions",
                            "$packageData.services.sessionCount"
                        ]
                    },
                    sessionConsumed: "$purchaseData.sessionConsumed"
                }
            }
        ]);

        return data?.[0] || null;
    }

    async formatScheduleMailData(schedule) {
        const [service, client, facility]: any = await Promise.all([
            this.ServicesModel.findOne(
                { _id: schedule?.serviceCategoryId },
                { appointmentType: { $elemMatch: { _id: schedule?.subTypeId } } }
            ),
            this.ClientsModel.findOne({ userId: schedule?.clientId }, { userId: 1, clientId: 1 }),
            this.FacilityModel.findById(schedule?.facilityId, { billingDetails: 1, _id: 1 }).populate([
                { path: "billingDetails.state", select: "_id name" },
                { path: "billingDetails.city", select: "_id name" },
            ]),
        ]);

        const usedSubType: any = service?.appointmentType?.[0] || {};
        const populatedSchedule = await schedule?.populate?.([
            { path: "scheduledBy", select: "_id name firstName lastName email" },
            { path: "facilityId", select: "_id facilityName address profilePicture billingDetails" },
            { path: "trainerId", select: "_id name firstName lastName email" },
            { path: "organizationId", select: "_id name email" },
            { path: "clientId", select: "_id firstName lastName email mobile isActive" },
            { path: "packageId", select: "_id name price services expiredInDays durationUnit" },
            { path: "purchaseId", select: "_id sessionConsumed totalSessions sharePass" },
            { path: "subTypeId", select: "_id name description classType" },
            { path: "serviceCategoryId", select: "_id name classType appointmentType isActive" },
            { path: "roomId", select: "_id roomName capacity description" },
        ]);

        const packageId = populatedSchedule?.packageId;
        const purchaseId = populatedSchedule?.purchaseId;
        const sessionType = packageId?.services?.sessionType;

        const remainingSessions =
            sessionType === SessionType.SINGLE
                ? 0
                : sessionType === SessionType.MULTIPLE
                    ? (purchaseId?.sharePass ? purchaseId?.totalSessions : packageId?.services?.sessionCount) - (purchaseId?.sessionConsumed || 0)
                    : 9999;

        const billingDetails = !facility?.billingDetails
            ? null
            : {
                _id: facility?.billingDetails?._id,
                billingName: facility?.billingDetails?.billingName,
                addressLine1: facility?.billingDetails?.addressLine1,
                addressLine2: facility?.billingDetails?.addressLine2 || "",
                state: facility?.billingDetails?.state?.name,
                city: facility?.billingDetails?.city?.name,
                postalCode: facility?.billingDetails?.postalCode,
                gstNumber: facility?.billingDetails?.gstNumber,
            };

        return {
            _id: populatedSchedule?._id,
            scheduledBy: populatedSchedule?.scheduledBy?._id,
            facilityId: populatedSchedule?.facilityId?._id,
            billingDetails,
            organizationId: populatedSchedule?.organizationId?._id,
            organizationEmail: populatedSchedule?.organizationId?.email,
            clientId: populatedSchedule?.clientId?._id,
            clientName: `${populatedSchedule?.clientId?.firstName || ""} ${populatedSchedule?.clientId?.lastName || ""}`,
            clientFirstName: populatedSchedule?.clientId?.firstName,
            clientLastName: populatedSchedule?.clientId?.lastName,
            clientEmail: populatedSchedule?.clientId?.email,
            clientPhone: populatedSchedule?.clientId?.mobile,
            customerId: client?.clientId,
            trainerId: populatedSchedule?.trainerId?._id || "",
            trainerName: `${populatedSchedule?.trainerId?.firstName || ""} ${populatedSchedule?.trainerId?.lastName || ""}`,
            trainerEmail: populatedSchedule?.trainerId?.email || "",
            purchaseId: purchaseId?._id,
            packageId: packageId?._id,
            classType: populatedSchedule?.classType,
            subTypeId: populatedSchedule?.subTypeId?._id,
            subTypeList: populatedSchedule?.serviceCategoryId?.appointmentType || [],
            serviceCategoryId: populatedSchedule?.serviceCategoryId?._id,
            roomId: populatedSchedule?.roomId?._id || null,
            roomName: populatedSchedule?.roomId?.roomName || "",
            remainingSessions,
            subTypeDuration: usedSubType?.durationInMinutes,
            duration: populatedSchedule?.duration,
            sessions: populatedSchedule?.sessions,
            date: populatedSchedule?.date,
            from: populatedSchedule?.from,
            to: populatedSchedule?.to,
            scheduleStatus: populatedSchedule?.scheduleStatus,
            notes: populatedSchedule?.notes,
            facilityName: populatedSchedule?.facilityId?.facilityName,
            packageName: packageId?.name,
            totalSession: purchaseId?.sharePass ? purchaseId?.totalSessions : packageId?.services?.sessionCount,
            sessionConsumed: purchaseId?.sessionConsumed || 0,
        };
    }

    async getSchedulesList(
        user: IUserDocument,
        organizationId: IDatabaseObjectId,
        body: GetSchedulesDto
    ): Promise<any> {
        const { search, facilityId, classType, serviceCategory, trainerId, roomId, startDate, endDate } = body;
        const filter: any = {
            scheduleStatus: { $in: [ScheduleStatusType.BOOKED, ScheduleStatusType.CHECKEDIN] },
            organizationId
        };
        const { role } = user;
        if (role.type == ENUM_ROLE_TYPE.USER) {
            filter['clientId'] = user._id
        }

        if (body.scheduleStatus) {
            filter["scheduleStatus"] = body.scheduleStatus;
        }
        if (facilityId.length) {
            filter["facilityId"] = { $in: facilityId.map((_id) => new Types.ObjectId(_id)) };
        }
        if (classType) {
            filter["classType"] = classType;
        }
        if (serviceCategory.length) {
            filter["serviceCategoryId"] = { $in: serviceCategory.map((_id) => new Types.ObjectId(_id)) };
        }
        if (roomId.length) {
            filter["roomId"] = { $in: roomId.map((_id) => new Types.ObjectId(_id)) };
        }
        if (trainerId.length) {
            filter["trainerId"] = { $in: trainerId.map((_id) => new Types.ObjectId(_id)) };
        }
        if (startDate) {
            filter["date"] = {
                $gte: startDate,

            };
        }

        const classesEnrollment = await this.EnrollmentModel.find({ userId: user._id }).lean();
        const enrolledScheduleIds = classesEnrollment.map(enrollment => enrollment.schedulingId);
        if (role.type == ENUM_ROLE_TYPE.USER) {

            filter['$or'] = [
                { clientId: user._id },
                { _id: { $in: enrolledScheduleIds } }
            ];

            delete filter['clientId'];
        }
        const agg: PipelineStage[] = [
            {
                $match: filter,
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    as: "client",
                    pipeline: [
                        {
                            $project: {
                                name: 1,
                                firstNam: 1,
                                lastName: 1,
                                email: 1,
                                mobile: 1,
                                isActive: 1,
                            },
                        },
                    ],
                },
            },
            {
                $set: {
                    client: {
                        $arrayElemAt: ["$client", 0],
                    },
                },
            },
            ...(search
                ? [
                    {
                        $addFields: {
                            searched: { $regexMatch: { input: "$client.name", regex: search, options: "i" } },
                        },
                    },
                    {
                        $match: {
                            searched: true,
                        },
                    },
                ]
                : []),
            ...(startDate ? [] : [
                {
                    $addFields: {
                        dateOrder: {
                            $cond: [
                                { $gte: ["$date", new Date()] },
                                0, // Upcoming dates first
                                1  // Past dates second
                            ]
                        }
                    }
                }
            ]),
            {
                $facet: {
                    total: [
                        { $count: "count" },
                    ],
                    scheduleIds: [
                        {
                            $addFields: {
                                dateOrder: {
                                    $cond: [
                                        { $gte: ["$date", new Date()] },
                                        0,
                                        1
                                    ]
                                }
                            }
                        },
                        {
                            $sort: {
                                dateOrder: 1,
                                date: 1,
                                updatedAt: -1
                            }
                        },
                        {
                            $project: {
                                _id: 1
                            }
                        }
                    ]
                }
            },

            {
                $replaceRoot: {
                    newRoot: {
                        total: {
                            $arrayElemAt: ["$total.count", 0],
                        },
                        scheduleIds: "$scheduleIds",
                    },
                },
            },
        ];
        const idsList = await this.SchedulingModel.aggregate(agg);
        const { total = 0, scheduleIds } = idsList.length ? idsList[0] : { total: 0, scheduleIds: [] };
        const populateFields = [
            { path: "scheduledBy", select: "_id name firstName lastName email" },
            { path: "facilityId", select: "_id facilityName address profilePicture" },
            { path: "trainerId", select: "_id name firstName lastName email" },
            // { path: 'organizationId', select: '_id name email' },
            { path: "clientId", select: "_id firstName lastName email mobile isActive" },
            { path: "packageId", select: "_id name price expiredInDays durationUnit" },
            // { path: 'subTypeId', select: '_id name description classType' },
            { path: "serviceCategoryId", select: "_id name classType appointmentType isActive" },
            { path: "roomId", select: "_id roomName capacity description" },
        ];
        const schedules = await this.SchedulingModel.find({ _id: { $in: scheduleIds.map((item) => item._id) } })
            .sort({ date: 1, from: 1 })
            .populate(populateFields);
        let enrichedSchedules = schedules.map((schedule: any) => {
            const appointmentTypes = schedule?.serviceCategoryId?.appointmentType || [];
            const matchedAppointment = appointmentTypes.find((apt: any) => apt._id.toString() === schedule.subTypeId?.toString());

            return {
                ...schedule.toObject(),
                subTypeName: matchedAppointment?.name || null,
                subTypeId: matchedAppointment?._id || null,
            };
        });
        const classSchedules = enrichedSchedules.filter((schedule: any) =>
            schedule.classType === ClassType.CLASSES);
        if (classSchedules.length > 0) {
            const classScheduleIds = classSchedules.map((schedule: any) => schedule._id);
            const enrollments = await this.EnrollmentModel.find({
                schedulingId: { $in: classScheduleIds },
                userId: user._id
            }).populate('packageId', '_id name');
            // Create a map of schedulingId to package info
            const packageInfoMap = new Map();
            enrollments.forEach((enrollment: any) => {
                packageInfoMap.set(
                    enrollment.schedulingId.toString(),
                    {
                        packageId: enrollment.packageId?._id,
                        packageName: enrollment.packageId?.name
                    }
                );
            });
            // Update enriched schedules with package info
            enrichedSchedules = enrichedSchedules.map((schedule: any) => {
                if (schedule.classType === ClassType.CLASSES) {
                    const packageInfo = packageInfoMap.get(schedule._id.toString());
                    if (packageInfo) {
                        return {
                            ...schedule,
                            packageId: packageInfo.packageId,
                            packageName: packageInfo.packageName  // Use 'name' instead of 'packageName'
                        };
                    }
                }
                return schedule;
            });
        }

        const userIds = enrichedSchedules?.filter((item: any) => item.clientId?._id).map((item: any) => item.clientId?._id);
        const clients = await this.ClientsModel.find({ userId: { $in: userIds } }, { userId: 1, clientId: 1 });
        // const customerId = await clients.find(client=>(client.userId == item.clientId._id)),
        const data = enrichedSchedules?.map((item: any) => {
            let customerId: any = clients.find((client) => client.userId.toString() == item.clientId?._id?.toString());
            customerId = customerId ? customerId.clientId : "";
            return {
                _id: item?._id,
                facilityId: item?.facilityId?._id,
                trainerId: item?.trainerId?._id ?? "",
                trainerName: item?.trainerId ? `${item?.trainerId?.firstName ?? ""} ${item?.trainerId?.lastName ?? ""}`.trim() : "",
                facilityName: item?.facilityId?.facilityName ?? "",
                clientId: item?.clientId?._id,
                clientName: item?.clientId ? `${item?.clientId?.firstName ?? ""} ${item?.clientId?.lastName ?? ""}`.trim() : null,
                clientFirstName: item?.clientId?.firstName ?? "",
                clientLastName: item?.clientId?.lastName ?? "",
                clientEmail: item?.clientId?.email ?? "",
                clientPhone: item?.clientId?.mobile ?? "",
                customerId: customerId ?? "",
                packageId: item?.packageId?._id,
                purchaseId: item?.purchaseId,
                packageName: item?.packageId?.name ? item?.packageId?.name : item?.packageName,
                classType: item?.classType ?? "",
                scheduleStatus: item?.scheduleStatus ?? "",
                serviceCategoryId: item?.serviceCategoryId?._id,
                serviceCategoryName: item?.serviceCategoryId?.name ?? "",
                subTypeId: item?.subTypeId?._id,
                subTypeName: item?.subTypeName ?? "",
                duration: item?.duration ?? 0,
                sessions: item?.sessions ?? 0,
                room: item?.roomId?._id ?? null,
                roomName: item?.roomId?.roomName ?? "",
                date: item?.date ?? null,
                from: item?.from ?? null,
                to: item?.to ?? null
            };
        });

        return {
            count: total,
            data,
        };
    }

    async getSchedulesListV1(user: Record<string, any>, body: GetSchedulesDto): Promise<any> {
        const { search, facilityId, classType, serviceCategory, trainerId, roomId, startDate, endDate, page, pageSize } = body;
        const skip = pageSize * (page - 1);
        const filter: any = { scheduleStatus: { $in: [ScheduleStatusType.BOOKED, ScheduleStatusType.CHECKEDIN] } };

        const { role } = user;
        switch (role.type) {
            case ENUM_ROLE_TYPE.USER:
                filter["clientId"] = user._id;
                break;
            case ENUM_ROLE_TYPE.ORGANIZATION:
                filter["organizationId"] = user._id;
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                const staffDetails = await this.StaffProfileModel.findOne({ userId: user._id }, { organizationId: 1 });
                if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                filter["organizationId"] = staffDetails.organizationId;
                break;
            case ENUM_ROLE_TYPE.TRAINER:
                const trainerDetails = await this.StaffProfileModel.findOne({ userId: user._id }, { organizationId: 1 });
                if (!trainerDetails) throw new BadRequestException("Access denied, Staff does not have access");
                filter["organizationId"] = trainerDetails.organizationId;
                // filter["trainerId"] = user._id;
                break;
            default:
                throw new BadRequestException("Access denied");
        }

        if (body.scheduleStatus) {
            filter["scheduleStatus"] = body.scheduleStatus;
        }
        if (facilityId.length) {
            filter["facilityId"] = { $in: facilityId.map((_id) => new Types.ObjectId(_id)) };
        }
        if (classType) {
            filter["classType"] = classType;
        }
        if (serviceCategory.length) {
            filter["serviceCategoryId"] = { $in: serviceCategory.map((_id) => new Types.ObjectId(_id)) };
        }
        if (roomId.length) {
            filter["roomId"] = { $in: roomId.map((_id) => new Types.ObjectId(_id)) };
        }
        if (trainerId.length) {
            filter["trainerId"] = { $in: trainerId.map((_id) => new Types.ObjectId(_id)) };
        }
        if (startDate && endDate) {
            filter["date"] = {
                $gte: startDate,
                $lte: endDate,
            };
        }
        const agg: PipelineStage[] = [
            {
                $match: filter,
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    as: "client",
                    pipeline: [
                        {
                            $project: {
                                name: 1,
                                firstNam: 1,
                                lastName: 1,
                                email: 1,
                                mobile: 1,
                                isActive: 1,
                            },
                        },
                    ],
                },
            },
            {
                $set: {
                    client: {
                        $arrayElemAt: ["$client", 0],
                    },
                },
            },
            ...(search
                ? [
                    {
                        $addFields: {
                            searched: { $regexMatch: { input: "$client.name", regex: search, options: "i" } },
                        },
                    },
                    {
                        $match: {
                            searched: true,
                        },
                    },
                ]
                : []),
            {
                $facet: {
                    total: [
                        {
                            $count: "count",
                        },
                    ],
                    scheduleIds: [
                        {
                            $sort: {
                                updatedAt: -1,
                            },
                        },
                        {
                            $skip: skip,
                        },
                        {
                            $limit: pageSize,
                        },
                        {
                            $project: {
                                _id: 1,
                            },
                        },
                    ],
                },
            },
            {
                $replaceRoot: {
                    newRoot: {
                        total: {
                            $arrayElemAt: ["$total.count", 0],
                        },
                        scheduleIds: "$scheduleIds",
                    },
                },
            },
        ];
        const idsList = await this.SchedulingModel.aggregate(agg);
        const { total = 0, scheduleIds } = idsList.length ? idsList[0] : { total: 0, scheduleIds: [] };
        const populateFields = [
            { path: "scheduledBy", select: "_id name firstName lastName email" },
            { path: "facilityId", select: "_id facilityName address profilePicture" },
            { path: "trainerId", select: "_id name firstName lastName email" },
            // { path: 'organizationId', select: '_id name email' },
            { path: "clientId", select: "_id firstName lastName email mobile isActive" },
            { path: "packageId", select: "_id name price expiredInDays durationUnit" },
            // { path: 'subTypeId', select: '_id name description classType' },
            { path: "serviceCategoryId", select: "_id name classType appointmentType isActive" },
            { path: "roomId", select: "_id roomName capacity description" },
        ];
        const schedules = await this.SchedulingModel.find({ _id: { $in: scheduleIds.map((item) => item._id) } })
            .sort({ updatedAt: -1 })
            .populate(populateFields);
        const enrichedSchedules = schedules.map((schedule: any) => {
            const appointmentTypes = schedule?.serviceCategoryId?.appointmentType || [];
            const matchedAppointment = appointmentTypes.find((apt: any) => apt._id.toString() === schedule.subTypeId?.toString());

            return {
                ...schedule.toObject(),
                subTypeName: matchedAppointment?.name || null,
                subTypeId: matchedAppointment?._id || null,
            };
        });
        const userIds = enrichedSchedules?.filter((item: any) => item.clientId?._id).map((item: any) => item.clientId?._id);
        const clients = await this.ClientsModel.find({ userId: { $in: userIds } }, { userId: 1, clientId: 1 });
        // const customerId = await clients.find(client=>(client.userId == item.clientId._id)),
        const data = enrichedSchedules?.map((item: any) => {
            let customerId: any = clients.find((client) => client.userId.toString() == item.clientId?._id?.toString());
            let userClientId = customerId ? customerId?._id : null;
            customerId = customerId ? customerId.clientId : "";
            return {
                _id: item._id,
                facilityId: item.facilityId._id,
                trainerId: item.trainerId ? item.trainerId._id : "",
                trainerName: item.trainerId ? `${item.trainerId.firstName} ${item.trainerId.lastName}` : "",
                facilityName: item.facilityId.facilityName,
                userClientId: userClientId,
                clientId: item.clientId?._id,
                clientName: item.clientId?.firstName ? `${item.clientId?.firstName} ${item.clientId?.lastName}` : null,
                clientFirstName: item.clientId?.firstName,
                clientLastName: item.clientId?.lastName,
                clientEmail: item.clientId?.email,
                clientPhone: item.clientId?.mobile,
                customerId: customerId,
                packageId: item.packageId._id,
                purchaseId: item?.purchaseId,
                packageName: item.packageId.name,
                classType: item.classType,
                scheduleStatus: item.scheduleStatus,
                serviceCategoryId: item.serviceCategoryId._id,
                serviceCategoryName: item.serviceCategoryId.name,
                subTypeId: item.subTypeId?._id,
                subTypeName: item.subTypeName,
                duration: item.duration,
                sessions: item.sessions,
                room: item.roomId ? item.roomId._id : null,
                roomName: item.roomId ? item.roomId.roomName : "",
                date: item.date,
                from: item.from,
                to: item.to,
            };
        });

        return {
            count: total,
            data,
        };
    }

    async cancelSchedule(user: IUserDocument, scheduleId: string, dto: CancelRecurringScheduleDto): Promise<any> {
        let { startDate, endDate, dateRange } = dto;
        if (!dateRange) {
            dateRange = DateRange.SINGLE;
        }
        const session = await this.transactionService.startTransaction();
        const query = { _id: scheduleId };
        const { role } = user;

        switch (role.type) {
            case ENUM_ROLE_TYPE.USER:
                query['clientId'] = user._id;
                break;
            case ENUM_ROLE_TYPE.ORGANIZATION:
                query['organizationId'] = user._id;
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                const staffDetails = await this.StaffProfileModel.findOne({ userId: user._id }, { organizationId: 1 });
                if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                query['organizationId'] = staffDetails.organizationId;
                break;
            case ENUM_ROLE_TYPE.TRAINER:
                const trainerDetails = await this.StaffProfileModel.findOne({ userId: user._id }, { organizationId: 1 });
                if (!trainerDetails) throw new BadRequestException("Access denied, Staff does not have access");
                query["organizationId"] = trainerDetails.organizationId;
                // query["trainerId"] = user._id;
                break;
            default:
                throw new BadRequestException("Access denied");
        }

        const schedule = await this.SchedulingModel.findOne(query).session(session);

        if (!schedule) throw new NotFoundException(`Schedule with ID ${scheduleId} not found`);

        if (role.type === ENUM_ROLE_TYPE.USER && schedule.scheduledBy.toString() !== user._id.toString()) {
            throw new BadRequestException("You are not authorized to cancel this schedule");
        }

        if (schedule.scheduleStatus === ScheduleStatusType.CANCELED) {
            throw new BadRequestException(`The session scheduled on ${moment(schedule.date).format("YYYY-MM-DD")} from ${schedule.from} to ${schedule.to} has already been cancelled.`);
        }

        if (schedule.scheduleStatus === ScheduleStatusType.CHECKEDIN) {
            throw new BadRequestException(`The session scheduled on ${moment(schedule.date).format("YYYY-MM-DD")} from ${schedule.from} to ${schedule.to} has already been cancelled.`);
        }

        // const now = moment();
        // const sessionStart = moment(`${moment(schedule.date).format("YYYY-MM-DD")} ${schedule.from}`, "YYYY-MM-DD HH:mm");
        // if (now.isAfter(sessionStart)) {
        //     throw new BadRequestException("You can only cancel the session before it starts");
        // }

        const istNow = DateTime.now().setZone("Asia/Kolkata");
        const istToday = istNow.startOf("day").toJSDate();

        istToday.setHours(0, 0, 0, 0);
        istToday.setMinutes(istToday.getMinutes() + 330);

        const currentISTTime = moment().tz("Asia/Kolkata").format("HH:mm");
        const scheduleDateStr = schedule.date.toISOString().split("T")[0];
        const todayStr = istToday.toISOString().split("T")[0];

        if (currentISTTime >= schedule.to && scheduleDateStr === todayStr) {
            throw new BadRequestException(`You cannot cancel past schedules for ${moment(schedule.date).format("YYYY-MM-DD")}`);
        }

        const isMultiple = dateRange === DateRange.MULTIPLE;

        if (isMultiple) {
            const baseScheduleDate = new Date(schedule.date);
            if (!startDate || !endDate) throw new BadRequestException("Start and End Date are required for multiple cancellation");
            if (startDate > endDate) throw new BadRequestException("Start date must be before or equal to End date");

            if (!(startDate >= istToday)) {
                throw new BadRequestException("Start date should greater than or equal to current Date");
            }

            // if (baseScheduleDate.toISOString().split("T")[0] !== new Date(startDate).toISOString().split("T")[0]) {
            //     throw new BadRequestException("Start date should be same as the base schedule date");
            // }

            if (schedule.classType !== ClassType.PERSONAL_APPOINTMENT) {
                throw new BadRequestException("Only personal appointment can be cancelled");
            }

            const {
                clientId, facilityId, serviceCategoryId, subTypeId,
                packageId, purchaseId, trainerId,
                organizationId, from
            } = schedule;

            const matchQuery: any = {
                clientId,
                facilityId,
                // serviceCategoryId,
                // subTypeId,
                packageId,
                purchaseId,
                //trainerId,
                classType: ClassType.PERSONAL_APPOINTMENT,
                organizationId,
                scheduleStatus: ScheduleStatusType.BOOKED,
                //from,
                date: { $gte: new Date(startDate), $lte: new Date(endDate) }
            };

            const schedules = await this.SchedulingModel.find(matchQuery).session(session);
            if (!schedules.length) throw new NotFoundException("No matching recurring schedules found in the date range");

            const canceledCount = schedules.length;
            const sessionsToDecrement = schedules.reduce((sum, s) => sum + s.sessions, 0);

            await this.SchedulingModel.updateMany(
                { _id: { $in: schedules.map(s => s._id) } },
                { $set: { scheduleStatus: ScheduleStatusType.CANCELED, canceledBy: user._id } },
                { session }
            );

            await this.PurchaseModel.updateOne(
                { _id: purchaseId },
                { $inc: { sessionConsumed: -1 * sessionsToDecrement } },
                { session }
            );

            await session.commitTransaction();
            session.endSession();

            this.waitTimeGatewayService.sendWaitingTimeUpdate(facilityId);

            return {
                message: `${canceledCount} schedule(s) canceled successfully.`,
                canceledCount
            };
        }

        else if (dateRange === DateRange.SINGLE) {
            schedule.scheduleStatus = ScheduleStatusType.CANCELED;
            schedule.canceledBy = user._id;
            await schedule.save({ session });

            await this.PurchaseModel.updateOne(
                { _id: schedule.purchaseId },
                { $inc: { sessionConsumed: -1 * schedule.sessions } },
                { session }
            );

            await session.commitTransaction();
            session.endSession();

            this.waitTimeGatewayService.sendWaitingTimeUpdate(schedule.facilityId);

            return {
                scheduleStatus: schedule.scheduleStatus,
                message: "Schedule cancelled successfully"
            };
        }
    }

    async checkInSchedule(user: IUserDocument, scheduleId: string): Promise<any> {
        const query = { _id: scheduleId }
        const { role } = user
        switch (role.type) {
            case ENUM_ROLE_TYPE.USER:
                query['clientId'] = user._id
                break
            case ENUM_ROLE_TYPE.ORGANIZATION:
                query['organizationId'] = user._id
                break
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                const staffDetails = await this.StaffProfileModel.findOne(
                    { userId: user._id },
                    { organizationId: 1 }
                );
                if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                query['organizationId'] = staffDetails.organizationId
                break
            case ENUM_ROLE_TYPE.TRAINER:
                const trainerDetails = await this.StaffProfileModel.findOne(
                    { userId: user._id },

                    { organizationId: 1 },
                );

                if (!trainerDetails) throw new BadRequestException("Access denied, Staff does not have access");

                query["organizationId"] = trainerDetails.organizationId;

                // query["trainerId"] = user._id;

                break;

            default:
                throw new BadRequestException("Access denied");
        }

        const schedule = await this.SchedulingModel.findOne(query).populate("clientId facilityId packageId roomId");
        if (!schedule) {
            throw new NotFoundException(`Schedule with ID ${scheduleId} not found`);
        }

        if (schedule.scheduleStatus === ScheduleStatusType.CHECKEDIN) {
            throw new BadRequestException("This session has already been checked in");
        }
        if (schedule.scheduleStatus === ScheduleStatusType.CANCELED) {
            throw new BadRequestException("This session has been canceled and cannot be checked in");
        }

        const sessionStartDate = new Date(schedule.date).setHours(0, 0, 0, 0);
        const currentDate = new Date().setHours(0, 0, 0, 0);
        const timeString = new Date().toLocaleTimeString("en-GB", { hour: "2-digit", minute: "2-digit" });
        if (currentDate > sessionStartDate || (currentDate == sessionStartDate && timeString > schedule.to)) {
            throw new BadRequestException("You can only check-in before or at the scheduled end time");
        }

        schedule.scheduleStatus = ScheduleStatusType.CHECKEDIN;
        await schedule.save();

        const updatedSchedule = await this.SchedulingModel.findById(schedule._id).populate([
            { path: "scheduledBy", select: "_id name firstName lastName email" },
            { path: "facilityId", select: "_id facilityName address profilePicture" },
            { path: "trainerId", select: "_id name firstName lastName email" },
            { path: "organizationId", select: "_id name email" },
            { path: "clientId", select: "_id firstName lastName email" },
            { path: "packageId", select: "_id name price expiredInDays durationUnit" },
            { path: "subTypeId", select: "_id name description classType" },
            { path: "serviceCategoryId", select: "_id name attributeType isActive" },
            { path: "roomId", select: "_id roomName capacity description" },
        ]);
        this.waitTimeGatewayService.sendWaitingTimeUpdate(schedule.facilityId);

        return {
            scheduleId: updatedSchedule._id,
            clientId: updatedSchedule.clientId,
            status: updatedSchedule.scheduleStatus,
            checkInTime: new Date(),
        };
    }

    async getSchedulesListByUser(user: IUserDocument, body: GetSchedulesByUserDto): Promise<any> {
        const { page, pageSize, clientId } = body;
        const skip = pageSize * (page - 1);
        const filter: any = { scheduleStatus: { $in: [ScheduleStatusType.BOOKED, ScheduleStatusType.CHECKEDIN] } };

        const { role } = user;
        switch (role.type) {
            case ENUM_ROLE_TYPE.USER:
                filter["clientId"] = user._id;
                break;
            case ENUM_ROLE_TYPE.ORGANIZATION:
                filter["organizationId"] = user._id;
                filter["clientId"] = new Types.ObjectId(clientId);
                ;
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                const staffDetails = await this.StaffProfileModel.findOne({ userId: user._id }, { organizationId: 1 });
                if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                filter["organizationId"] = staffDetails.organizationId;
                filter["clientId"] = new Types.ObjectId(clientId);
                ;
                break;
            case ENUM_ROLE_TYPE.TRAINER:
                const trainerDetails = await this.StaffProfileModel.findOne({ userId: user._id }, { organizationId: 1 });
                if (!trainerDetails) throw new BadRequestException("Access denied, Staff does not have access");
                filter["organizationId"] = trainerDetails.organizationId;
                filter["trainerId"] = user._id;
                filter["clientId"] = new Types.ObjectId(clientId);
                ;
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        const agg: PipelineStage[] = [
            {
                $match: filter,
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    as: "client",
                    pipeline: [
                        {
                            $project: {
                                name: 1,
                                firstNam: 1,
                                lastName: 1,
                                email: 1,
                                mobile: 1,
                                isActive: 1,
                            },
                        },
                    ],
                },
            },
            {
                $set: {
                    client: {
                        $arrayElemAt: ["$client", 0],
                    },
                },
            },
            {
                $facet: {
                    total: [
                        {
                            $count: "count",
                        },
                    ],
                    scheduleIds: [
                        {
                            $sort: {
                                updatedAt: -1,
                            },
                        },
                        {
                            $skip: skip,
                        },
                        {
                            $limit: pageSize,
                        },
                        {
                            $project: {
                                _id: 1,
                            },
                        },
                    ],
                },
            },
            {
                $replaceRoot: {
                    newRoot: {
                        total: {
                            $arrayElemAt: ["$total.count", 0],
                        },
                        scheduleIds: "$scheduleIds",
                    },
                },
            },
        ];
        const idsList = await this.SchedulingModel.aggregate(agg);
        const { total = 0, scheduleIds } = idsList.length ? idsList[0] : { total: 0, scheduleIds: [] };
        const populateFields = [
            { path: "scheduledBy", select: "_id name firstName lastName email" },
            { path: "facilityId", select: "_id facilityName address profilePicture" },
            { path: "trainerId", select: "_id name firstName lastName email" },
            // { path: 'organizationId', select: '_id name email' },
            { path: "clientId", select: "_id firstName lastName email mobile isActive" },
            { path: "packageId", select: "_id name price expiredInDays durationUnit" },
            // { path: 'subTypeId', select: '_id name description classType' },
            { path: "serviceCategoryId", select: "_id name classType appointmentType isActive" },
            { path: "roomId", select: "_id roomName capacity description" },
        ];
        const schedules = await this.SchedulingModel.find({ _id: { $in: scheduleIds.map((item) => item._id) } })
            .sort({ updatedAt: -1 })
            .populate(populateFields);
        const enrichedSchedules = schedules.map((schedule: any) => {
            const appointmentTypes = schedule?.serviceCategoryId?.appointmentType || [];
            const matchedAppointment = appointmentTypes.find((apt: any) => apt._id.toString() === schedule.subTypeId?.toString());

            return {
                ...schedule.toObject(),
                subTypeName: matchedAppointment?.name || null,
                subTypeId: matchedAppointment?._id || null,
            };
        });
        const userIds = enrichedSchedules?.filter((item: any) => item.clientId?._id).map((item: any) => item.clientId?._id);
        const clients = await this.ClientsModel.find({ userId: { $in: userIds } }, { userId: 1, clientId: 1 });
        // const customerId = await clients.find(client=>(client.userId == item.clientId._id)),
        const data = enrichedSchedules?.map((item: any) => {
            let customerId: any = clients.find((client) => client.userId.toString() == item.clientId?._id?.toString());
            customerId = customerId ? customerId.clientId : "";
            return {
                _id: item._id,
                facilityId: item.facilityId._id,
                trainerId: item.trainerId ? item.trainerId._id : "",
                trainerName: item.trainerId ? `${item.trainerId.firstName} ${item.trainerId.lastName}` : "",
                facilityName: item.facilityId.facilityName,
                clientId: item.clientId?._id,
                clientName: item.clientId?.firstName ? `${item.clientId?.firstName} ${item.clientId?.lastName}` : null,
                clientFirstName: item.clientId?.firstName,
                clientLastName: item.clientId?.lastName,
                clientEmail: item.clientId?.email,
                clientPhone: item.clientId?.mobile,
                customerId: customerId,
                packageId: item.packageId._id,
                packageName: item.packageId.name,
                classType: item.classType,
                scheduleStatus: item.scheduleStatus,
                serviceCategoryId: item.serviceCategoryId._id,
                serviceCategoryName: item.serviceCategoryId.name,
                subTypeId: item.subTypeId?._id,
                subTypeName: item.subTypeName,
                duration: item.duration,
                sessions: item.sessions,
                room: item.roomId ? item.roomId._id : null,
                roomName: item.roomId ? item.roomId.roomName : "",
                date: item.date,
                from: item.from,
                to: item.to,
            };
        });

        return {
            count: total,
            data,
        };
    }
    private async getALltheStaffs(facilityId: Types.ObjectId): Promise<any[]> {
        const commonPipeline: any[] = [
            {
                $lookup: {
                    from: "staffprofiledetails",
                    localField: "_id",
                    foreignField: "userId",
                    as: "staffDetails",
                    pipeline: [{ $match: { facilityId: { $in: [facilityId] } } }],
                },
            },
            {
                $lookup: {
                    from: 'role',
                    localField: "role",
                    foreignField: "_id",
                    as: "roleDetails",
                    pipeline: [
                        {
                            $match: {
                                type: { $in: [ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.WEB_MASTER] },
                            },
                        },
                    ],
                }
            },
            { $unwind: { path: "$staffDetails", preserveNullAndEmptyArrays: false } },
            { $unwind: { path: "$roleDetails", preserveNullAndEmptyArrays: false } },
            {
                $lookup: {
                    from: "facilities",
                    localField: "staffDetails.facilityId",
                    foreignField: "_id",
                    as: "facilityDetails",
                },
            },
            { $unwind: { path: "$facilityDetails", preserveNullAndEmptyArrays: true } },
        ];
        commonPipeline.push({
            $group: {
                _id: "$staffDetails._id",
                gender: { $first: "$staffDetails.gender" },
                profilePicture: { $first: "$staffDetails.profilePicture" },
                userId: { $first: "$_id" },
                firstName: { $first: "$firstName" },
                lastName: { $first: "$lastName" },
                mobile: { $first: "$mobile" },
                email: { $first: "$email" },
                role: { $first: "$role" },
                isActive: { $first: "$isActive" },
                createdAt: { $first: "$createdAt" },
                facilityNames: { $push: "$facilityDetails.facilityName" },
            },
        });
        commonPipeline.push(
            {
                $sort: {
                    isActive: -1,
                    updatedAt: -1,
                },
            },
            {
                $facet: {
                    metadata: [{ $count: "total" }],
                    data: [
                        {
                            $project: {
                                _id: 1,
                                userId: 1,
                                firstName: 1,
                                lastName: 1,
                                facilityNames: 1,
                                mobile: 1,
                                email: 1,
                                role: 1,
                                isActive: 1,
                                createdAt: 1,
                                gender: 1,
                                profilePicture: 1,
                            },
                        },

                    ],
                },
            },
        );

        // Execute aggregation
        const result = await this.UserModel.aggregate(commonPipeline);
        return result[0]?.data || []

    }


    async exportSchedulesByUser(
        user: IUserDocument,
        body: GetSchedulesByUserDto,
        userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone
    ): Promise<{ count: number; data: string }> {
        const { count, data } = await this.getSchedulesListByUser(user, { ...body, page: 1, pageSize: 100000 });

        const csv = [
            `Client Name,Trainer Name,Package Name,Date,Start Time,End Time,Location,Room,Service Category,Booking Status`,
            ...data.map((item: any) => {
                const formattedDate = moment(item.date).tz(userTimezone).format('DD-MMMM-YYYY');
                const from = moment(item.from, 'HH:mm').format('HH:mm');
                const to = moment(item.to, 'HH:mm').format('HH:mm');

                const bookingStatus = item.scheduleStatus
                    ? item.scheduleStatus.charAt(0).toUpperCase() + item.scheduleStatus.slice(1).toLowerCase()
                    : '';

                return [
                    item.clientName ?? '',
                    item.trainerName ?? '',
                    item.packageName ?? '',
                    formattedDate,
                    from,
                    to,
                    item.facilityName ?? '',
                    item.roomName ?? '',
                    item.serviceCategoryName ?? '',
                    bookingStatus,
                ].join(',');
            })
        ].join('\n');

        return { count, data: csv };
    }

    // async findPurchasesByIds(purchaseIds: string[]) {
    //     // Replace with your actual PurchaseModel and query
    //     return this.PurchaseModel.find({ _id: { $in: purchaseIds.map(id => new Types.ObjectId(id)) } });
    // }

    // async prepWriteForCheckin(user: IUserDocument, purchase: any) {
    //     // Implement your logic based on classType or other fields
    //     // For example:
    //     if (purchase.classType === ClassType.PERSONAL_APPOINTMENT) {
    //         // ...do something
    //     }
    //     // ...other logic
    //     return { purchaseId: purchase._id, status: "checked-in" };
    // }

    async instantBookedAndCheckin(user: IUserDocument, body: InstantCheckinDto, extSessions?: ClientSession): Promise<any> {
        const session = extSessions ?? await this.transactionService.startTransaction();

        try {
            const { purchaseIds, organizationId, facilityId } = body;
            let scheduleIds = []

            if (!purchaseIds || !Array.isArray(purchaseIds) || purchaseIds.length === 0) {
                throw new BadRequestException("Purchases IDs are required and should be a non-empty array.");
            }

            const filter: any = { _id: { $in: purchaseIds.map(id => new Types.ObjectId(id)) } };
            const { role } = user;

            switch (role?.type) {
                case ENUM_ROLE_TYPE.ORGANIZATION:
                    if (user?._id?.toString() !== organizationId?.toString()) {
                        throw new BadRequestException("Access denied, User does not belong to this organization");
                    }
                    filter["organizationId"] = user?._id;
                    break;

                case ENUM_ROLE_TYPE.WEB_MASTER:
                case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                case ENUM_ROLE_TYPE.TRAINER:
                    const staffDetails = await this.StaffProfileModel.findOne(
                        { userId: user?._id },
                        { organizationId: 1, facilityId: 1 }
                    );
                    if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                    if (staffDetails?.organizationId?.toString() !== organizationId?.toString()) {
                        throw new BadRequestException("Access denied, User does not belong to this organization");
                    }
                    if (!staffDetails?.facilityId?.toString()?.includes(facilityId?.toString())) {
                        throw new BadRequestException("Access denied, User does not belong to this facility");
                    }
                    filter["organizationId"] = staffDetails?.organizationId;
                    filter["facilityId"] = staffDetails?.facilityId;
                    break;

                default:
                    throw new BadRequestException("Access denied");
            }

            const purchases = await this.PurchaseModel.find(filter).session(session);
            if (!purchases || purchases.length === 0) {
                throw new NotFoundException("No purchases found for the provided IDs.");
            }
            const packageIds = purchases.map(p => p.packageId);
            const packageDetails = await this.PricingModel.findOne({
                _id: { $in: packageIds },
                //"services.type": ClassType.BOOKINGS,
                organizationId: new Types.ObjectId(organizationId),
            }).session(session);

            if (!packageDetails) {
                throw new NotFoundException("No package found for the provided IDs.");
            }
            if (packageDetails?.services?.type !== ClassType.BOOKINGS) {
                throw new BadRequestException("Only bookings type packages can be checked in instantly.");
            }

            const serviceCategory = packageDetails?.services?.serviceCategory;
            const subType = packageDetails?.services?.appointmentType?.[0];

            const serviceDetails = await this.ServicesModel.findOne({
                _id: new Types.ObjectId(serviceCategory),
                classType: ClassType.BOOKINGS,
                organizationId: new Types.ObjectId(organizationId),
                isActive: { $ne: false }
            }).session(session).lean();

            if (!serviceDetails) {
                throw new NotFoundException("No service found for the provided service category.");
            }

            const serviceSubType = serviceDetails?.appointmentType?.find((item) =>
                item?._id?.toString() === subType?.toString()
            ) as any;

            if (!serviceSubType || serviceSubType?.isActive === false) {
                throw new NotFoundException("No service sub-type found for the provided service category.");
            }

            const appointmentDuration = serviceSubType?.durationInMinutes ?? 30; // fallback default
            const requestedSessions = 1;

            // Get current time in Asia/Kolkata
            let istNow = DateTime.now().setZone("Asia/Kolkata");

            // Round up to next 15-minute slot
            const roundedMinutes = Math.ceil(istNow.minute / 15) * 15;
            if (roundedMinutes === 60) {
                istNow = istNow.plus({ hours: 1 }).set({ minute: 0, second: 0, millisecond: 0 });
            } else {
                istNow = istNow.set({ minute: roundedMinutes, second: 0, millisecond: 0 });
            }

            // Format `from` time as HH:MM
            const from = istNow.toFormat("HH:mm");

            // Calculate `to` time
            const to = istNow.plus({ minutes: appointmentDuration * requestedSessions }).toFormat("HH:mm");

            // Get IST date at start of the day (00:00)

            // Also get JS Date object for actual 'from' time
            const istDateStartOfDay = istNow.startOf("day").toJSDate();
            istDateStartOfDay.setHours(0, 0, 0, 0); // Set to start of the day
            istDateStartOfDay.setMinutes(istDateStartOfDay.getMinutes() + 330);

            for (const purchasesData of purchases) {
                await this.validatePackageEligibility(
                    purchasesData?.userId,
                    istDateStartOfDay,
                    from,
                    to,
                    packageDetails,
                    purchasesData,
                    requestedSessions,
                    undefined,
                    undefined,
                    session
                );

                const body = {
                    facilityId,
                    classType: ClassType.BOOKINGS,
                    serviceCategory,
                    date: istDateStartOfDay,
                    from,
                    to,
                };

                const rooms = await this.roomListForScheduling(body);
                if (!rooms?.length) {
                    throw new NotFoundException(`No rooms available for the provided service category: ${serviceDetails["name"]}`);
                }

                const roomId = rooms?.[0]?._id;
                await this.validateFacilityAvailabilityV1(istDateStartOfDay, { from, to }, facilityId);

                const newSchedule = await new this.SchedulingModel({
                    organizationId,
                    scheduledBy: user?._id,
                    facilityId,
                    subTypeId: subType,
                    serviceCategoryId: serviceCategory,
                    packageId: purchasesData?.packageId,
                    purchaseId: purchasesData?._id,
                    clientId: purchasesData?.userId,
                    classType: ClassType.BOOKINGS,
                    roomId,
                    date: istDateStartOfDay,
                    dateRange: DateRange.SINGLE,
                    duration: requestedSessions * appointmentDuration,
                    sessions: requestedSessions,
                    istDateStartOfDay,
                    from,
                    to,
                    scheduleStatus: ScheduleStatusType.CHECKEDIN,
                    notes: "Instant Booked and Checked In",
                }).save({ session });
                scheduleIds.push(newSchedule._id);
                await this.consumeSessionUpdateInPurchase(purchasesData?._id, requestedSessions, session);
            }
            this.waitTimeGatewayService.sendWaitingTimeUpdate(facilityId);

            if (!extSessions) await session.commitTransaction();
            return await this.getSchedulingData(scheduleIds, session)

        } catch (error) {
            if (!extSessions) await session.abortTransaction();
            throw error;
        } finally {
            if (!extSessions) session.endSession();
        }
    }

    async instantSingleBookedAndCheckin(user: IUserDocument, body: InstantSingleCheckinDto): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const { userId, packageId, invoiceId, organizationId, facilityId } = body;
            let scheduleIds = []

            const { role } = user;
            let filter: any = {
                userId: new Types.ObjectId(userId),
                packageId: new Types.ObjectId(packageId),
                invoiceId: new Types.ObjectId(invoiceId),
                organizationId: new Types.ObjectId(organizationId),
                facilityId: new Types.ObjectId(facilityId),
                sessionType: { $in: [SessionType.MULTIPLE, SessionType.DAY_PASS, SessionType.SINGLE] },
                $expr: {
                    $gt: [
                        {
                            $cond: {
                                if: { $eq: ["$sessionType", SessionType.DAY_PASS] },
                                then: { $subtract: ["$dayPassLimit", "$sessionConsumed"] },
                                else: { $subtract: ["$totalSessions", "$sessionConsumed"] }
                            }
                        },
                        0
                    ]
                }
            };

            switch (role?.type) {
                case ENUM_ROLE_TYPE.ORGANIZATION:
                    if (user?._id?.toString() !== organizationId?.toString()) {
                        throw new BadRequestException("Access denied, User does not belong to this organization");
                    }
                    filter["organizationId"] = user?._id;
                    break;
                case ENUM_ROLE_TYPE.WEB_MASTER:
                case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                case ENUM_ROLE_TYPE.TRAINER:
                    const staffDetails = await this.StaffProfileModel.findOne(
                        { userId: user?._id },
                        { organizationId: 1, facilityId: 1 }
                    );
                    if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                    if (staffDetails?.organizationId?.toString() !== organizationId?.toString()) {
                        throw new BadRequestException("Access denied, User does not belong to this organization");
                    }
                    if (!staffDetails?.facilityId?.toString()?.includes(facilityId?.toString())) {
                        throw new BadRequestException("Access denied, User does not belong to this facility");
                    }
                    filter["organizationId"] = staffDetails?.organizationId;
                    filter["facilityId"] = staffDetails?.facilityId;
                    break;
                default:
                    throw new BadRequestException("Access denied");
            }

            const purchase = await this.PurchaseModel.findOne(filter).session(session);
            if (!purchase) throw new NotFoundException("No purchase found for the provided details.");

            const packageDetails = await this.PricingModel.findOne({
                _id: new Types.ObjectId(packageId),
                organizationId: new Types.ObjectId(organizationId),
            }).session(session);

            if (!packageDetails) throw new NotFoundException("No package found for the provided ID.");
            if (packageDetails?.services?.type !== ClassType.BOOKINGS) {
                throw new BadRequestException("Only bookings type packages can be checked in instantly.");
            }

            const serviceCategory = packageDetails?.services?.serviceCategory;
            const subType = packageDetails?.services?.appointmentType?.[0];

            const serviceDetails = await this.ServicesModel.findOne({
                _id: new Types.ObjectId(serviceCategory),
                classType: ClassType.BOOKINGS,
                organizationId: new Types.ObjectId(organizationId),
                isActive: { $ne: false }
            }).session(session).lean();

            if (!serviceDetails) throw new NotFoundException("No service found for the provided service category.");

            const serviceSubType = serviceDetails?.appointmentType?.find((item) =>
                item?._id?.toString() === subType?.toString()
            ) as any;

            if (!serviceSubType || serviceSubType?.isActive === false) {
                throw new NotFoundException("No service sub-type found for the provided service category.");
            }

            const appointmentDuration = serviceSubType?.durationInMinutes ?? 30;
            const requestedSessions = 1;

            // Get current time in Asia/Kolkata
            let istNow = DateTime.now().setZone("Asia/Kolkata");
            const roundedMinutes = Math.ceil(istNow.minute / 15) * 15;
            if (roundedMinutes === 60) {
                istNow = istNow.plus({ hours: 1 }).set({ minute: 0, second: 0, millisecond: 0 });
            } else {
                istNow = istNow.set({ minute: roundedMinutes, second: 0, millisecond: 0 });
            }
            const from = istNow.toFormat("HH:mm");
            const to = istNow.plus({ minutes: appointmentDuration * requestedSessions }).toFormat("HH:mm");

            const istDateStartOfDay = istNow.startOf("day").toJSDate();
            istDateStartOfDay.setHours(0, 0, 0, 0);
            istDateStartOfDay.setMinutes(istDateStartOfDay.getMinutes() + 330);

            await this.validatePackageEligibility(
                userId,
                istDateStartOfDay,
                from,
                to,
                packageDetails,
                purchase,
                requestedSessions
            );

            const bodyForRoom = {
                facilityId,
                classType: ClassType.BOOKINGS,
                serviceCategory,
                date: istDateStartOfDay,
                from,
                to,
            };

            const rooms = await this.roomListForScheduling(bodyForRoom);
            if (!rooms?.length) {
                throw new NotFoundException(`No rooms available for the provided service category: ${serviceDetails["name"]}`);
            }
            const roomId = rooms?.[0]?._id;
            await this.validateFacilityAvailabilityV1(istDateStartOfDay, { from, to }, facilityId);

            const newSchedule = await new this.SchedulingModel({
                organizationId,
                scheduledBy: user?._id,
                facilityId,
                subTypeId: subType,
                serviceCategoryId: serviceCategory,
                packageId: packageId,
                purchaseId: purchase?._id,
                clientId: userId,
                classType: ClassType.BOOKINGS,
                roomId,
                date: istDateStartOfDay,
                dateRange: DateRange.SINGLE,
                duration: requestedSessions * appointmentDuration,
                sessions: requestedSessions,
                istDateStartOfDay,
                from,
                to,
                scheduleStatus: ScheduleStatusType.CHECKEDIN,
                notes: "Single Instant Booked and Checked In",
                invoiceId: invoiceId,
            }).save({ session });
            scheduleIds.push(newSchedule._id);
            await this.consumeSessionUpdateInPurchase(purchase?._id, requestedSessions);

            this.waitTimeGatewayService.sendWaitingTimeUpdate(facilityId);
            await session.commitTransaction();
            session.endSession();
            return await this.getSchedulingData(scheduleIds)


        } catch (error) {
            await session.abortTransaction();
            session.endSession();
            throw error;
        }
    }
    private async getSchedulingData(scheculeIds, session?: ClientSession): Promise<any> {
        let filter = { _id: { $in: scheculeIds.map(id => new Types.ObjectId(id)) } };

        const agg: PipelineStage[] = [
            {
                $match: filter,
            },
            {
                $lookup: {
                    from: "users",
                    localField: "clientId",
                    foreignField: "_id",
                    as: "client",
                    pipeline: [
                        {
                            $project: {
                                name: 1,
                                firstNam: 1,
                                lastName: 1,
                                email: 1,
                                mobile: 1,
                                isActive: 1,
                            },
                        },
                    ],
                },
            },
            {
                $set: {
                    client: {
                        $arrayElemAt: ["$client", 0],
                    },
                },
            },
            {
                $facet: {
                    total: [
                        {
                            $count: "count",
                        },
                    ],
                    scheduleIds: [
                        {
                            $sort: {
                                updatedAt: -1,
                            },
                        },
                        {
                            $project: {
                                _id: 1,
                            },
                        },
                    ],
                },
            },
            {
                $replaceRoot: {
                    newRoot: {
                        total: {
                            $arrayElemAt: ["$total.count", 0],
                        },
                        scheduleIds: "$scheduleIds",
                    },
                },
            },
        ];
        const idsList = await this.SchedulingModel.aggregate(agg).session(session);
        const { scheduleIds } = idsList.length ? idsList[0] : { scheduleIds: [] };
        const populateFields = [
            { path: "scheduledBy", select: "_id name firstName lastName email" },
            //{ path: "facilityId", select: "_id facilityName address profilePicture" },
            //{ path: "trainerId", select: "_id name firstName lastName email" },
            // { path: 'organizationId', select: '_id name email' },
            { path: "clientId", select: "_id firstName lastName email mobile isActive" },
            { path: "packageId", select: "_id name price expiredInDays durationUnit" },
            // { path: 'subTypeId', select: '_id name description classType' },
            { path: "serviceCategoryId", select: "_id name classType appointmentType isActive" },
            { path: "roomId", select: "_id roomName capacity description" },
        ];
        const schedules = await this.SchedulingModel.find({ _id: { $in: scheduleIds.map((item) => item._id) } })
            .sort({ updatedAt: -1 })
            .populate(populateFields).session(session);
        const enrichedSchedules = schedules.map((schedule: any) => {
            const appointmentTypes = schedule?.serviceCategoryId?.appointmentType || [];
            const matchedAppointment = appointmentTypes.find((apt: any) => apt._id.toString() === schedule.subTypeId?.toString());

            return {
                ...schedule.toObject(),
                subTypeName: matchedAppointment?.name || null,
                subTypeId: matchedAppointment?._id || null,
            };
        });
        const userIds = enrichedSchedules?.filter((item: any) => item.clientId?._id).map((item: any) => item.clientId?._id);
        const clients = await this.ClientsModel.find({ userId: { $in: userIds } }, { userId: 1, clientId: 1 });
        const data = enrichedSchedules?.map((item: any) => {
            let customerId: any = clients.find((client) => client.userId.toString() == item.clientId?._id?.toString());
            customerId = customerId ? customerId.clientId : "";


            return {
                _id: item._id,
                //facilityId: item.facilityId._id,
                // trainerId: item.trainerId ? item.trainerId._id : "",
                // trainerName: item.trainerId ? `${item.trainerId.firstName} ${item.trainerId.lastName}` : "",
                //facilityName: item.facilityId.facilityName,
                clientId: item.clientId?._id,
                clientName: item.clientId?.firstName ? `${item.clientId?.firstName} ${item.clientId?.lastName}` : null,
                clientFirstName: item.clientId?.firstName,
                clientLastName: item.clientId?.lastName,
                clientEmail: item.clientId?.email,
                clientPhone: item.clientId?.mobile,
                customerId: customerId,
                packageId: item.packageId._id,
                packageName: item.packageId.name,
                classType: item.classType,
                scheduleStatus: item.scheduleStatus,
                serviceCategoryId: item.serviceCategoryId._id,
                serviceCategoryName: item.serviceCategoryId.name,
                subTypeId: item.subTypeId?._id,
                subTypeName: item.subTypeName,
                duration: item.duration,
                sessions: item.sessions,
                room: item.roomId ? item.roomId._id : null,
                roomName: item.roomId ? item.roomId.roomName : "",
                date: item.date,
                from: item.from,
                to: item.to,
            };
        });
        return data

    }


}
