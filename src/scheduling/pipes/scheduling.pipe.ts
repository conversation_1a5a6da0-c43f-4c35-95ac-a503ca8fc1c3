import { Injectable } from "@nestjs/common";
import { CheckStaffAvailabilityDto } from "../dto/check-staff-availability.dto";
import { Types } from "mongoose";

@Injectable()
export class SchedulingPipe {
    async availableStaffPipe(checkStaffAvailability: CheckStaffAvailabilityDto, organizationId: string, availableDates: Array<string>): Promise<any> {
        let pipeline = [
            {
                $match: {
                    serviceType: "classes",
                    organizationId: organizationId,
                    serviceCategory: Types.ObjectId.createFromHexString(checkStaffAvailability.serviceId),
                },
            },
            {
                $lookup: {
                    from: "staffavailabilities",
                    localField: "userId",
                    foreignField: "userId",
                    as: "availabilities",
                    pipeline: [
                        {
                            $match: {
                                facilityId: Types.ObjectId.createFromHexString(checkStaffAvailability.facilityId),
                                date: {
                                    $in: availableDates,
                                },
                                timeSlots: {
                                    $elemMatch: {
                                        availabilityStatus: "available",
                                    },
                                },
                            },
                        },
                        {
                            $addFields: {
                                dateDay: { $dayOfWeek: "$date" }, // Get the day of the week for each date
                                timeSlotsForDay: {
                                    $arrayElemAt: [
                                        {
                                            $map: {
                                                input: availableDates, // Map through available dates
                                                as: "dateEntry",
                                                in: {
                                                    $cond: [
                                                        { $eq: [{ $dayOfWeek: "$$dateEntry" }, { $dayOfWeek: "$date" }] },
                                                        {
                                                            $switch: {
                                                                branches: [
                                                                    { case: { $eq: [{ $dayOfWeek: "$$dateEntry" }, 1] }, then: [{ from: "08:00", to: "12:00" }] }, // Sunday
                                                                    { case: { $eq: [{ $dayOfWeek: "$$dateEntry" }, 2] }, then: [{ from: "09:00", to: "13:00" }] }, // Monday
                                                                    { case: { $eq: [{ $dayOfWeek: "$$dateEntry" }, 3] }, then: [{ from: "10:00", to: "14:00" }] }, // Tuesday
                                                                    { case: { $eq: [{ $dayOfWeek: "$$dateEntry" }, 4] }, then: [{ from: "11:00", to: "15:00" }] }, // Wednesday
                                                                    { case: { $eq: [{ $dayOfWeek: "$$dateEntry" }, 5] }, then: [{ from: "12:00", to: "16:00" }] }, // Thursday
                                                                    { case: { $eq: [{ $dayOfWeek: "$$dateEntry" }, 6] }, then: [{ from: "13:00", to: "17:00" }] }, // Friday
                                                                    { case: { $eq: [{ $dayOfWeek: "$$dateEntry" }, 7] }, then: [{ from: "14:00", to: "18:00" }] }, // Saturday
                                                                ],
                                                                default: [],
                                                            },
                                                        },
                                                        null,
                                                    ],
                                                },
                                            },
                                        },
                                        0,
                                    ],
                                },
                            },
                        },
                        {
                            $addFields: {
                                validTimeSlot: {
                                    $anyElementTrue: {
                                        $map: {
                                            input: "$timeSlotsForDay",
                                            as: "daySlot",
                                            in: {
                                                $and: [{ $gte: ["$$daySlot.from", "$timeSlots.from"] }, { $lte: ["$$daySlot.to", "$timeSlots.to"] }],
                                            },
                                        },
                                    },
                                },
                            },
                        },
                        {
                            $match: {
                                validTimeSlot: true, // Ensure valid time slots exist for each day
                            },
                        },
                    ],
                },
            },
            {
                $unwind: {
                    path: "$availabilities",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $group:
                    /**
                     * _id: The id of the group.
                     * fieldN: The first field name.
                     */
                    {
                        _id: "$userId",
                    },
            },
            {
                $lookup:
                    /**
                     * from: The target collection.
                     * localField: The local join field.
                     * foreignField: The target join field.
                     * as: The name for the results.
                     * pipeline: Optional pipeline to run on the foreign collection.
                     * let: Optional variables to use in the pipeline field stages.
                     */
                    {
                        from: "users",
                        localField: "_id",
                        foreignField: "_id",
                        as: "staffDetails",
                    },
            },
            {
                $unwind:
                    /**
                     * path: Path to the array field.
                     * includeArrayIndex: Optional name for index.
                     * preserveNullAndEmptyArrays: Optional
                     *   toggle to unwind null and empty values.
                     */
                    {
                        path: "$staffDetails",
                        preserveNullAndEmptyArrays: false,
                    },
            },
            {
                $project:
                    /**
                     * specifications: The fields to
                     *   include or exclude.
                     */
                    {
                        _id: 0,
                    },
            },
            {
                $replaceRoot:
                    /**
                     * replacementDocument: A document or string.
                     */
                    {
                        newRoot: "$staffDetails",
                    },
            },
            {
                $project:
                    /**
                     * specifications: The fields to
                     *   include or exclude.
                     */
                    {
                        name: 1,
                        firstName: 1,
                        lastName: 1,
                        mobile: 1,
                        email: 1,
                    },
            },
        ];
        return "pipeline";
    }
}
