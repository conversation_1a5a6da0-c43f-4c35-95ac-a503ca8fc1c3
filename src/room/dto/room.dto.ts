import { ApiProperty } from "@nestjs/swagger";
import {
  IsString,
  IsNotEmpty,
  IsBoolean,
  IsOptional,
  IsArray,
  IsNumber,
  IsMongoId,
} from "class-validator";
import { PaginationDTO } from "src/users/dto/pagination.dto";

export class CreateRoomDto {
  @ApiProperty({
    description: "Name of the room",
    example: "Conference Room A",
  })
  @IsNotEmpty()
  @IsString()
  roomName: string;

  @ApiProperty({
    description: "Capacity of the room",
    example: 20,
  })
  @IsNotEmpty()
  @IsNumber()
  capacity: number;

  @ApiProperty({
    description: "Availability status of the room",
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  status?: boolean;

  @ApiProperty({
    description: "Location of the room",
    example: "2nd Floor, Building A",
  })
  @IsNotEmpty()
  @IsString()
  facilityId: string;

  @ApiProperty({
    description: "Type of the room (e.g., conference, meeting)",
    example: ["conference", "meeting"],
  })
  @IsNotEmpty()
  @IsArray()
  @IsString({ each: true })
  classType: string[];

  @ApiProperty({
    description: "Service categories offered by the room",
    example: ["training", "consultation"],
  })
  @IsNotEmpty()
  @IsArray()
  @IsString({ each: true })
  serviceCategory: string[];

  @ApiProperty({
    description: "Additional description of the room",
    example: "This is a well-equipped room with a projector and AC.",
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: "Image URL of the room",
    example: "https://example.com/images/room.jpg",
  })
  @IsOptional()
  @IsString()
  image?: string;


}


export class RoomListDto extends PaginationDTO {
  @ApiProperty({
    description: "Text on which you want to apply search | Working only on Room name",
    example: "Rvi",
    required: false,
  })
  @IsOptional()
  @IsString({ message: "Required valid search string" })
  search: string;
  @ApiProperty({
    description: "Class Type of the room",
    example: "['class','booking']",
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  classType?: string[];
  @ApiProperty({
    description: "Filter by Facility ID",
    example: "63f1c19e8b4c5c0012f3b123",
    required: false,
  })
  @IsOptional()
  @IsMongoId({ message: "Invalid facility ID format" }) // Ensures the facilityId is a valid MongoDB ObjectId
  facilityId?: string;

  @ApiProperty({
    description: "Filter by Room status (true for active rooms)",
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: "Status must be a boolean value" }) // Ensures the status is a boolean
  status?: boolean;

}
export class RoomListByFacilitiesDto {
  @ApiProperty({
    description: "Location of the room",
    example: "66cedf7a731d1269a4157a2d",
  })
  @IsString()
  @IsNotEmpty()
  @IsMongoId()
  facilityId: string;

  @ApiProperty({
    description: "service Category of the room",
    example: ["674039a1ba6c7e5eda123193"],
  })
  @IsOptional()
  @IsArray()
  @IsMongoId({ each: true })
  serviceCategoryId?: string[];

  @ApiProperty({
    description: "class type of the room",
    example: ["bookings"],
  })
  @IsOptional()
  @IsArray()
  classType?: string[];
  @ApiProperty({
    description: "List of room IDs to filter",
    example: ["66cedf7a731d1269a4157a2d", "674039a1ba6c7e5eda123193"],
  })
  @IsOptional()
  @IsArray()
  @IsMongoId({ each: true })
  roomIds?: string[];
}
