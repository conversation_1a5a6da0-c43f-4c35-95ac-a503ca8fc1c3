import { BadRequestException, Body, Controller, Post, UseGuards, Get, Param, NotFoundException, Patch, Delete } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { AuthGuard } from "@nestjs/passport";
import { RolesGuard } from "src/auth/roles.guard";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { Roles } from "src/auth/decorators/roles.decorator";
import { RoomService } from "../service/room.service";
import { CreateRoomDto, RoomListDto, RoomListByFacilitiesDto } from "../dto/room.dto";
import { GetRoomsForSchedulingDto } from "../dto/get-rooms.dto";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { PolicyAbilityProtected, PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";
import { ENUM_PERMISSION_TYPE } from "src/policy/enums/policy.permissions.enum";
import { RoomAvailabilityDto } from "../dto/room-availability.dto";

@ApiTags("Rooms")
@ApiBearerAuth()
@Controller("rooms")
export class RoomController {
  constructor(private readonly roomService: RoomService) { }

  /**
   * Create a new room
   */
  @Post("/create")
  @ApiOperation({ summary: "Create a new room" })
  @PolicyAbilityProtected()
  @AuthJwtAccessProtected()
  async createRoom(
    @Body() createRoomDto: CreateRoomDto,
    @GetUser() user: any, // Extract the authenticated user
  ) {
    try {
      const payload: any = {
        ...createRoomDto,
        createdBy: user._id,
      };
      const result = await this.roomService.createRoom(payload, user);
      return result;

    } catch (error) {
      console.log(error)
      throw new BadRequestException(error.message || "Room creation failed");
    }
  }

  /* get list of the room according to the user */
  @Post("/list")
  @PolicyAbilityProtected()
  @AuthJwtAccessProtected()
  @ApiOperation({ summary: "Get all the Room of the User" })
  async roomList(@GetUser() user, @Body() roomListDto: RoomListDto): Promise<any> {
    const roomListResult = await this.roomService.roomList(roomListDto, user);
    return roomListResult;
  }

  @Get(":roomId")
  @PolicyAbilityProtected()
  @AuthJwtAccessProtected()
  @ApiOperation({ summary: "Get Room  by ID" })
  async getPayRate(@Param("roomId") roomId: string, @GetUser() user) {
    const room = await this.roomService.roomById(roomId, user);
    if (!room) {
      throw new NotFoundException('Room not found');
    }
    return room
  }

  @Patch("/update/:roomId")
  @PolicyAbilityProtected()
  @AuthJwtAccessProtected()
  @ApiOperation({ summary: "Room Update" })
  async roomUpdate(@Param("roomId") roomId: string, @GetUser() user, @Body() updateRoomDto: CreateRoomDto): Promise<{ message: String; data: any }> {
    let output = await this.roomService.roomUpdate(updateRoomDto, roomId, user);
    return {
      message: "Room update Successfully",
      data: output,
    };
  }
  @Delete(":roomId")
  @PolicyAbilityProtected()
  @AuthJwtAccessProtected()
  @ApiOperation({ summary: "Delete the Room" })
  async deleteRoom(@Param("roomId") roomId: string) {
    return await this.roomService.delete(roomId);
  }
  @Patch("/status/:roomId")
  @PolicyAbilityProtected()
  @AuthJwtAccessProtected()
  @ApiOperation({ summary: "Update the Room Status" })
  async roomStatusUpdate(@Param("roomId") roomId: string, @GetUser() user, @Body() status) {
    return await this.roomService.updateRoomStatus(status, roomId);

  }
  @Post("/room-list/serviceCategoryId")
  @UseGuards(AuthGuard(), RolesGuard)
  @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN, ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.USER)
  @ApiOperation({ summary: "Get List of all the room of the user according to the Service Category Id " })
  async roomListByServiceCategory(@GetUser() user, @Body() payload): Promise<any> {
    const roomListResult = await this.roomService.roomListByServiceId(payload, user);
    return roomListResult

  }
  @Post("/room-list/byFacilitiesId")
  @PolicyAbilityProtected()
  @AuthJwtAccessProtected()
  @ApiOperation({ summary: "Get List of all the room of the user according to the Facilities Id " })
  async roomListByFacilityId(@GetUser() user, @Body() payload: RoomListByFacilitiesDto): Promise<any> {
    const roomListResult = await this.roomService.roomListByFacilitiesId(payload, user);
    return roomListResult

  }

  @Get("wait-time/:facilityId")
  @ApiOperation({ summary: "Get Room wait-time by FacilityId" })
  async getWaitTime(@Param("facilityId") facilityId: string, @GetUser() user) {
    const room = await this.roomService.roomWaitTime(facilityId);
    if (!room) {
      throw new NotFoundException('Room not found');
    }
    return room
  }

  @Post("/list/scheduling")
  @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN)
  @AuthJwtAccessProtected()
  @ApiOperation({ summary: "Get List of all the room available for scheduling" })
  async roomListForScheduling(@GetUser() user, @Body() getRoomsForScheduling: GetRoomsForSchedulingDto): Promise<any> {
    let response = await this.roomService.roomListForScheduling(user, getRoomsForScheduling);
    return {
      message: "Room List for Scheduling",
      data: response,
    }

  }

  @Post("/availability")
  @ApiOperation({ summary: "Check room availability by classType, serviceCategoryId, and date" })
  async checkRoomAvailability(@Body() dto: RoomAvailabilityDto): Promise<any> {
    const availableRooms = await this.roomService.getAvailableRoomsByCriteria(dto);
    return {
      message: "Available rooms fetched successfully",
      data: availableRooms,
    };
  }
}
