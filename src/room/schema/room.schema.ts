import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { ObjectId, SchemaTypes, Document,Types } from 'mongoose';
import { DateRange } from "src/utils/enums/date-range-enum";

@Schema({ timestamps: true })
export class Room extends Document {
    @Prop({ type: String, required: true })
    roomName: string;
    @Prop({ type: Number, required: true })
    capacity: number;
    @Prop({ type: Boolean, default: true })
    status: boolean;
    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Facility" })
    facilityId: string;
    @Prop({ type: [String], required: false }) // Updated to be an array of strings
    classType: string[];
    @Prop({ type: [SchemaTypes.ObjectId],required:false,ref:'Services'})
    serviceCategory?: Types.ObjectId[];
    @Prop({ type: String, required: false })
    description: string;
    @Prop({ type: String, required: false })
    image: string;
    @Prop({ type: SchemaTypes.ObjectId, ref: "User" })
    createdBy: string;
    @Prop({ type: SchemaTypes.ObjectId, ref: "User" })
    organizationId: string;
  
}

export const RoomSchema = SchemaFactory.createForClass(Room);
