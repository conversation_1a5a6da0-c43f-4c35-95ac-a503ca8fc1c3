import { forwardRef, Global, Module } from "@nestjs/common";
import { FacilityController } from "./controllers/facility.controller";
import { FacilityService } from "./services/facility.service";
import { FacilityPipe } from "./pipes/facility.pipe";
import { AuthModule } from "src/auth/auth.module";
import { UtilsModule } from "src/utils/utils.module";
import { MailModule } from "src/mail/mail.module";
import { MongooseModule } from "@nestjs/mongoose";
import { User, UserSchema } from "src/users/schemas/user.schema";
import { Facility, FacilitySchema } from "./schemas/facility.schema";
import { StaffProfileDetails, StaffSchema } from "src/staff/schemas/staff.schema";
import { Cities, CitiesSchema } from "src/utils/schemas/cities.schema";
import { FacilityAvailability, FacilityAvailabilitySchema } from "./schemas/facility-availability.schema";
import { Scheduling, SchedulingSchema } from "src/scheduling/schemas/scheduling.schema";
import { DATABASE_PRIMARY_CONNECTION_NAME } from "src/common/database/constants/database.constant";

@Global()
@Module({
    imports: [
        forwardRef(() => AuthModule),
        UtilsModule,
        MailModule,
        MongooseModule.forFeature([
            { name: User.name, schema: UserSchema },
            { name: Facility.name, schema: FacilitySchema },
            { name: StaffProfileDetails.name, schema: StaffSchema },
            { name: Cities.name, schema: CitiesSchema },
            { name: FacilityAvailability.name, schema: FacilityAvailabilitySchema },
            { name: Scheduling.name, schema: SchedulingSchema }],
            DATABASE_PRIMARY_CONNECTION_NAME
        ),
    ],
    controllers: [FacilityController],
    providers: [FacilityService, FacilityPipe],
    exports: [FacilityService, FacilityPipe],
})
export class FacilityModule { }
