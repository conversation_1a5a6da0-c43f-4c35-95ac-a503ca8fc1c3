import { Injectable } from "@nestjs/common";
import { FacilityDetailsDto } from "../dto/facility-details.dto";
import { Types } from "mongoose";
import { FacilityListDto } from "../dto/facility-list.dto";
import { WorkingHours } from "../dto/update-facility.dto";

@Injectable()
export class FacilityPipe {
    facilityDetailsOrg(facilityDetailsDto: FacilityDetailsDto, orgId: string) {
        let pipeline = [
            {
                $match: {
                    organizationId: new Types.ObjectId(orgId),
                    _id: Types.ObjectId.createFromHexString(facilityDetailsDto.facilityId),
                },
            },
            {
                $lookup: {
                    from: "cities",
                    localField: "address.city",
                    foreignField: "_id",
                    as: "cityDetails",
                },
            },
            {
                $lookup: {
                    from: "facilityavailabilities",
                    localField: "_id",
                    foreignField: "facilityId",
                    as: "facilityAvailabilities",
                    pipeline: [
                        {
                            $project: {
                                type: 1,
                                workingHours: 1,
                                fromDate: 1,
                                endDate: 1,
                                time: 1,
                            },
                        },
                    ],
                },
            },
            {
                $project: {
                    organizationId: 1,
                    facilityName: 1,
                    address: 1,
                    billingDetails:1,
                    profilePicture: 1,
                    gallery: 1,
                    amenities: 1,
                    city: "$address.city",
                    cityName: "$cityDetails.name",
                    contactName: 1,
                    description: 1,
                    email: 1,
                    mobile: 1,
                    availability: "$facilityAvailabilities",
                },
            },
        ] as any;
        return pipeline;
    }

    facilityDetailsSuperAdmin(facilityDetailsDto: FacilityDetailsDto) {
        let pipeline = [
            {
                $match: {
                    _id: Types.ObjectId.createFromHexString(facilityDetailsDto.facilityId),
                },
            },
            {
                $lookup: {
                    from: "cities",
                    localField: "address.city",
                    foreignField: "_id",
                    as: "cityDetails",
                },
            },
            {
                $lookup: {
                    from: "facilityavailabilities",
                    localField: "_id",
                    foreignField: "facilityId",
                    as: "facilityAvailabilities",
                    pipeline: [
                        {
                            $project: {
                                type: 1,
                                workingHours: 1,
                                fromDate: 1,
                                endDate: 1,
                                time: 1,
                            },
                        },
                    ],
                },
            },
            {
                $project: {
                    organizationId: 1,
                    facilityName: 1,
                    address: 1,
                    billingDetails:1,
                    profilePicture: 1,
                    gallery: 1,
                    amenities: 1,
                    city: "$address.city",
                    cityName: "$cityDetails.name",
                    availability: "$facilityAvailabilities",
                },
            },
        ] as any;
        return pipeline;
    }

    facilityDetailsStaff(facilityDetailsDto: FacilityDetailsDto) {
        let pipeline = [
            {
                $match: {
                    _id: Types.ObjectId.createFromHexString(facilityDetailsDto.facilityId),
                },
            },
            {
                $lookup: {
                    from: "cities",
                    localField: "address.city",
                    foreignField: "_id",
                    as: "cityDetails",
                },
            },
            {
                $lookup:
                    /**
                     * from: The target collection.
                     * localField: The local join field.
                     * foreignField: The target join field.
                     * as: The name for the results.
                     * pipeline: Optional pipeline to run on the foreign collection.
                     * let: Optional variables to use in the pipeline field stages.
                     */
                    {
                        from: "states",
                        localField: "address.state",
                        foreignField: "_id",
                        as: "stateDetails",
                    },
            },
            {
                $lookup: {
                    from: "facilityavailabilities",
                    localField: "_id",
                    foreignField: "facilityId",
                    as: "facilityAvailabilities",
                    pipeline: [
                        {
                            $project: {
                                type: 1,
                                workingHours: 1,
                                fromDate: 1,
                                endDate: 1,
                                time: 1,
                            },
                        },
                    ],
                },
            },
            {
                $lookup:
                    /**
                     * from: The target collection.
                     * localField: The local join field.
                     * foreignField: The target join field.
                     * as: The name for the results.
                     * pipeline: Optional pipeline to run on the foreign collection.
                     * let: Optional variables to use in the pipeline field stages.
                     */
                    {
                        from: "amenities",
                        localField: "amenities",
                        foreignField: "_id",
                        as: "amenityDetails",
                        pipeline: [
                            {
                                $project: {
                                    name: 1,
                                },
                            },
                        ],
                    },
            },
            {
                $project: {
                    organizationId: 1,
                    facilityName: 1,
                    address: 1,
                    billingDetails:1,
                    profilePicture: 1,
                    email: 1,
                    mobile: 1,
                    contactName: 1,
                    description: 1,
                    gallery: 1,
                    amenities: 1,
                    city: "$address.city",
                    cityName: "$cityDetails.name",
                    stateName: "$stateDetails.name",
                    availability: "$facilityAvailabilities",
                    amenityDetails: 1,
                },
            },
        ] as any;
        return pipeline;
    }

    facilityListFrontDesk(facilityListDto: FacilityListDto, frontDeskId: string, search: string) {
        const pageSize = facilityListDto.pageSize ?? 10;
        const page = facilityListDto.page ?? 1;
        const skip = pageSize * (page - 1);

        let pipeline = [
            {
                $match: {
                    userId: frontDeskId,
                },
            },
            {
                $unwind: {
                    path: "$facilityId",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $lookup: {
                    from: "facilities",
                    localField: "facilityId",
                    foreignField: "_id",
                    as: "facilityDetails",
                },
            },
            {
                $unwind: {
                    path: "$facilityDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            ...(search ? [{
                $match: {
                    $or: [
                        {
                            name: {
                                $regex: `.*${search}.*`,
                                $options: "i",
                            },
                        },
                    ],
                },
            }]:[]),
            {
                $facet: {
                    list: [
                        {
                            $skip: skip,
                        },
                        {
                            $limit: pageSize,
                        },
                        {
                            $project: {
                                facilityName: "$facilityDetails.facilityName",
                                address:"$facilityDetails.address",
                                _id: "$facilityDetails._id",
                            },
                        },
                    ],
                    total: [
                        {
                            $count: "total",
                        },
                    ],
                },
            },
        ] as any;
        return pipeline;
    }

    facilityListWebMaster(facilityListDto: FacilityListDto, frontDeskId: string, search: string) {
        const pageSize = facilityListDto.pageSize ?? 10;
        const page = facilityListDto.page ?? 1;
        const skip = pageSize * (page - 1);

        let pipeline = [
            {
                $match: {
                    userId: frontDeskId,
                },
            },
            {
                $unwind: {
                    path: "$facilityId",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $lookup: {
                    from: "facilities",
                    localField: "facilityId",
                    foreignField: "_id",
                    as: "facilityDetails",
                },
            },
            {
                $unwind: {
                    path: "$facilityDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $facet: {
                    list: [
                        {
                            $match: {
                                $or: [
                                    {
                                        "facilityDetails.facilityName": {
                                            $regex: `.*${search}.*`,
                                            $options: "i",
                                        },
                                    },
                                ],
                            },
                        },
                        {
                            $skip: skip,
                        },
                        {
                            $limit: pageSize,
                        },
                        {
                            $project: {
                                facilityName: "$facilityDetails.facilityName",
                                _id: "$facilityDetails._id",
                                organizationId: "$facilityDetails.organizationId",
                                address:  "$facilityDetails.address",
                                billingDetails: "$facilityDetails.billingDetails",
                                profilePicture: "$facilityDetails.profilePicture",
                                gallery: "$facilityDetails.gallery" ,
                                amenities: "$facilityDetails.amenities",
                                city: "$facilityDetails.address.city",
                                cityName: "$facilityDetails.cityDetails.name",
                                contactName: "$facilityDetails.contactName",
                                description: "$facilityDetails.description",
                                email: "$facilityDetails.email",
                                mobile: "$facilityDetails.mobile",
                                availability: "$facilityAvailabilities",
                                isStoreActive: "$facilityDetails.isStoreActive",
                            },
                        },
                    ],
                    total: [
                        {
                            $count: "total",
                        },
                    ],
                },
            },
        ] as any;
        return pipeline;
    }
}
