import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import { User } from "src/users/schemas/user.schema";
import { Facility, FacilityDocument } from "../schemas/facility.schema";
import { MailService } from "src/mail/services/mail.service";
import { TransactionService } from "src/utils/services/transaction.service";
import { InitiateFacilityCreationDto } from "../dto/initiate-facility-creation.dto";
import { FacilityListDto } from "../dto/facility-list.dto";
import { UpdateFacilityDto } from "../dto/update-facility.dto";
import { FacilityDetailsDto } from "../dto/facility-details.dto";
import { FacilityPipe } from "../pipes/facility.pipe";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { Cities } from "src/utils/schemas/cities.schema";
import { FacilityDetailsByStaffDto } from "../dto/facility-details-by-staff.dto";
import { AvailabilityType } from "../enums/availability-type.enum";
import { FacilityAvailability } from "../schemas/facility-availability.schema";
import { FacilityUnavailabilityDto } from "../dto/facility-unavailability.dto";
import { FacilityAvailabilityDetailsDto } from "../dto/facility-availability-details.dto";
import { AddFacilityUnavailabilityDto } from "../dto/add-facility-unavailability.dto";
import { UpdateFacilityUnavailabilityDto } from "../dto/update-facility-unavailability.dto";
import { DeleteFacilityUnavailabilityDto } from "../dto/delete-facility-unavailability.dto";
import { BulkUpdateFacilityUnavailabilityDto } from "../dto/bulk-update-facility-unavailability.dto";
import { ChangeStatusDto } from "src/organization/dto/change-status.dto";
import { Scheduling } from "src/scheduling/schemas/scheduling.schema";
import { ScheduleStatusType } from "src/scheduling/enums/schedule-status.enum";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { Clients } from "src/users/schemas/clients.schema";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { CachingService } from "src/common/caching/services/caching.service";

@Injectable()
export class FacilityService {
    constructor(
        @InjectModel(User.name) private UserModel: Model<User>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        @InjectModel(FacilityAvailability.name) private FacilityAvailabilityModel: Model<FacilityAvailability>,
        @InjectModel(Cities.name) private CityModel: Model<Cities>,
        @InjectModel(StaffProfileDetails.name) private StaffProfileModel: Model<StaffProfileDetails>,
        @InjectModel(Scheduling.name) private SchedulingModel: Model<Scheduling>,
        @InjectModel(Clients.name) private ClientModel: Model<Clients>,
        private readonly mailService: MailService,
        private readonly transactionService: TransactionService,
        private readonly facilityPipe: FacilityPipe,
        private readonly cachingService: CachingService,
    ) { }

    async registerFacility(initiateFacilityCreationDto: InitiateFacilityCreationDto): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            let checkValidOrg = await this.UserModel.findOne({ _id: initiateFacilityCreationDto.organizationId });
            if (!checkValidOrg) throw new BadRequestException("Invalid organization");
            let facilityDetails = new this.FacilityModel(initiateFacilityCreationDto);
            await facilityDetails.save({ session });
            let cityDetails = await this.CityModel.findOne({ _id: initiateFacilityCreationDto.address.city });
            let dataForMail = {
                organizationName: checkValidOrg["name"],
                facilityName: initiateFacilityCreationDto.facilityName,
                location: cityDetails["name"],
            };
            this.mailService.sendMail({
                to: checkValidOrg["email"].toString(),
                subject: `Welcome to ${initiateFacilityCreationDto.facilityName} at ${checkValidOrg["name"]}`,
                template: "facility-creation",
                context: dataForMail,
            });
            await this.transactionService.commitTransaction(session);
            return {
                message: "Facility created",
                data: facilityDetails,
            };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async facilityListSuperAdmin(facilityListDto: FacilityListDto): Promise<any> {
        if (!facilityListDto.organizationId) throw new BadRequestException("Required valid organization details");

        const pageSize = facilityListDto.pageSize ?? 10;
        const page = facilityListDto.page ?? 1;
        const skip = pageSize * (page - 1);
        let query = {
            organizationId: facilityListDto.organizationId,
        };
        if (facilityListDto.search !== "" && facilityListDto.search) {
            const titleQueryString = facilityListDto.search?.trim().split(" ").join("|");
            query["$or"] = [{ facilityName: { $regex: `.*${titleQueryString}.*`, $options: "i" } }];
        }
        const total = await this.FacilityModel.countDocuments(query);
        const data = await this.FacilityModel.find(query).sort({ createdAt: -1 }).skip(skip).limit(pageSize);
        return {
            list: data,
            count: total,
        };
    }

    async facilityListOrg(organizationId: string): Promise<any> {
        let query = {
            organizationId: organizationId,
        };
        const data = await this.FacilityModel.find(query).sort({ createdAt: -1 });
        return data;
    }
   async facilityListOrgv2(organizationId: string): Promise<any> {
    const data = await this.FacilityModel.aggregate([
        {
            $match: {
                organizationId: new Types.ObjectId(organizationId),
            },
        },
        {
            $lookup: {
                from: "cities",
                localField: "address.city",
                foreignField: "_id",
                as: "cityDetails",
            },
        },
        {
            $lookup: {
                from: "states",
                localField: "address.state",
                foreignField: "_id",
                as: "stateDetails",
            },
        },
        {
            $project: {
                _id: 1,
                name: "$facilityName",
                address: {
                    cityName: { $arrayElemAt: ["$cityDetails.name", 0] },
                    stateName: { $arrayElemAt: ["$stateDetails.name", 0] },
                    addressLine1: "$address.addressLine1",
                    addressLine2: "$address.addressLine2",
                    postalCode: "$address.postalCode",
                    _id: "$address._id",
                    state: "$address.state",
                    city: "$address.city",
                },
            },
        },
        {
            $sort: { createdAt: -1 },
        },
    ]);
    return data;
}

    async facilityListByStaff(body: FacilityDetailsByStaffDto): Promise<any> {
        if (!body.staffId) {
            throw new BadRequestException("Required staff details");
        }
        const data = await this.StaffProfileModel.aggregate([
            {
                $match: {
                    userId: new Types.ObjectId(body.staffId),
                },
            },
            {
                $lookup: {
                    from: "facilities",
                    localField: "facilityId",
                    foreignField: "_id",
                    as: "result",
                },
            },
            {
                $unwind: "$result",
            },
            {
                $lookup: {
                    from: "cities",
                    localField: "result.address.city",
                    foreignField: "_id",
                    as: "cityDetails",
                },
            },
            {
                $lookup: {
                    from: "states",
                    localField: "result.address.state",
                    foreignField: "_id",
                    as: "stateDetails",
                },
            },
            {
                $project: {
                    _id: "$result._id",
                    name: "$result.facilityName",
                    address: {
                        cityName: { $arrayElemAt: ["$cityDetails.name", 0] },
                        stateName: { $arrayElemAt: ["$stateDetails.name", 0] },
                        addressLine1: "$result.address.addressLine1",
                        addressLine2: "$result.address.addressLine2",
                        postalCode: "$result.address.postalCode",
                        _id: "$result.address._id",
                        state: "$result.address.state",
                        city: "$result.address.city",
                    },
                },
            },
        ]);
        return data;
    }

    async facilityListOrganization(facilityListDto: FacilityListDto, userId: string): Promise<any> {
        const pageSize = facilityListDto.pageSize ?? 10;
        const page = facilityListDto.page ?? 1;
        const skip = pageSize * (page - 1);
        let query = {
            organizationId: userId,
        };
        if (facilityListDto.search !== "" && facilityListDto.search) {
            const titleQueryString = facilityListDto.search?.trim().split(" ").join("|");
            query["$or"] = [{ facilityName: { $regex: `.*${titleQueryString}.*`, $options: "i" } }];
        }

        const total = await this.FacilityModel.countDocuments(query);
        const data = await this.FacilityModel.find(query).sort({ createdAt: -1 }).skip(skip).limit(pageSize);
        return {
            list: data,
            count: total,
        };
    }

    async facilityUpdateOrganization(updateFacilityDto: UpdateFacilityDto, userId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            let availabilityData = {
                organizationId: updateFacilityDto.organizationId,
                facilityId: updateFacilityDto.facilityId,
                type: AvailabilityType.AVAILABLE,
                workingHours: updateFacilityDto.workingHours,
            };
            if (!updateFacilityDto.mobile) throw new BadRequestException("Required mobile number");
            if (!updateFacilityDto.email) throw new BadRequestException("Required email");
            let updateFacility = await this.FacilityModel.findOneAndUpdate(
                {
                    organizationId: userId,
                    _id: new Types.ObjectId(updateFacilityDto.facilityId),
                },
                {
                    $set: updateFacilityDto,
                },
                {
                    new: true,
                    session,
                },
            );
            if (!updateFacility) throw new BadRequestException("Facility not found");

            let facilityAvailability = await this.FacilityAvailabilityModel.findOne({
                facilityId: availabilityData.facilityId,
                organizationId: availabilityData.organizationId,
                type: AvailabilityType.AVAILABLE,
            });
            if (facilityAvailability) {
                facilityAvailability["workingHours"] = availabilityData.workingHours;
                await facilityAvailability.save({ session });
            } else {
                let saveAvailability = new this.FacilityAvailabilityModel(availabilityData);
                await saveAvailability.save({ session });
            }
            await this.transactionService.commitTransaction(session);
            return updateFacility;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async facilityUpdateSuperAdmin(updateFacilityDto: UpdateFacilityDto): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            if (!updateFacilityDto.organizationId) throw new BadRequestException("Required organization details");
            let updateFacility = await this.FacilityModel.findOneAndUpdate(
                {
                    organizationId: updateFacilityDto.organizationId,
                    _id: updateFacilityDto.facilityId,
                },
                {
                    $set: updateFacilityDto,
                },
                {
                    new: true,
                    session,
                },
            );

            if (!updateFacility) throw new BadRequestException("Facility not found");
            await this.transactionService.commitTransaction(session);
            return updateFacility;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async facilityDetailsOrganization(facilityDetailsDto: FacilityDetailsDto, orgId: string): Promise<any> {
        let pipeline = this.facilityPipe.facilityDetailsOrg(facilityDetailsDto, orgId);
        let output = await this.FacilityModel.aggregate(pipeline);
        if (output.length == 0) throw new BadRequestException("Facility not found");
        return output[0];
    }

    async facilityDetailsSuperAdmin(facilityDetailsDto: FacilityDetailsDto): Promise<any> {
        let pipeline = this.facilityPipe.facilityDetailsSuperAdmin(facilityDetailsDto);
        let output = await this.FacilityModel.aggregate(pipeline);
        if (output.length == 0) throw new BadRequestException("Facility not found");
        return output[0];
    }

    async facilityDetailsStaff(facilityDetailsDto: FacilityDetailsDto): Promise<any> {
        let pipeline = this.facilityPipe.facilityDetailsStaff(facilityDetailsDto);
        let output = await this.FacilityModel.aggregate(pipeline);
        if (output.length == 0) throw new BadRequestException("Facility not found");
        return output[0];
    }

    async facilityListFrontDesk(facilityListDto: FacilityListDto, userId: string): Promise<any> {
        let search = "";
        if (facilityListDto.search) {
            search = facilityListDto.search.trim().split(" ").join("|");
        }

        let pipeline = this.facilityPipe.facilityListFrontDesk(facilityListDto, userId, search);
        let data = await this.StaffProfileModel.aggregate(pipeline);
        return {
            list: data[0]?.list,
            count: data[0]?.total[0]?.total ? data[0]?.total[0]?.total : 0,
        };
    }

    async facilityListWebMaster(facilityListDto: FacilityListDto, userId: string): Promise<any> {
        let search = "";
        if (facilityListDto.search) {
            search = facilityListDto.search.trim().split(" ").join("|");
        }
        let pipeline = this.facilityPipe.facilityListWebMaster(facilityListDto, userId, search);
        let data = await this.StaffProfileModel.aggregate(pipeline);
        return {
            list: data[0]?.list,
            count: data[0]?.total[0]?.total ? data[0]?.total[0]?.total : 0,
        };
    }

    async facilityListTrainer(facilityListDto: FacilityListDto, userId: string): Promise<any> {
        return this.facilityListFrontDesk(facilityListDto, userId);
        // const pageSize = facilityListDto.pageSize ?? 10;
        // const page = facilityListDto.page ?? 1;
        // const skip = pageSize * (page - 1);
        // let query = {
        //     // _id: userId,
        // };
        // if (facilityListDto.search !== "" && facilityListDto.search) {
        //     const titleQueryString = facilityListDto.search?.trim().split(" ").join("|");
        //     query["$or"] = [{ facilityName: { $regex: `.*${titleQueryString}.*`, $options: "i" } }];
        // }

        // const total = await this.FacilityModel.countDocuments(query);
        // const data = await this.FacilityModel.find(query).sort({ createdAt: -1 }).skip(skip).limit(pageSize);
        // return {
        //     list: data,
        //     count: total,
        // };
    }

    async generateDailyFacilityObjects(organizationId: string, facilityUnavailabilityDto: AddFacilityUnavailabilityDto) {
        const result = [];

        // Parse the fromDate and endDate strings into Date objects
        const startDate = new Date(facilityUnavailabilityDto.fromDate);
        const endDate = new Date(facilityUnavailabilityDto.endDate);

        // Loop through each date in the range and create an object for each day
        for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
            // Create a new Date object for fromDate and endDate for each day
            const currentDate = new Date(date);

            // Generate the daily facility object
            result.push({
                organizationId: organizationId,
                facilityId: facilityUnavailabilityDto.facility,
                type: AvailabilityType.UNAVAILABLE,
                fromDate: new Date(currentDate.setHours(0, 0, 0, 0)), // Start of the day
                endDate: new Date(currentDate.setHours(23, 59, 59, 999)), // End of the day
                time: facilityUnavailabilityDto.time,
            });
        }

        return result;
    }


    private async validateIsScheduled(facilityUnavailabilityDto: AddFacilityUnavailabilityDto) {
        const fromDate = new Date(facilityUnavailabilityDto.fromDate);
        const endDate = new Date(facilityUnavailabilityDto.endDate);
        // Step 2: Fetch schedules that intersect with the provided unavailability time range
        const schedules = await this.SchedulingModel.find({
            facilityId: facilityUnavailabilityDto.facility,
            scheduleStatus: { $ne: ScheduleStatusType.CANCELED },
            date: {
                $gte: fromDate,
                $lte: endDate,
            },
        }, { from: 1, to: 1, date: 1 });

        // Step 3: Check for schedule conflicts within the selected time ranges
        for (let schedule of schedules) {
            const { from, to } = schedule;

            // Check for conflict within the provided time range (fromDate to endDate)
            const timeConflict = facilityUnavailabilityDto.time.some(item => {
                const unavailabilityStart = item.from;
                const unavailabilityEnd = item.to;

                // Check if there is an overlap with the schedule
                return (
                    (unavailabilityStart < to && unavailabilityEnd > from)
                );
            });

            if (timeConflict) {
                throw new BadRequestException("A schedule already exists in the selected time range");
            }
        }
        return false
    }

    async addUnavailabilityOrganization(facilityUnavailabilityDto: AddFacilityUnavailabilityDto, organizationId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            const fromDate = new Date(facilityUnavailabilityDto.fromDate);
            const endDate = new Date(facilityUnavailabilityDto.endDate);
            const overlappingDocuments = await this.FacilityAvailabilityModel.find({
                facilityId: facilityUnavailabilityDto.facility,
                type: AvailabilityType.UNAVAILABLE,
                $or: [
                    // Condition 1: The existing document's fromDate is within the new range
                    {
                        fromDate: {
                            $gte: fromDate,
                            $lte: endDate,
                        },
                    },
                    // Condition 2: The existing document's endDate is within the new range
                    {
                        endDate: {
                            $gte: fromDate,
                            $lte: endDate,
                        },
                    },
                    // Condition 3: The existing document's range fully covers the new range
                    {
                        fromDate: { $lte: fromDate },
                        endDate: { $gte: endDate },
                    },
                ],
            });
            if (overlappingDocuments.length > 0) throw new BadRequestException("Unavailability already exists between the given dates ");

            // Step 2: Fetch schedules that intersect with the provided unavailability time range
            await this.validateIsScheduled(facilityUnavailabilityDto)

            let updatedPayload = await this.generateDailyFacilityObjects(organizationId, facilityUnavailabilityDto);
            let insertAll = await this.FacilityAvailabilityModel.insertMany(updatedPayload, { ordered: false, session });
            await this.transactionService.commitTransaction(session);
            return insertAll;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async addUnavailabilityWebMaster(facilityUnavailabilityDto: AddFacilityUnavailabilityDto, webMasterId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            let staffDetails = await this.StaffProfileModel.findOne({
                userId: webMasterId,
                facilityId: { $in: [facilityUnavailabilityDto.facility] },
            });
            if (!staffDetails) throw new BadRequestException("Invalid payload / Staff has not been assigned with the provided facilities");
            let checkFacility = await this.FacilityModel.findOne({ _id: facilityUnavailabilityDto.facility }, { organizationId: 1 });
            if (!checkFacility) throw new BadRequestException("Facility not found");
            const fromDate = new Date(facilityUnavailabilityDto.fromDate);
            const endDate = new Date(facilityUnavailabilityDto.endDate);
            const overlappingDocuments = await this.FacilityAvailabilityModel.find({
                facilityId: facilityUnavailabilityDto.facility,
                type: AvailabilityType.UNAVAILABLE,
                $or: [
                    // Condition 1: The existing document's fromDate is within the new range
                    {
                        fromDate: {
                            $gte: fromDate,
                            $lte: endDate,
                        },
                    },
                    // Condition 2: The existing document's endDate is within the new range
                    {
                        endDate: {
                            $gte: fromDate,
                            $lte: endDate,
                        },
                    },
                    // Condition 3: The existing document's range fully covers the new range
                    {
                        fromDate: { $lte: fromDate },
                        endDate: { $gte: endDate },
                    },
                ],
            });
            await this.validateIsScheduled(facilityUnavailabilityDto)

            if (overlappingDocuments.length > 0) throw new BadRequestException("Unavailability already exists between the given dates ");
            let updatedPayload = await this.generateDailyFacilityObjects(checkFacility["organizationId"], facilityUnavailabilityDto);
            let insertAll = await this.FacilityAvailabilityModel.insertMany(updatedPayload, { ordered: false, session });
            await this.transactionService.commitTransaction(session);
            return insertAll;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async facilityAvailabilityOrganization(facilityAvailabilityDto: FacilityAvailabilityDetailsDto, organizationId: string): Promise<any> {
        let checkValidPayload = await this.FacilityModel.findOne(
            {
                _id: facilityAvailabilityDto.facilityId,
                organizationId: organizationId,
            },
            { _id: 1 },
        );
        if (!checkValidPayload) throw new BadRequestException("Facility not found / Invalid payload");
        let availabilities = await this.FacilityAvailabilityModel.findOne({
            facilityId: facilityAvailabilityDto.facilityId,
            organizationId: organizationId,
            type: AvailabilityType.AVAILABLE,
        });

        let unAvailabilities = await this.FacilityAvailabilityModel.find({
            facilityId: facilityAvailabilityDto.facilityId,
            organizationId: organizationId,
            type: AvailabilityType.UNAVAILABLE,
            endDate: { $gte: new Date(facilityAvailabilityDto.fromDate) },
            fromDate: { $lte: new Date(facilityAvailabilityDto.endDate) },
        }).sort({ createdAt: 1 });
        return {
            availabilities: availabilities ? availabilities : [],
            unAvailabilities: unAvailabilities,
        };
    }

    async facilityAvailabilityStaff(facilityAvailabilityDto: FacilityAvailabilityDetailsDto, staffId: string): Promise<any> {
        let checkValidPayload = await this.StaffProfileModel.findOne(
            {
                userId: staffId,
                facilityId: { $in: facilityAvailabilityDto.facilityId },
            },
            { _id: 1 },
        );
        if (!checkValidPayload) throw new BadRequestException("Facility not found / Invalid payload");

        let facilityOrg = await this.FacilityModel.findOne({ _id: facilityAvailabilityDto.facilityId }, { organizationId: 1 });
        let availabilities = await this.FacilityAvailabilityModel.findOne({
            facilityId: facilityAvailabilityDto.facilityId,
            organizationId: facilityOrg["organizationId"],
            type: AvailabilityType.AVAILABLE,
        });

        let unAvailabilities = await this.FacilityAvailabilityModel.find({
            facilityId: facilityAvailabilityDto.facilityId,
            organizationId: facilityOrg["organizationId"],
            type: AvailabilityType.UNAVAILABLE,
            endDate: { $gte: new Date(facilityAvailabilityDto.fromDate) },
            fromDate: { $lte: new Date(facilityAvailabilityDto.endDate) },
        }).sort({ createdAt: 1 });

        return {
            availabilities: availabilities,
            unAvailabilities: unAvailabilities,
        };
    }

    async updateUnavailabilityOrganization(facilityUnavailabilityDto: UpdateFacilityUnavailabilityDto, organizationId: string, unavailabilityId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            let updateTime = await this.FacilityAvailabilityModel.findOneAndUpdate(
                {
                    _id: unavailabilityId,
                    organizationId: organizationId,
                    facilityId: facilityUnavailabilityDto.facility,
                },
                {
                    $set: {
                        time: facilityUnavailabilityDto.time,
                    },
                },
                {
                    new: true,
                    session,
                },
            );
            if (!updateTime) throw new BadRequestException("Invalid payload / Details not found");
            await this.transactionService.commitTransaction(session);
            return updateTime;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async updateUnavailabilityWebMaster(facilityUnavailabilityDto: UpdateFacilityUnavailabilityDto, staffId: string, unavailabilityId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            let staffDetails = await this.StaffProfileModel.findOne({
                userId: staffId,
                facilityId: { $in: [facilityUnavailabilityDto.facility] },
            });
            if (!staffDetails) throw new BadRequestException("Invalid payload / Staff has not been assigned with the provided facilities");
            let checkFacility = await this.FacilityModel.findOne({ _id: facilityUnavailabilityDto.facility }, { organizationId: 1 });
            if (!checkFacility) throw new BadRequestException("Facility not found");
            let updateTime = await this.FacilityAvailabilityModel.findOneAndUpdate(
                {
                    _id: unavailabilityId,
                    organizationId: checkFacility["organizationId"],
                    facilityId: facilityUnavailabilityDto.facility,
                },
                {
                    $set: {
                        time: facilityUnavailabilityDto.time,
                    },
                },
                {
                    session,
                },
            );
            if (!updateTime) throw new BadRequestException("Invalid payload / Details not found");
            await this.transactionService.commitTransaction(session);
            return updateTime;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async deleteUnavailabilityOrganization(deleteUnavailabilityDto: DeleteFacilityUnavailabilityDto, organizationId: string, unavailabilityId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            let updateTime = await this.FacilityAvailabilityModel.deleteOne(
                {
                    _id: unavailabilityId,
                    organizationId: organizationId,
                    facilityId: deleteUnavailabilityDto.facility,
                },
                {
                    session,
                },
            );
            if (updateTime.deletedCount == 0) throw new BadRequestException("Invalid payload / Details not found");
            await this.transactionService.commitTransaction(session);
            return updateTime;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async deleteUnavailabilityWebMaster(deleteUnavailabilityDto: DeleteFacilityUnavailabilityDto, staffId: string, unavailabilityId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            let staffDetails = await this.StaffProfileModel.findOne({
                userId: staffId,
                facilityId: { $in: [deleteUnavailabilityDto.facility] },
            });
            if (!staffDetails) throw new BadRequestException("Invalid payload / Staff has not been assigned with the provided facilities");
            let checkFacility = await this.FacilityModel.findOne({ _id: deleteUnavailabilityDto.facility }, { organizationId: 1 });
            if (!checkFacility) throw new BadRequestException("Facility not found");
            let updateTime = await this.FacilityAvailabilityModel.deleteOne(
                {
                    _id: unavailabilityId,
                    organizationId: checkFacility["organizationId"],
                    facilityId: deleteUnavailabilityDto.facility,
                },
                {
                    session,
                },
            );
            if (updateTime.deletedCount == 0) throw new BadRequestException("Invalid payload / Details not found");
            await this.transactionService.commitTransaction(session);
            return updateTime;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async bulkUpdateUnavailabilityOrganization(bulkUpdateUnavailabilityDto: BulkUpdateFacilityUnavailabilityDto, organizationId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            for (let i = 0; i < bulkUpdateUnavailabilityDto.unAvailabilities.length; i++) {
                let unavailabilityId = bulkUpdateUnavailabilityDto.unAvailabilities[i].unAvailabilityId;
                let updateTime = await this.FacilityAvailabilityModel.findOneAndUpdate(
                    {
                        _id: unavailabilityId,
                        organizationId: organizationId,
                        type: AvailabilityType.UNAVAILABLE,
                        facilityId: bulkUpdateUnavailabilityDto.facility,
                    },
                    {
                        $set: {
                            time: bulkUpdateUnavailabilityDto.unAvailabilities[i].time,
                        },
                    },
                    {
                        new: true,
                        session,
                    },
                );
                if (!updateTime) throw new BadRequestException("Invalid payload / Details not found");
            }
            await this.transactionService.commitTransaction(session);
            return "updateTime";
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async bulkUpdateUnavailabilityWebMaster(bulkUpdateUnavailabilityDto: BulkUpdateFacilityUnavailabilityDto, staffId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            let staffDetails = await this.StaffProfileModel.findOne({
                userId: staffId,
                facilityId: { $in: [bulkUpdateUnavailabilityDto.facility] },
            });
            if (!staffDetails) throw new BadRequestException("Invalid payload / Staff has not been assigned with the provided facilities");
            let checkFacility = await this.FacilityModel.findOne({ _id: bulkUpdateUnavailabilityDto.facility }, { organizationId: 1 });
            if (!checkFacility) throw new BadRequestException("Facility not found");

            for (let i = 0; i < bulkUpdateUnavailabilityDto.unAvailabilities.length; i++) {
                let unavailabilityId = bulkUpdateUnavailabilityDto.unAvailabilities[i].unAvailabilityId;
                let updateTime = await this.FacilityAvailabilityModel.findOneAndUpdate(
                    {
                        _id: unavailabilityId,
                        organizationId: checkFacility["organizationId"],
                        facilityId: bulkUpdateUnavailabilityDto.facility,
                    },
                    {
                        $set: {
                            time: bulkUpdateUnavailabilityDto.unAvailabilities[i].time,
                        },
                    },
                    {
                        session,
                    },
                );
                if (!updateTime) throw new BadRequestException("Invalid payload / Details not found");
            }
            await this.transactionService.commitTransaction(session);
            return "updateTime";
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async facilityStatusUpdate(facilityId: string, changeStatusDto: ChangeStatusDto): Promise<any> {
        const facility = await this.FacilityModel.findById(facilityId);

        if (!facility) {
            throw new NotFoundException(`facility with ID ${facilityId} not found`);
        }

        if (facility.isActive === changeStatusDto.status) {
            throw new BadRequestException(
                `Facility is already ${changeStatusDto.status ? 'Active' : 'Inactive'}`
            );
        }

        facility["isActive"] = changeStatusDto.status;
        await facility.save();
        return {
            message: `Facility status updated to ${changeStatusDto.status ? 'Active' : 'Inactive'}`,
            facility,
        };
    }
    async facilityStoreStatusUpdate(facilityId: string, changeStatusDto: ChangeStatusDto): Promise<any> {
        const facility = await this.FacilityModel.findById(facilityId);

        if (!facility) {
            throw new NotFoundException(`facility with ID ${facilityId} not found`);
        }
        if (!facility.isActive) {
            throw new BadRequestException(
                `Facility is disabled. Store status cannot be changed.`
            );
        }
        // if (facility.isStoreActive === changeStatusDto.status) {
        //         throw new BadRequestException(
        //             `Facility Store is already ${changeStatusDto.status ? 'Active' : 'Inactive'}`
        //         );
        // }

        facility["isStoreActive"] = changeStatusDto.status;
        await facility.save();
        return {
            message: `Facility  Store status updated to ${changeStatusDto.status ? 'Active' : 'Inactive'}`,
            facility,
        };
    }

    async normalizeFacility(facility: FacilityDocument): Promise<Facility> {
        return JSON.parse(JSON.stringify(facility));
    }

    async denormalizeFacility(facility: any): Promise<FacilityDocument> {
        return new this.FacilityModel(facility);
    }

    async getUserFacilities(userId: IDatabaseObjectId, roleType: ENUM_ROLE_TYPE): Promise<Facility[]> {
        // Create a cache key based on userId and roleType
        const cacheKey = `user-facilities:${userId}`;

        let facilities: FacilityDocument[] = [];

        // For organization users, get all facilities under their organization
        if (roleType === ENUM_ROLE_TYPE.ORGANIZATION) {
            facilities = await this.FacilityModel.find({ organizationId: userId });
        }

        // For staff users (FRONT_DESK_ADMIN, TRAINER, etc.), get their assigned facilities
        if (roleType === ENUM_ROLE_TYPE.FRONT_DESK_ADMIN ||
            roleType === ENUM_ROLE_TYPE.TRAINER ||
            roleType === ENUM_ROLE_TYPE.WEB_MASTER) {

            const staffDetails = await this.StaffProfileModel.findOne(
                { userId: userId },
                { facilityId: 1 },
                { lean: true }
            );

            if (!staffDetails || !staffDetails.facilityId.length) {
                return [];
            }

            facilities = await this.FacilityModel.find({
                _id: { $in: staffDetails.facilityId },
            });
        }

        if (roleType === ENUM_ROLE_TYPE.USER) {
            const clientDetails = await this.ClientModel.findOne(
                { userId: userId },
                { facilityId: 1 }
            );

            if (clientDetails && clientDetails.facilityId.length) {
                facilities = await this.FacilityModel.find({
                    _id: { $in: clientDetails.facilityId },
                });
            }
        }

        return facilities;
    }
}
