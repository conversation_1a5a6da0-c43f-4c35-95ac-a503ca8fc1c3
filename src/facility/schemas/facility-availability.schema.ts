import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, SchemaTypes } from "mongoose";
import { AvailabilityType } from "../enums/availability-type.enum";

export type FacilityAvailabilityDocument = HydratedDocument<FacilityAvailability>;

@Schema({ timestamps: true })
export class FacilityAvailability {
    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    organizationId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "facility" })
    facilityId: string;

    @Prop({ enum: AvailabilityType, default: AvailabilityType.AVAILABLE })
    type: AvailabilityType;

    @Prop({
        type: {
            mon: {
                type: [
                    {
                        from: String,
                        to: String,
                    },
                ],
                default: [],
            },
            tue: {
                type: [
                    {
                        from: String,
                        to: String,
                    },
                ],
                default: [],
            },
            wed: {
                type: [
                    {
                        from: String,
                        to: String,
                    },
                ],
                default: [],
            },
            thu: {
                type: [
                    {
                        from: String,
                        to: String,
                    },
                ],
                default: [],
            },
            fri: {
                type: [
                    {
                        from: String,
                        to: String,
                    },
                ],
                default: [],
            },
            sat: {
                type: [
                    {
                        from: String,
                        to: String,
                    },
                ],
                default: [],
            },
            sun: {
                type: [
                    {
                        from: String,
                        to: String,
                    },
                ],
                default: [],
            },
        },
    })
    workingHours: {
        mon: { from: String; to: String }[];
        tue: { from: String; to: String }[];
        wed: { from: String; to: String }[];
        thu: { from: String; to: String }[];
        fri: { from: String; to: String }[];
        sat: { from: String; to: String }[];
        sun: { from: String; to: String }[];
    };

    @Prop({ type: Date, unique: true, sparse: true })
    fromDate: Date;

    @Prop({ type: Date, unique: true, sparse: true })
    endDate: Date;

    @Prop({
        type: [
            {
                from: String,
                to: String,
            },
        ],
    })
    time: { from: String; to: String }[];

    @Prop()
    createdAt: Date;

    @Prop()
    updatedAt: Date;
}

export const FacilityAvailabilitySchema = SchemaFactory.createForClass(FacilityAvailability);
