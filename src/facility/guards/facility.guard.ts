import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { FacilityService } from '../services/facility.service';
import { IRequestApp } from 'src/common/request/interfaces/request.interface';

@Injectable()
export class UserFacilityGuard implements CanActivate {
  constructor(private readonly facilityService: FacilityService) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<IRequestApp>();
    const user = request.__user;

    if (!user) {
      return false;
    }

    // Get user facilities and attach to request
    const facilities = await this.facilityService.getUserFacilities(user._id, user.role.type);
    request.__facilities = facilities;
    return true;
  }
}