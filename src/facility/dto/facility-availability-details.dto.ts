import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsDate, IsMongoId, IsNotEmpty } from "class-validator";

export class FacilityAvailabilityDetailsDto {
    @ApiProperty({
        description: "Start date from when the availability is required",
        example: "2024-10-01T00:00:00.000+00:00",
        required: true,
    })
    @IsDate({ message: "Required valid start date" })
    @Type(() => Date)
    @IsNotEmpty({ message: "Invalid start date" })
    fromDate: Date;

    @ApiProperty({
        description: "End date from when the availability is required",
        example: "2024-10-01T00:00:00.000+00:00",
        required: true,
    })
    @IsDate({ message: "Required valid end date" })
    @Type(() => Date)
    @IsNotEmpty({ message: "Invalid start date" })
    endDate: Date;

    @ApiProperty({
        description: "The facility id of the organization.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsMongoId({ message: "Required valid facility id" })
    facilityId: string;
}
