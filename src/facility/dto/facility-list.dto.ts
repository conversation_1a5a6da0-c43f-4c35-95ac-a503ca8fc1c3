import { ApiProperty } from "@nestjs/swagger";
import { IsMongoId, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { PaginationDTO } from "src/users/dto/pagination.dto";

export class FacilityListDto extends PaginationDTO {
    @ApiProperty({
        description: "Search must be a string || Search is optional",
        example: "Knox",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Search must be a string" })
    search: string;

    @ApiProperty({
        description: "The User ID of the organization.",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Organization details is Invalid" })
    organizationId: string;
}
