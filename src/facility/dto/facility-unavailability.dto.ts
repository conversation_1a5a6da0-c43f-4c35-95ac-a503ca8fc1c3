import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { ArrayMinSize, IsArray, IsDate, IsEnum, IsMongoId, IsNotEmpty, IsString, Matches } from "class-validator";
import { AvailabilityType } from "../enums/availability-type.enum";

export class TimeSlots {
    @ApiProperty({
        description: "Start time for availability on specified days (HH:mm)",
        example: "08:00",
    })
    @IsString({ message: "From time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "From time must be in the format HH:mm" })
    from: string;

    @ApiProperty({
        description: "End time for availability on specified days (HH:mm)",
        example: "17:00",
    })
    @IsString({ message: "To time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "To time must be in the format HH:mm" })
    to: string;
}

export class FacilityUnavailabilityDto {
    @ApiProperty({
        description: "Availability type for facility",
        type: AvailabilityType.UNAVAILABLE,
        required: true,
    })
    @IsEnum(AvailabilityType, { message: "Invalid availability type" })
    type: AvailabilityType;

    @ApiProperty({
        description: "Selected facility id",
        example: ["dbhfkjew83425293", "bgf78rywrubnvd0932ri"],
        required: true,
    })
    @IsArray({ message: "Please select facilities" })
    @IsMongoId({
        each: true,
        message: "Invalid facility details",
    })
    @ArrayMinSize(1, { message: "Please select at least 1 facility" })
    facility: Array<String>;

    @ApiProperty({
        description: "Start date from when the unavailability is getting marked",
        example: "2024-10-01T00:00:00.000+00:00",
        required: true,
    })
    @IsDate({ message: "Required valid start date" })
    @Type(() => Date)
    @IsNotEmpty({ message: "Invalid start date" })
    fromDate: Date;

    @ApiProperty({
        description: "End date from when the unavailability is getting marked",
        example: "2024-10-01T00:00:00.000+00:00",
        required: true,
    })
    @IsDate({ message: "Required valid end date" })
    @Type(() => Date)
    @IsNotEmpty({ message: "Invalid start date" })
    endDate: Date;

    @ApiProperty({
        description: "Days of the week the facility is unavailable",
        type: TimeSlots,
        required: true,
    })
    @IsArray({ message: "Time slots needs to be in correct format" })
    time: TimeSlots[];
}
