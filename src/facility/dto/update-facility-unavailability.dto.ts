import { ApiProperty } from "@nestjs/swagger";
import { ArrayMinSize, IsArray, IsEnum, IsMongoId, IsString, Matches } from "class-validator";
import { AvailabilityType } from "../enums/availability-type.enum";

export class TimeSlots {
    @ApiProperty({
        description: "Start time for availability on specified days (HH:mm)",
        example: "08:00",
    })
    @IsString({ message: "From time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "From time must be in the format HH:mm" })
    from: string;

    @ApiProperty({
        description: "End time for availability on specified days (HH:mm)",
        example: "17:00",
    })
    @IsString({ message: "To time must be a string" })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "To time must be in the format HH:mm" })
    to: string;
}

export class UpdateFacilityUnavailabilityDto {
    @ApiProperty({
        description: "Availability type for facility",
        type: AvailabilityType.UNAVAILABLE,
        required: true,
    })
    @IsEnum(AvailabilityType, { message: "Invalid availability type" })
    type: AvailabilityType;

    @ApiProperty({
        description: "Selected facility id",
        example: "6731ba42217eb177339f2356",
        required: true,
    })
    @IsMongoId({ message: "Invalid facility details" })
    facility: string;

    @ApiProperty({
        description: "Days of the week the facility is unavailable",
        type: TimeSlots,
        required: true,
    })
    @IsArray({ message: "Time slots needs to be in correct format" })
    @ArrayMinSize(1, { message: "Please add time slots" })
    time: TimeSlots;
}
