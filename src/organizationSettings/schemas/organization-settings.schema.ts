import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { IsBoolean, IsDate, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { Document, SchemaTypes } from 'mongoose';

@Schema({ timestamps: true })
export class OrganizationSettings extends Document{

  @Prop({ required: false, index: true, type: Number, default: 0 })
  @IsNumber()
  order: number;

  @Prop({ required: true, index: true, type: SchemaTypes.ObjectId, ref: 'Organization' })
  organizationId: string;

  @Prop({ required: true, index: true, type: SchemaTypes.ObjectId, ref: 'SettingsOptions' })
  settingOptionId: string;

  @Prop({ required: true, index: true, type: String })
  @IsString()
  @IsNotEmpty()
  key: string;

  @Prop({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  name: string;

  @Prop({ type: String, default: null })
  @IsString()
  @IsOptional()
  description?: string;

  @Prop({ type: Boolean, default: false })
  @IsBoolean()
  isActive: boolean; // Set default value for organization

  @Prop({ type: Boolean, default: false })
  @IsBoolean()
  isEnabled: boolean; // Is hks providing service of it,

  @Prop({ type: String, default: "", required: false })
  href?: "";

  @Prop({ type: Date, required: false, default: undefined })
  deletedAt: Date;

}


export const OrganizationSettingsSchema = SchemaFactory.createForClass(OrganizationSettings);

OrganizationSettingsSchema.index({organizationId: 1, settingOptionId: 1}, {unique: true})