import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { OrganizationSettings, } from '../schemas/organization-settings.schema';
import { FilterQuery, Model, PipelineStage, QueryOptions, Types } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { OrganizationSubSettings } from '../schemas/organization-sub-settings.schema';
import { SettingsOptions } from 'src/settingsOptions/schemas/setting-options.schema';
import { skip } from 'rxjs';
import { GrantOneSettingOptionsDto } from '../dto/grant-setting-options.dto';
import { CachingService } from 'src/common/caching/services/caching.service';
import { IDatabaseObjectId } from 'src/common/database/interfaces/database.objectid.interface';
import { CACHE_DEFAULT_TTL } from 'src/common/caching/constants/caching.constants';

@Injectable()
export class OrganizationSettingService {

    private organizationSettingCacheKey = `orgSetting:isActive`;

    constructor(
        @InjectModel(OrganizationSettings.name) private readonly OrganizationSettingModel: Model<OrganizationSettings>,
        @InjectModel(OrganizationSubSettings.name) private readonly OrganizationSubSettingModel: Model<OrganizationSubSettings>,
        @InjectModel(SettingsOptions.name) private readonly SettingOptionsModel: Model<SettingsOptions>,
        private readonly cachingService: CachingService
    ) {

    }

    async findOne(filter: FilterQuery<OrganizationSettings & { organizationId: string | Types.ObjectId }> = {}, options?: QueryOptions<OrganizationSettings>): Promise<any> {

        return await this.OrganizationSettingModel.findOne(filter, options);
    }

    findAndFilterByKey(data, targetKey) {
        // Clone the data to avoid modifying the original
        const clonedData = JSON.parse(JSON.stringify(data));

        // Track if we've found the target
        let found = false;

        // Backtracking function to search and filter the hierarchy
        function backtrack(item) {
            // Check if current item matches the target key
            if (item.key === targetKey) {
                found = true;
                return { item, inPath: true };
            }

            // If no subSettings, this path doesn't lead to the target
            if (!item.subSettings || item.subSettings.length === 0) {
                return { item: null, inPath: false };
            }

            // Search through subSettings and filter
            const filteredSubSettings = [];

            for (const subItem of item.subSettings) {
                const { item: resultItem, inPath } = backtrack(subItem);

                if (inPath && resultItem) {
                    filteredSubSettings.push(resultItem);
                }
            }

            // If we found matching items in the subtree, return this item with filtered subSettings
            if (filteredSubSettings.length > 0) {
                item.subSettings = filteredSubSettings;
                return { item, inPath: true };
            }

            // No match in this subtree
            return { item: null, inPath: false };
        }

        // Start the backtracking search from the root
        const { item: result, inPath } = backtrack(clonedData);

        return {
            result,
            found
        };
    }

    // Wrapper function with simplified API
    getFilteredHierarchyByKey(data, key) {
        const { result, found } = this.findAndFilterByKey(data, key);

        if (!found) {
            return {
                error: `Item with key "${key}" not found`
            };
        }

        return result;
    }

    /**
     * This utility function flattens hierarchical settings data to be stored in the
     * OrganizationSettings and OrganizationSubSettings collections.
     * 
     * @param settingsData The hierarchical settings data from the DTO
     * @param organizationId The ID of the organization
     * @returns An object containing entries for both collections
     */
    async flattenSettingsData(
        settingsData: any,
        organizationId: string
    ) {
        // Result objects to store flattened data
        const organizationSetting: any[] = [];
        const organizationSubSetting: any[] = [];

        // Process root level settings
        const rootSetting = {
            order: settingsData.order || 0,
            organizationId,
            settingOptionId: settingsData._id, // Assuming the settings definition has an _id
            key: settingsData.key,
            name: settingsData.name,
            description: settingsData.description || null,
            isActive: settingsData.default || false,
            isEnabled: settingsData.inOperation || false,
            href: settingsData.href || '',
            deletedAt: undefined
        };

        const setting = new this.OrganizationSettingModel(rootSetting)
        organizationSetting.push(setting);

        // Helper function to process sub-settings recursively
        function processSubSettings(
            subSettings: any[],
            settingId: string,
            OrganizationSubSettingModel: Model<OrganizationSubSettings>,
            parentId: string | null = null,
            groupKey: string | "" = ""
        ) {
            if (!subSettings || !Array.isArray(subSettings) || subSettings.length === 0) {
                return;
            }

            for (const subSetting of subSettings) {
                // Create entry for OrganizationSubSettings
                const subSettingEntry = {
                    order: subSetting.order || 0,
                    organizationId,
                    subSettingOptionId: subSetting._id,
                    settingId,
                    parentId,
                    key: subSetting.key,
                    groupKey: groupKey,
                    isGroup: Array.isArray(subSetting.subSettings) && subSetting.subSettings.length > 0,
                    name: subSetting.name,
                    description: subSetting.description || null,
                    isActive: subSetting.isActive !== undefined ? subSetting.isActive : false,
                    href: settingsData.href || '',
                    deletedAt: undefined
                };

                // Add to result array
                const subSettingDoc = new OrganizationSubSettingModel(subSettingEntry)
                organizationSubSetting.push(subSettingDoc);

                // Process children if they exist
                if (Array.isArray(subSetting.subSettings) && subSetting.subSettings.length > 0) {
                    // We'll use the MongoDB ObjectId of the current sub-setting entry when it's inserted
                    // For this example, we'll simulate this with a placeholder ID
                    const newSubSettingId = `${subSettingEntry.key}_id`; // This will be replaced with actual _id

                    processSubSettings(
                        subSetting.subSettings,
                        settingId,
                        OrganizationSubSettingModel,
                        subSettingDoc._id,
                        subSettingDoc.key
                    );
                }
            }
        }

        // Start processing sub-settings with the root setting id
        if (settingsData.subSettings && settingsData.subSettings.length > 0) {
            // We'll use the MongoDB ObjectId of the organization setting when it's inserted
            const rootSettingId = `${rootSetting.key}_id`; // This will be replaced with actual _id

            processSubSettings(
                settingsData.subSettings,
                setting._id,
                this.OrganizationSubSettingModel
            );
        }

        return {
            organizationSetting: organizationSetting,
            organizationSubSetting: organizationSubSetting
        };
    }

    getActiveSubSettings(settings: any[]): { status: boolean, settingKey: string }[] {
        let activeSettings = [];

        function traverse(settingsArray) {
            for (const setting of settingsArray) {
                if (setting.isActive) {
                    activeSettings.push({ status: true, settingKey: setting.key });
                }
                if (setting.subSettings && setting.subSettings.length > 0) {
                    traverse(setting.subSettings);
                }
            }
        }

        traverse(settings);
        return activeSettings;
    }

    async processSubSettings(
        organizationId,
        settingId,
        originalSubSettings,
        userSubSettings,
        parentId = null,
        parentGroupKey = null,
        session
    ) {
        // Process each original subsetting
        for (const subSetting of originalSubSettings) {
            // Find or create the organization subsetting
            let organizationSubSetting = await this.OrganizationSubSettingModel.findOne({
                organizationId,
                key: subSetting.key
            }).session(session);

            if (!organizationSubSetting) {
                // Find if the user has provided a status for this subsetting
                const userSetting = userSubSettings.find(s => s.settingKey === subSetting.key);
                const isEnabled = userSetting ? userSetting.status : subSetting.default || false;

                // Create new subsetting
                organizationSubSetting = new this.OrganizationSubSettingModel({
                    organizationId,
                    settingId,
                    key: subSetting.key,
                    name: subSetting.name,
                    description: subSetting.description,
                    isActive: subSetting.default || false,
                    isEnabled: !!isEnabled,
                    href: subSetting.href || '',
                    order: subSetting.order || 0,
                    parentId,
                    groupKey: subSetting.groupKey || parentGroupKey,
                    isGroup: !!subSetting?.subSetting?.length
                });
                await organizationSubSetting.save({ session });
            } else {
                // Find if the user has provided a status for this subsetting
                const userSetting = userSubSettings.find(s => s.settingKey === subSetting.key);
                const isEnabled = userSetting ? userSetting.status : organizationSubSetting.isEnabled;

                // Update existing subsetting
                organizationSubSetting.isEnabled = !!isEnabled;
                organizationSubSetting.isActive = !!isEnabled;
                organizationSubSetting.parentId = parentId;
                organizationSubSetting.groupKey = subSetting.groupKey || parentGroupKey;
                await organizationSubSetting.save({ session });
            }

            // Recursively process child subsettings if any
            if (subSetting.subSettings && subSetting.subSettings.length > 0) {
                await this.processSubSettings(
                    organizationId,
                    settingId,
                    subSetting.subSettings,
                    userSubSettings,
                    organizationSubSetting._id,  // This subsetting becomes the parent
                    subSetting.isGroup ? subSetting.key : parentGroupKey,  // If it's a group, use its key as groupKey
                    session
                );
            }
        }
    }

    // async grantOneSettingsV0(bodyArray: GrantOneSettingOptionsDto[], organizationId: string) {
    //     const oid = new Types.ObjectId(organizationId);
    //     for (const body of bodyArray) {
    //         // Start a session for the transaction
    //         const session = await this.SettingOptionsModel.startSession();
    //         try {
    //             session.startTransaction();

    //             // Find the main setting option
    //             const settingOptionDoc = await this.SettingOptionsModel.findOne({
    //                 key: body.settingKey.settingKey
    //             }).session(session);

    //             if (!settingOptionDoc) {
    //                 throw new BadRequestException({ message: "Setting not found" });
    //             }

    //             // Find or create the organization setting
    //             let organizationSetting = await this.OrganizationSettingModel.findOne({
    //                 organizationId: oid,
    //                 key: body.settingKey.settingKey
    //             }).session(session);

    //             if (!organizationSetting) {
    //                 organizationSetting = new this.OrganizationSettingModel({
    //                     organizationId: oid,
    //                     settingOptionId: settingOptionDoc._id,
    //                     key: settingOptionDoc.key,
    //                     name: settingOptionDoc.name,
    //                     description: settingOptionDoc.description,
    //                     isActive: settingOptionDoc.default,
    //                     isEnabled: body.settingKey.status,
    //                     href: settingOptionDoc.href,
    //                     order: settingOptionDoc.order || 0,
    //                     subSettings: []
    //                 });
    //                 await organizationSetting.save({ session });
    //             } else {
    //                 // Update existing organization setting
    //                 organizationSetting.isEnabled = body.settingKey.status;
    //                 await organizationSetting.save({ session });
    //             }

    //             const defaultSubletting = this.getActiveSubSettings(settingOptionDoc.subSettings)

    //             // Process subletting recursively
    //             await this.processSubSettings(
    //                 oid,
    //                 organizationSetting._id,
    //                 settingOptionDoc.subSettings || [],
    //                 body.subSettingsKey || defaultSubletting,
    //                 null,
    //                 null,
    //                 session
    //             );

    //             await session.commitTransaction();
    //             return { success: true };
    //         } catch (error) {
    //             await session.abortTransaction();
    //             throw error;
    //         } finally {
    //             session.endSession();
    //         }
    //     }
    // }

    async grantSettings(bodyArray: GrantOneSettingOptionsDto[], organizationId: string) {
        const oid = new Types.ObjectId(organizationId);
        const session = await this.SettingOptionsModel.startSession();
        session.startTransaction();
        try {
            for (const body of bodyArray) {
                // Start a session for the transaction

                // Find the main setting option
                const settingOptionDoc = await this.SettingOptionsModel.findOne({
                    key: body.settingKey.settingKey
                }).session(session);

                if (!settingOptionDoc) {
                    throw new BadRequestException({ message: "Setting not found" });
                }

                // Find or create the organization setting
                let organizationSetting = await this.OrganizationSettingModel.findOne({
                    organizationId: oid,
                    key: body.settingKey.settingKey
                }).session(session);

                if (!organizationSetting) {
                    organizationSetting = new this.OrganizationSettingModel({
                        organizationId: oid,
                        settingOptionId: settingOptionDoc._id,
                        key: settingOptionDoc.key,
                        name: settingOptionDoc.name,
                        description: settingOptionDoc.description,
                        isActive: !!body.settingKey?.status,// settingOptionDoc.default,
                        isEnabled: !!body.settingKey?.status,
                        href: settingOptionDoc.href,
                        order: settingOptionDoc.order || 0,
                        subSettings: []
                    });
                    await organizationSetting.save({ session });
                } else {
                    // Update existing organization setting
                    organizationSetting.isEnabled = !!body.settingKey?.status;
                    organizationSetting.isActive = !!body.settingKey?.status;
                    await organizationSetting.save({ session });
                }

                const defaultSubletting = this.getActiveSubSettings(settingOptionDoc.subSettings)

                // Process subletting recursively
                await this.processSubSettings(
                    oid,
                    organizationSetting._id,
                    settingOptionDoc.subSettings || [],
                    body.subSettingsKey || defaultSubletting,
                    null,
                    null,
                    session
                );

            }
            await session.commitTransaction();
            return { success: true };
        } catch (error) {
            console.log(error)
            await session.abortTransaction();
            throw new InternalServerErrorException("Failed to grant settings");
        } finally {
            session.endSession();
        }
    }

    async findSettings(filter: FilterQuery<OrganizationSettings & { organizationId: string | Types.ObjectId }> = {}, options?: QueryOptions<OrganizationSettings>): Promise<{ data: any[], count: number }> {

        for (let item in filter) {
            if (Types.ObjectId.isValid(filter[item])) {
                filter[item] = new Types.ObjectId(filter[item])
            }
        }

        // filter['isEnabled'] = true

        const pipeline: PipelineStage[] = [
            {
                $match: filter
            },
            {
                $lookup: {
                    from: OrganizationSubSettings.name.toLowerCase(),
                    localField: "_id",
                    foreignField: "settingId",
                    as: "subSettings"
                }
            },
            {
                $unwind: {
                    path: "$subSettings",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $graphLookup: {
                    from: OrganizationSubSettings.name.toLowerCase(),
                    startWith: "$subSettings._id",
                    connectFromField: "_id",
                    connectToField: "parentId",
                    as: "subSettingsHierarchy"
                }
            },
            {
                $group: {
                    _id: "$_id",
                    order: { $first: "$order" },
                    organizationId: { $first: "$organizationId" },
                    settingOptionId: { $first: "$settingOptionId" },
                    key: { $first: "$key" },
                    name: { $first: "$name" },
                    description: { $first: "$description" },
                    isActive: { $first: "$isActive" },
                    isEnabled: { $first: "$isEnabled" },
                    href: { $first: "$href" },
                    subSettings: {
                        $push: {
                            _id: "$subSettings._id",
                            key: "$subSettings.key",
                            groupKey: "$subSettings.groupKey",
                            isGroup: "$subSettings.isGroup",
                            name: "$subSettings.name",
                            description: "$subSettings.description",
                            isActive: "$subSettings.isActive",
                            parentId: "$subSettings.parentId",
                            href: "$subSettings.href",
                            isEnabled: "$subSettings.isEnabled",
                            subSettings: "$subSettingsHierarchy"
                        }
                    }
                }
            },
            {
                $addFields: {
                    subSettings: {
                        $reduce: {
                            input: "$subSettings",
                            initialValue: [],
                            in: {
                                $concatArrays: [
                                    "$$value",
                                    {
                                        $cond: {
                                            if: { $eq: ["$$this.parentId", null] },
                                            then: [
                                                {
                                                    _id: "$$this._id",
                                                    key: "$$this.key",
                                                    groupKey: "$$this.groupKey",
                                                    isGroup: "$$this.isGroup",
                                                    name: "$$this.name",
                                                    description: "$$this.description",
                                                    isActive: "$$this.isActive",
                                                    href: "$$this.href",
                                                    isEnabled: "$$this.isEnabled",
                                                    subSettings: {
                                                        $filter: {
                                                            input: "$subSettings",
                                                            as: "child",
                                                            cond: { $eq: ["$$child.parentId", "$$this._id"] }
                                                        }
                                                    }
                                                }
                                            ],
                                            else: []
                                        }
                                    }
                                ]
                            }
                        }
                    }
                }
            },
            {
                $facet: {
                    count: [{ $count: "total" }],
                    data: [
                        {
                            $sort: {
                                order: 1
                            }
                        },
                        { $skip: 0 }, // Change this for pagination (e.g., 10 * (pageNumber - 1))
                        { $limit: 10 }, // Change this for the number of results per page
                        {
                            $project: {
                                _id: 1,
                                order: 1,
                                organizationId: 1,
                                settingOptionId: 1,
                                key: 1,
                                name: 1,
                                description: 1,
                                isActive: 1,
                                isEnabled: 1,
                                href: 1,
                                subSettings: 1
                            }
                        }
                    ]
                }
            },
            {
                $project: {
                    count: { $arrayElemAt: ["$count.total", 0] },
                    data: 1
                }
            }
        ]
        const result = await this.OrganizationSettingModel.aggregate(pipeline);

        return {
            count: result.length && result[0]?.count || 0,
            data: result.length && result[0]?.data || []
        }

    }

    async findOneSetting(filter: FilterQuery<OrganizationSettings & { organizationId: string | Types.ObjectId }> = {}, options?: QueryOptions<OrganizationSettings>): Promise<any> {

        for (let item in filter) {
            if (Types.ObjectId.isValid(filter[item])) {
                filter[item] = new Types.ObjectId(filter[item])
            }
        }

        filter['isEnabled'] = true

        const pipeline: PipelineStage[] = [
            {
                $match: filter
            },
            {
                $lookup: {
                    from: OrganizationSubSettings.name.toLowerCase(),
                    localField: "_id",
                    foreignField: "settingId",
                    as: "subSettings"
                }
            },
            {
                $unwind: {
                    path: "$subSettings",
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $graphLookup: {
                    from: OrganizationSubSettings.name.toLowerCase(),
                    startWith: "$subSettings._id",
                    connectFromField: "_id",
                    connectToField: "parentId",
                    as: "subSettingsHierarchy"
                }
            },
            {
                $group: {
                    _id: "$_id",
                    order: { $first: "$order" },
                    organizationId: { $first: "$organizationId" },
                    settingOptionId: { $first: "$settingOptionId" },
                    key: { $first: "$key" },
                    name: { $first: "$name" },
                    description: { $first: "$description" },
                    isActive: { $first: "$isActive" },
                    isEnabled: { $first: "$isEnabled" },
                    subSettings: {
                        $push: {
                            _id: "$subSettings._id",
                            key: "$subSettings.key",
                            groupKey: "$subSettings.groupKey",
                            isGroup: "$subSettings.isGroup",
                            name: "$subSettings.name",
                            description: "$subSettings.description",
                            isActive: "$subSettings.isActive",
                            parentId: "$subSettings.parentId",
                            subSettings: "$subSettingsHierarchy"
                        }
                    }
                }
            },
            {
                $addFields: {
                    subSettings: {
                        $reduce: {
                            input: "$subSettings",
                            initialValue: [],
                            in: {
                                $concatArrays: [
                                    "$$value",
                                    {
                                        $cond: {
                                            if: { $eq: ["$$this.parentId", null] },
                                            then: [
                                                {
                                                    _id: "$$this._id",
                                                    key: "$$this.key",
                                                    groupKey: "$$this.groupKey",
                                                    isGroup: "$$this.isGroup",
                                                    name: "$$this.name",
                                                    description: "$$this.description",
                                                    isActive: "$$this.isActive",
                                                    subSettings: {
                                                        $filter: {
                                                            input: "$subSettings",
                                                            as: "child",
                                                            cond: { $eq: ["$$child.parentId", "$$this._id"] }
                                                        }
                                                    }
                                                }
                                            ],
                                            else: []
                                        }
                                    }
                                ]
                            }
                        }
                    }
                }
            },
            {
                $sort: {
                    order: 1
                }
            },
            { $skip: 0 },
            { $limit: 1 },
            {
                $project: {
                    _id: 1,
                    order: 1,
                    organizationId: 1,
                    settingOptionId: 1,
                    key: 1,
                    name: 1,
                    description: 1,
                    isActive: 1,
                    isEnabled: 1,
                    subSettings: 1
                }
            },

        ]
        const result = await this.OrganizationSettingModel.aggregate(pipeline);
        return result.length && result[0] || null

    }

    async findSubSetting(filter: FilterQuery<OrganizationSettings & { organizationId: string | Types.ObjectId }> = {}, options?: QueryOptions<OrganizationSettings>): Promise<{ data: any[], count: number }> {
        const countPromise = await this.OrganizationSubSettingModel.count(filter)
        const dataPromise = await this.OrganizationSubSettingModel.find(filter).sort({ order: 1 }).skip(0).limit(10)
        const [count, data] = await Promise.all([countPromise, dataPromise])
        return {
            count,
            data
        }
    }


    async changeStatusOfSetting(settingId: string, status: boolean) {
        const session = await this.SettingOptionsModel.startSession();

        session.startTransaction();

        try {
            // const setting = await this.findOneSetting({ _id: new Types.ObjectId(settingId) }, { session });
            const setting = await this.OrganizationSettingModel.findOne({ _id: new Types.ObjectId(settingId) })

            if (!setting) throw new Error("Setting not found");

            // Update the selected setting
            await this.OrganizationSettingModel.updateOne(
                { _id: new Types.ObjectId(settingId) },
                { $set: { isActive: status } },
                { session }
            );

            if (!status) {
                // Disable all child sub-settings recursively
                await this.disableChildSettings(settingId, session);
            }
            // else {
            //     // If enabling a setting, check & enable parents if all siblings are enabled
            //     await this.enableParentSettings(settingId, session);
            // }

            await session.commitTransaction();
            session.endSession();
            return { success: true };
        } catch (error) {
            await session.abortTransaction();
            session.endSession();
            throw error;
        }
    }

    async changeStatusOfSubSetting(subSettingId: string, status: boolean) {
        const session = await this.SettingOptionsModel.startSession();

        session.startTransaction();

        try {
            // const setting = await this.findOneSetting({ _id: new Types.ObjectId(settingId) }, { session });
            const subSetting = await this.OrganizationSubSettingModel.findOne({ _id: new Types.ObjectId(subSettingId) })

            if (!subSetting) throw new BadRequestException("Setting not found");
            const setting = await this.OrganizationSettingModel.findOne({ _id: subSetting.settingId })
            if (!setting || !setting.isActive) throw new Error("Setting is can not change as its parent is inactive");

            // Update the selected setting
            await this.OrganizationSubSettingModel.updateOne(
                { _id: new Types.ObjectId(subSettingId) },
                { $set: { isActive: status } },
                { session }
            );

            if (!status) {
                // Disable all child sub-settings recursively
                // await this.disableChildSettings(subSetting._id, session);
                // await this.changeStatusSubSettingAndChildren(subSetting._id, status, session)
                await this.disableSubSettingParentSettings(subSetting._id, session)
            }
            else {
                // If enabling a setting, check & enable parents if all siblings are enabled
                await this.enableParentSubSettings(subSetting._id, session);
            }
            await this.changeStatusSubSettingAndChildren(subSetting._id, status, session)

            await session.commitTransaction();
            session.endSession();
            return { success: true };
        } catch (error) {
            await session.abortTransaction();
            session.endSession();
            throw error;
        }
    }

    async disableChildSettings(settingId: string, session) {
        const childSettings = await this.OrganizationSubSettingModel.find({ $or: [{ settingId: settingId }, { parentId: settingId }] });

        const promises = []
        for (const child of childSettings) {
            promises.push(this.OrganizationSubSettingModel.updateOne({ _id: child._id },
                { $set: { isActive: false } },
                { session })
            )

            // Recursively disable sub-settings of this child

            await this.disableChildSettings(child._id.toString(), session);
        }
    }

    async enableParentSubSettings(settingId: string, session) {
        const parentSetting = await this.OrganizationSubSettingModel.findOne({ _id: new Types.ObjectId(settingId) });
        // const rootSetting = await this.OrganizationSubSettingModel.findOne({ settingId: new Types.ObjectId(settingId) });

        if (parentSetting?.parentId) {
            const siblings = await this.OrganizationSubSettingModel.find({ parentId: parentSetting.parentId });

            const allSiblingsEnabled = siblings.every(sibling => sibling.isActive);

            if (allSiblingsEnabled) {
                await this.OrganizationSubSettingModel.updateOne(
                    { _id: parentSetting.parentId },
                    { $set: { isActive: true } },
                    { session }
                );
                // Recursively enable the parent
                await this.enableParentSubSettings(parentSetting.parentId.toString(), session);
            }
        }
    }

    async disableSubSettingParentSettings(settingId: string, session) {
        const parentSetting = await this.OrganizationSubSettingModel.findOne(
            { _id: new Types.ObjectId(settingId) },
            { parentId: 1 }
        ).session(session);
        if (parentSetting?.parentId) {
            // Check if all sibling settings are disabled
            const siblings = await this.OrganizationSubSettingModel.find(
                { parentId: parentSetting.parentId },
                { isActive: 1 }
            ).session(session);

            const allSiblingsDisabled = siblings.every(sibling => !sibling.isActive);

            if (allSiblingsDisabled) {
                await this.OrganizationSubSettingModel.updateOne(
                    { _id: parentSetting.parentId },
                    { $set: { isActive: false } },
                    { session }
                );
                // Recursively check and disable the next level parent
                await this.disableSubSettingParentSettings(parentSetting.parentId.toString(), session);
            }
        }
    }

    async changeStatusSubSettingAndChildren(settingId: string, status: boolean, session) {
        const queue = [new Types.ObjectId(settingId)];
        const allChildren = [];

        while (queue.length > 0) {
            const parentId = queue.shift();

            // Find direct children of the current parent
            const children = await this.OrganizationSubSettingModel.find(
                { parentId },
                { _id: 1 }
            ).session(session);

            if (children.length === 0) continue;

            // Collect all child IDs for batch update
            const childIds = children.map(child => child._id);
            allChildren.push(...childIds);

            // Push these children to the queue for next iteration
            queue.push(...childIds);
        }

        // Batch update all collected child sub-settings in one go
        if (allChildren.length > 0) {
            await this.OrganizationSubSettingModel.updateMany(
                { _id: { $in: allChildren } },
                { $set: { isActive: status } },
                { session }
            );
        }
    }

    async isEnabled(organizationId: IDatabaseObjectId, settingKey: string): Promise<boolean> {
        const cacheKey = `${this.organizationSettingCacheKey}:${organizationId.toString()}:${settingKey}`;
        const cachedValue = await this.cachingService.get(cacheKey);
        if (cachedValue) {
            return cachedValue === "true" || cachedValue === true;
        }
        const setting = settingKey.startsWith('settings_')
            ? await this.OrganizationSettingModel.findOne({ organizationId, key: settingKey })
            : await this.OrganizationSubSettingModel.findOne({ organizationId, key: settingKey });

        await this.cachingService.set(cacheKey, setting?.isEnabled && setting?.isActive, CACHE_DEFAULT_TTL * 1000);
        // await this.cachingService.set(cacheKey, setting?.isEnabled && setting?.isActive);
        return setting ? setting.isEnabled && setting.isActive : false;
    }

}
