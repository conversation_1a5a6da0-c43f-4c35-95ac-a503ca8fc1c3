{"create": "Voucher created successfully", "update": "Voucher updated successfully", "delete": "Voucher deleted successfully", "list": "Vouchers retrieved successfully", "get": "Voucher details fetched successfully", "pricing-list": "Vouchers retrieved successfully", "status": "Voucher status updated successfully", "error": {"notFound": "Sorry, we couldn't find the requested voucher", "alreadyExists": "A voucher with this name already exists", "invalidDateRange": "Invalid date range for the voucher", "invalidTimeWindow": "Invalid time window for the voucher", "alreadyUsedCannotDelete": "Voucher is already used and cannot be deleted"}}