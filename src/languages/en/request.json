{"validation": "There are validation errors.", "unknownValue": "Unknown value is not allowed.", "min": "{property} has fewer elements than the minimum allowed.", "max": "{property} has more elements than the maximum allowed.", "maxLength": "{property} exceeds the maximum length allowed.", "minLength": "{property} is shorter than the minimum length allowed.", "isString": "{property} should be a string.", "isNotEmpty": "{property} cannot be empty.", "isLowercase": "{property} should be in lowercase.", "isOptional": "{property} is optional.", "isPositive": "{property} should be a positive number.", "isEmail": "{property} should be a valid email address.", "isInt": "{property} should be an integer.", "isNumberString": "{property} should be a numeric string.", "isNumber": "{property} should be a number.", "isMongoId": "{property} should be a valid MongoDB ObjectId.", "isBoolean": "{property} should be a boolean.", "isEnum": "{property} does not match any valid enum value.", "isObject": "{property} should be an object.", "isArray": "{property} should be an array.", "arrayNotEmpty": "{property} array should not be empty.", "minDate": "{property} is earlier than the minimum allowed date.", "maxDate": "{property} is later than the maximum allowed date.", "isDate": "{property} should be a valid date.", "isPasswordStrong": "{property} must be a strong password.", "isPasswordMedium": "{property} must be a medium strength password.", "isPasswordWeak": "{property} must be a weak password.", "isStartWith": "{property} should start with {value}.", "safeString": "{property} should be a safe string, containing only A-Z, a-z, 0-9, and the symbols '_-'.", "isOnlyDigits": "{property} should contain only digits.", "mobileNumberAllowed": "{property} should be a valid mobile number.", "maxBinaryFile": "{property} size exceeds the maximum allowed. It should be less than {value}.", "dateGreaterThanEqualToday": "{property} must be today or later.", "dateLessThanEqualToday": "{property} must be today or earlier.", "LessThan": "{property} should be less than {value}.", "lessThanEqual": "{property} should be less than or equal to {value}.", "greaterThan": "{property} should be greater than {value}.", "greaterThanEqual": "{property} should be greater than or equal to {value}."}