import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class CashDenomination extends Document {

    @Prop({ type: Types.ObjectId, ref: 'Facility', required: true })
    facilityId: Types.ObjectId;

    @Prop({ type: Types.ObjectId, ref: 'Organization', required: true })
    organizationId: Types.ObjectId;

    @Prop({ type: Date, required: true })
    date: Date;

    @Prop({ type: [Types.ObjectId], required: true })
    invoiceIds: Types.ObjectId[]; 

    @Prop({
        type: [
            {
                denominationValue: { type: Number, required: true },
                count: { type: Number, required: true },
                totalAmount: { type: Number, required: true },
            },
        ],
        required: true,
    })
    denominations: {
        denominationValue: number;
        count: number;
        totalAmount: number;
    }[];

    @Prop({ type: Number, required: true })
    totalCashAmount: number;
}

export const CashDenominationSchema = SchemaFactory.createForClass(CashDenomination);