import { Body, Controller, HttpCode, HttpStatus, Post } from '@nestjs/common';
import { TransactionsService } from '../service/transactions.service';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { GetDelegatedUser, GetUser } from 'src/auth/decorators/get-user.decorator';
import { CreateReconciliationDto } from '../dto/create-reconciliation.dto';
import { GetReconciliationDto } from '../dto/get-reconciliation.dto';
import { IUserDocument } from 'src/users/interfaces/user.interface';
import { AuthJwtAccessProtected, AuthSessionProtected } from 'src/auth/decorators/auth.jwt.decorator';
import { PolicyAbilityDelegateProtected, PolicyAbilityProtected } from 'src/policy/decorators/policy.decorator';
import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { GetOrganizationId } from 'src/organization/decorators/organization.decorator';
import { IDatabaseObjectId } from 'src/common/database/interfaces/database.objectid.interface';


@ApiTags("Transactions")
@ApiBearerAuth()
@Controller('transactions')
export class TransactionsController {
    constructor(private readonly transactionService: TransactionsService) { }

    @Post("/reconciliation/create")
    @PolicyAbilityDelegateProtected(ENUM_PERMISSION_TYPE.TRANSACTION_WRITE)
    @AuthSessionProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Create a transaction" })
    async createReconciliation(
        @Body() body: CreateReconciliationDto,
        @GetDelegatedUser() delegateUser: IUserDocument,
    ): Promise<any> {
        const reconciliation = await this.transactionService.createReconciliation(delegateUser, body);
        return {
            message: "Reconciliation created successfully",
            data: reconciliation
        }
    }

    @Post("/reconciliation/get")
    @HttpCode(HttpStatus.OK)
    @PolicyAbilityDelegateProtected(ENUM_PERMISSION_TYPE.TRANSACTION_READ)
    @AuthSessionProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get reconciliations" })
    async getReconciliations(
        @GetUser() user: IUserDocument,
        @Body() body: GetReconciliationDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const reconciliations = await this.transactionService.getReconciliation(user, organizationId, body);
        return {
            message: "Reconciliations fetched successfully",
            data: reconciliations
        }
    }

}
