import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, PipelineStage, Types } from 'mongoose';
import { UserDocument } from 'src/users/schemas/user.schema';
import { StaffProfileDetails } from 'src/staff/schemas/staff.schema';
import { Facility } from 'src/facility/schemas/facility.schema';
import { CreateReconciliationDto } from '../dto/create-reconciliation.dto';
import { Reconciliation } from '../schema/reconciliation.schema';
import { GetReconciliationDto } from '../dto/get-reconciliation.dto';
import { Purchase, PurchaseDocument } from 'src/users/schemas/purchased-packages.schema';
import { Invoice } from 'src/users/schemas/invoice.schema';
import { PaymentStatus } from 'src/utils/enums/payment.enum';
import { PaymentMethod } from 'src/utils/enums/paymentMethod.enum';
import { InvoiceStatus } from 'src/utils/enums/invoice-status.enum';
import { GetReconciliationResponse } from '../interfaces/get-reconciliation.response';
import { IUserDocument } from 'src/users/interfaces/user.interface';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';
import { PaymentMethodSchema } from 'src/paymentMethod/schemas/payment-method.schema';
import { IDatabaseObjectId } from 'src/common/database/interfaces/database.objectid.interface';

@Injectable()
export class TransactionsService {
    constructor(
        @InjectModel(Reconciliation.name) private ReconciliationModel: Model<Reconciliation>,
        @InjectModel(StaffProfileDetails.name) private StaffProfileModel: Model<StaffProfileDetails>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        @InjectModel(Invoice.name) private readonly InvoiceModel: Model<Invoice>,
        @InjectModel('PaymentMethod') private readonly paymentMethodModel: Model<typeof PaymentMethodSchema>,
    ) { }

    private async getFacility(facilityId: string) {
        const facility = await this.FacilityModel.findOne({ _id: facilityId }, { organizationId: 1 })
        if (!facility) {
            throw new BadRequestException("This facility does not exist");
        }
        return facility;
    }

    private async getOrganizationId(user: IUserDocument) {
        const { role } = user;
        let organizationId = null;
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                organizationId = user._id;
                break;

            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.TRAINER:
                const staffDetails = await this.StaffProfileModel.findOne(
                    { userId: user._id },
                    { organizationId: 1 }
                );
                if (!staffDetails) throw new BadRequestException("Access denied, Staff does not have access");
                organizationId = staffDetails.organizationId
                break;

            default:
                throw new BadRequestException("Access denied");
        }
        return organizationId;
    }


    public async getReconciliation(
        user: IUserDocument,
        organizationId: IDatabaseObjectId,
        body: GetReconciliationDto,
    ): Promise<GetReconciliationResponse | null> {
        const { facilityId } = body;

        // Validate facility ownership in a single query
        const facility = await this.FacilityModel.findOne({
            _id: facilityId,
            organizationId
        });

        if (!facility) {
            throw new BadRequestException("Facility not found or does not belong to your organization");
        }

        // Get latest reconciliation
        const latestReconciliation = await this.ReconciliationModel.findOne(
            { facilityId, organizationId },
            {},
            { sort: { updatedAt: -1 } }
        );

        if (!latestReconciliation) {
            return null;
        }

        // Optimized aggregation pipeline
        const pipeline: PipelineStage[] = [
            {
                $match: {
                    facilityId: new Types.ObjectId(facilityId),
                    organizationId: new Types.ObjectId(organizationId),
                    paymentStatus: PaymentStatus.COMPLETED,
                    invoiceStatus: { $ne: InvoiceStatus.CANCELED },
                    createdAt: { $gte: latestReconciliation.createdAt }
                }
            },
            {
                $unwind: "$paymentDetails", // Deconstruct the paymentDetails array
            },
            {
                $group: {
                    _id: "$paymentDetails.paymentMethod",
                    total: { $sum: "$paymentDetails.amount" }
                }
            },
            {
                $project: {
                    _id: 0,
                    method: "$_id",
                    total: 1
                }
            }
        ];
        const salesByMethod = await this.InvoiceModel.aggregate(pipeline);
        const paymentMethods = await this.paymentMethodModel.find({});
        const cashSales = salesByMethod.find(sale => sale.method === PaymentMethod.CASH)?.total || 0;
        const otherSales = salesByMethod.filter(sale => sale.method !== PaymentMethod.CASH);
        const finalOtherSales = paymentMethods
            .filter((method: any) => otherSales.some(sale => sale.method === method.shortId))
            .map((method: any) => {
                const foundSale = otherSales.find(sale => sale.method === method.shortId);
                return {
                    shortId: method.shortId,
                    method: method.name,
                    total: foundSale?.total || 0,
                };
            });
        return {
            _id: latestReconciliation._id,
            organizationId,
            facilityId: facilityId,
            staffId: user._id,
            leaveAmount: latestReconciliation.leaveAmount,
            startingAmount: latestReconciliation.startingAmount,
            pettyAmount: latestReconciliation.pettyAmount,
            drawerAmount: latestReconciliation.drawerAmount,
            depositAmount: latestReconciliation.depositAmount,
            denominations: latestReconciliation.denominations,
            overUnder: latestReconciliation.overUnder,
            cashSales,
            otherSales: finalOtherSales,
            onlineOverUnder: latestReconciliation.onlineOverUnder,
            otherPayments: latestReconciliation.otherPayments,
            createdAt: latestReconciliation.createdAt,
            updatedAt: latestReconciliation.updatedAt
        };
    }


    public async createReconciliation(user: IUserDocument, body: CreateReconciliationDto): Promise<any> {
        const organizationId = await this.getOrganizationId(user);
        const { facilityId, leaveAmount, notes } = body;

        const facility = await this.getFacility(facilityId);
        if (facility.organizationId.toString() !== organizationId.toString()) {
            throw new BadRequestException("This facility does not belong to your organization");
        }

        const newReconciliation = new this.ReconciliationModel({
            organizationId,
            facilityId: facility._id,
            staffId: user._id,
            leaveAmount: leaveAmount,
            startingAmount: body.startingAmount,
            pettyAmount: body.pettyAmount,
            drawerAmount: body.drawerAmount,
            depositAmount: body.depositAmount,
            cashSales: body.cashSales,
            denominations: body.denominations,
            overUnder: body.overUnder,
            onlineOverUnder: body.onlineOverUnder,
            otherPayments: body.otherPayments,
            notes: notes
        });
        await newReconciliation.save();
        return newReconciliation;
    }

}