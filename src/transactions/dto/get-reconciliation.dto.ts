import { Is<PERSON>ongoId, <PERSON><PERSON><PERSON><PERSON>mpty, <PERSON><PERSON><PERSON><PERSON> } from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

export class GetReconciliationDto {

    @ApiProperty({
        description: "Facility ID for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    @IsMongoId()
    facilityId: string;
}   