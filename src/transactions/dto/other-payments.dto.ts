import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsN<PERSON>ber, IsNotEmpty, Min, IsString, IsOptional } from 'class-validator';

export class OtherPaymentsDto {
    
    @ApiProperty({ 
        example: 'Credit Card',
        required: true,
        description: 'Method of the other payment' 
    })
    @IsNotEmpty()
    @IsString()
    method: string;

    @ApiProperty({ 
        description: 'Total amount of the other payment',
        example: 100,
        required: true,
     })
    @IsNumber()
    @IsNotEmpty()
    @Min(0)
    @Type(() => Number)
    total: number;

    @ApiProperty({ 
        description: 'Collected amount of the other payment',
        example: 100,
        required: false,
    })
    @IsNumber()
    @IsOptional()
    @Min(0)
    @Type(() => Number)
    collected?: number = 0;

}