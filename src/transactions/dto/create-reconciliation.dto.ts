import { Type } from "class-transformer";
import { IsEnum, IsMongoId, IsNotEmpty, IsNumber, IsOptional, IsString, IsArray, ValidateNested } from "class-validator";
import { ApiHideProperty, ApiProperty } from "@nestjs/swagger";
import { DenominationDto } from './denomination.dto';
import { OtherPaymentsDto } from "./other-payments.dto";

export class CreateReconciliationDto {
    @ApiProperty({
        description: "Facility ID for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    @IsMongoId()
    facilityId: string;

    @ApiProperty({
        description: "Total amount of the reconciliation",
        type: Number,
        example: 1000,
    })
    @IsNotEmpty()
    @IsNumber()
    @Type(() => Number) 
    leaveAmount: number = 0;

    @ApiProperty({
        description: "Starting amount of the reconciliation",
        type: Number,
        example: 1000,
    })
    @IsNotEmpty()
    @IsNumber()
    @Type(() => Number) 
    startingAmount: number = 0;

    @ApiProperty({
        description: "Petty amount of the reconciliation",
        type: Number,
        example: 1000,
    })
    @IsOptional()
    @IsNumber()
    @Type(() => Number) 
    pettyAmount?: number = 0;

    @ApiProperty({
        description: "Drawer amount of the reconciliation",
        type: Number,
        example: 1000,
    })
    @IsOptional()
    @IsNumber()
    @Type(() => Number) 
    drawerAmount?: number = 0;

    @ApiProperty({
        description: "Deposit amount of the reconciliation",
        type: Number,
        example: 1000,
    })
    @IsOptional()
    @IsNumber()
    @Type(() => Number) 
    depositAmount?: number = 0;

    @ApiProperty({ type: [DenominationDto], description: 'Array of denominations' })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => DenominationDto)
    denominations: DenominationDto[] = [];

    @ApiProperty({ type: [OtherPaymentsDto], description: 'Array of other payments' })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => OtherPaymentsDto)
    otherPayments: OtherPaymentsDto[] = [];

    @ApiProperty({ description: 'Cash sales amount' })
    @IsNumber()
    cashSales: number = 0;

    @ApiProperty({ description: 'Over/Under amount' })
    @IsNumber()
    @IsNotEmpty()
    overUnder: number = 0;

    @ApiHideProperty()
    @IsNumber()
    @IsOptional()
    onlineOverUnder?: number = 0;

    @ApiProperty({
        description: "Notes for the reconciliation",
        type: String,
        example: "This is a note",
    })
    @IsOptional()
    @IsString()
    notes?: string = "";
}   
