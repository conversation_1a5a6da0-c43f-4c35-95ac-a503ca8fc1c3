import { Module } from '@nestjs/common';
import { CommonModule } from 'src/common/common.module';
import { TerminusModule } from '@nestjs/terminus';
import { AppMiddlewareModule } from 'src/app/app.middleware.module';
import { WorkerModule } from 'src/worker/worker.module';
import { AuthModule } from "src/auth/auth.module";
import { UsersModule } from "src/users/users.module";
import { UtilsModule } from "src/utils/utils.module";
import { StaffModule } from "src/staff/staff.module";
import { AttributesModule } from "src/attributes/attributes.module";
import { GymModule } from "src/gym/gym.module";
import { MailModule } from "src/mail/mail.module";
import { OrganizationModule } from "src/organization/organization.module";
import { FacilityModule } from "src/facility/facility.module";
import { AppointmentModule } from "src/appointment/appointment.module";
import { RouterModule } from "src/router/router.module";
import { RoomModule } from "src/room/room.module";
import { featureModule } from "src/featureTab/feature.module";
import { membershipModule } from "src/membership/membership.module";
import { TransactionsModule } from "src/transactions/transactions.module";
import { HealthModule } from "src/health/health.module";
import { BookDemoModule } from "src/website-api/website.module";
import { WaitTimeModule } from "src/wait-time/wait-time.module";
import { AnnouncementModule } from "src/announcement/announcement.module";
import { MerchandiseModule } from "src/merchandise/merchandise.module";
import { PaymentMethodModule } from "src/paymentMethod/payment-method.module";
import { SettingsOptionsModule } from "src/settingsOptions/setting-options.module";
import { OrganizationSettingsModule } from "src/organizationSettings/organization-settings.module";
import { CourseModule } from 'src/courses/course.module';
import { LeadsModule } from 'src/leads/leads.module';
import { MasterLeadModule } from 'src/masterLead/master-lead.module';
import { CustomPackageModule } from 'src/customPackage/custom-package.module';
import { MobileAuthModule } from 'src/mobile-auth/mobileauth.module';
import { PromotionsModule } from 'src/promotions/promotions.module';
import { PolicyModule } from 'src/policy/policy.module';
import { CartModule } from 'src/cart/cart.module';
import { Msg91Module } from 'src/message/message.module';
import { WebhookModule } from 'src/zoho-webhook/webhook.module';
import { PaymentModule } from 'src/payment/payment.module';
import { WidgetModule } from 'src/widget/widget.modulte';
import { SchedulerModule } from 'src/scheduler/scheduler.module';

@Module({
    controllers: [],
    providers: [],
    imports: [
        // Common
        CommonModule,
        AppMiddlewareModule,

        // Modules
        HealthModule,
        AuthModule,
        UsersModule,
        // SessionModule,
        UtilsModule,
        StaffModule,
        AttributesModule,
        GymModule,
        MailModule,
        OrganizationModule,
        FacilityModule,
        AppointmentModule,
        RouterModule,
        RoomModule,
        featureModule,
        membershipModule,
        TransactionsModule,
        BookDemoModule,
        WaitTimeModule,
        AnnouncementModule,
        MerchandiseModule,
        PaymentMethodModule,
        SettingsOptionsModule,
        PolicyModule,
        CartModule,
        CourseModule,
        LeadsModule,
        MasterLeadModule,
        CustomPackageModule,
        MobileAuthModule,
        PromotionsModule,
        Msg91Module,
        WebhookModule,
        PaymentModule,
        WidgetModule,
        SchedulerModule,
        WorkerModule,

        //--- System
        TerminusModule,
    ],
})
export class AppModule { }
