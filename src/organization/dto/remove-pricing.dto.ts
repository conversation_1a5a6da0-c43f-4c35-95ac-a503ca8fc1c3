import { ApiProperty } from "@nestjs/swagger";
import { IsMongoId, IsNotEmpty } from "class-validator";

export class RemovePricingDto {
    @ApiProperty({
        description: "The Id of the selected pricing",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsMongoId({ message: "Required valid pricing details" })
    @IsNotEmpty({ message: "Required pricing details" })
    pricingId: string;

    @ApiProperty({
        description: "The Id of the service category",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsNotEmpty({ message: "Required service category details" })
    @IsMongoId({ message: "Required valid service category details" })
    serviceCategoryId: string;

    @ApiProperty({
        description: "The Id of the subType (single value from UI)",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsNotEmpty({ message: "Required subType details" })
    @IsMongoId({ message: "Required valid subType details" })
    subTypeId: string; 
}