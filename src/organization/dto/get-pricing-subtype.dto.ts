import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsBoolean, IsEnum, IsMongoId, IsOptional } from "class-validator";
import { PaginationDTO } from "src/users/dto/pagination.dto";
import { ClassType } from "src/utils/enums/class-type.enum";

export class GetPricingActiveListSubTypeDto extends PaginationDTO {
    @ApiProperty({
        description: "Type of service",
        example: "bookings",
        required: true,
    })
    @IsOptional()
    @IsEnum(ClassType)
    classType: string;

    @ApiProperty({
        description: "This is used to fetch memberships",
        example: true,
        required: false,
    })
    @IsOptional()
    @IsBoolean()
    fetchMemberships?: boolean;

    @ApiProperty({
        description: "Organizatioon Id",
        example: "66cecb432351713ae4447a6b ",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Invalid organization id" })
    organizationId?: string;
    @ApiProperty({
        description: "Organizatioon Id",
        example: "66cecb432351713ae4447a6b ",
        required: true,
    })
    @IsOptional()
    @IsMongoId({ message: "Invalid SubType id" })
    subTypeId?: string;
    @ApiProperty({
        description: "Organizatioon Id",
        example: "66cecb432351713ae4447a6b ",
        required: true,
    })
    @IsOptional()
    @IsMongoId({ message: "Invalid Service Category Id id" })
    serviceCategoryId?: string;


}