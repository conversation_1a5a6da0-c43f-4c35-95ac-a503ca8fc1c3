import { ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsInt, IsMongoId, IsNotEmpty, IsOptional, IsString } from "class-validator";

export class AppointmentTypesDto {
    @ApiProperty({
        description: "The ID of the service category",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsMongoId({ message: "Required valid service category details" })
    serviceId: string;

    @ApiProperty({
        description: "The name of the appointment type.",
        example: "Select",
        required: true,
    })
    @IsString({ message: "Invalid type of appointment type name" })
    @IsNotEmpty({ message: "Appointment type name cannot be empty" })
    name: string;

    @ApiProperty({
        description: "The duration of the appointment type in minutes.",
        example: 45,
        required: true,
    })
    @IsInt({ message: "Invalid format of appointment type duration in minutes" })
    durationInMinutes: number;

    @ApiProperty({
        description: "Appointment type can be booked online or not.",
        example: true,
        required: true,
    })
    @IsBoolean({ message: "Invalid type of online booking" })
    onlineBookingAllowed: Boolean;

    @ApiProperty({
        description: "Add new tier",
        example: true,
        required: true,
    })
    @IsBoolean({ message: "New can only be boolean." })
    new: Boolean;

    @ApiProperty({
        description: "Reference of global tier",
        example: "66cecb432351713ae4447a6b",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Required valid appointment type details" })
    attributeId?: string;

    // @ApiProperty({
    //     description: "Array of ClassTypes for Availability",
    //     enum: ClassType,
    //     example: ClassType.PERSONAL_APPOINTMENT,
    //     required: true,
    // })
    // @IsEnum(ClassType, { each: true, message: "Each ClassType must be a valid enum value" })
    // @IsNotEmpty({ message: "ClassType is required" })
    // classType: ClassType;

    @ApiProperty({
        description: "Image must be a string",
        example: "https://example.com/image",
    })
    @IsOptional()
    @IsString({ message: "Image must be a valid string" })
    image?: string

    @ApiProperty({
        description: "The isFeatured of the service category.",
        example: false,
        required: false,
    })
    @IsOptional()
    isFeatured?: boolean = false;
}
