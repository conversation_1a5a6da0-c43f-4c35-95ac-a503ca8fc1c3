import { ApiProperty } from "@nestjs/swagger";
import { IsMongoId, IsOptional } from "class-validator";

export class PricingListByAppointmentTypeDto {
    @ApiProperty({
        description: "The Id of the service category",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsMongoId({ message: "Required valid service category details" })
    serviceId: string;

    @ApiProperty({
        description: "The Id of the appointment type of particular service category",
        example: "66cecb432351713ae4447a6b",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Required valid appointment type details" })
    appointmentId: string;
}
