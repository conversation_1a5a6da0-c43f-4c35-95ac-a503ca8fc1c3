import { ApiProperty } from "@nestjs/swagger";
import { IsOptional } from "class-validator";
import { PaginationListDto } from "src/common/pagination/dtos/pagination.list.dto";

export class VoucherListDto extends PaginationListDto {
    @ApiProperty({
        description: "Is the voucher active | Boolean",
        example: true,
        required: false,
    })
    @IsOptional()
    isActive?: boolean;

}
