import { ApiProperty } from "@nestjs/swagger";
import { IsMongoId, IsOptional } from "class-validator";
import { PaginationDTO } from "src/users/dto/pagination.dto";

export class GetPricingForSharePassDto extends PaginationDTO {
    @ApiProperty({
        description: "User Id",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsMongoId({ message: "Invalid userId" })
    userId: string;

    @ApiProperty({
        description: "Bundled Pricing Id to get the pricing of same bundle",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Invalid bundledPricingId" })
    bundledPricingId?: string;

    @ApiProperty({
        description: "Invoice Id to get the pricing of same order",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Invalid invoiceId" })
    invoiceId?: string;
}
