import { ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsEnum, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { ClassType } from "src/utils/enums/class-type.enum";

export class AddServiceCategoryDto {
    @ApiProperty({
        description: "The name of the service category.",
        example: "Animal Flow",
        required: true,
    })
    @IsString({ message: "Invalid type of service category name" })
    @IsNotEmpty({ message: "Service category name cannot be empty" })
    name: string;

    @ApiProperty({
        description: "The description of the service category.",
        example: "Animal Flow",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Invalid type of service category description" })
    description: string;

    @ApiProperty({
        description: "The image of the service category.",
        example: "https://github.com/",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Invalid type of service category logo" })
    image: string;

    @ApiProperty({
        description: "The class type of the service category.",
        example: ClassType.BOOKINGS,
        required: true,
    })
    @IsEnum(ClassType, { message: "Invalid class type" })
    classType: ClassType;

    @ApiProperty({
        description: "The isFeatured of the service category.",
        example: false,
        required: false,
    })
    @IsOptional()
    isFeatured?: boolean = false;

    @ApiProperty({
        description: "This service is active or not",
        example: true,
        required: false,
    })
    @IsOptional()
    @IsBoolean({ message: "Invalid flag value for online indicator" })
    isActive?: Boolean;
}
