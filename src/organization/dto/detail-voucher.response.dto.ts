import { ApiProperty } from "@nestjs/swagger";
import { Expose } from "class-transformer";
import { IsOptional } from "class-validator";
import { PaginationListDto } from "src/common/pagination/dtos/pagination.list.dto";
import { TransformObjectId, TransformNestedObjectId } from "src/common/transformers/objectid.transformer";
import { DurationUnit } from "src/utils/enums/duration-unit.enum";

export class VoucherResponseDto {
    @ApiProperty({
        description: "The id of the voucher.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @Expose()
    @TransformObjectId()
    _id: string;

    @ApiProperty({
        description: "The organization id of the voucher.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @Expose()
    @TransformObjectId()
    organizationId: string;

    @ApiProperty({
        description: "The name of the voucher.",
        example: "New Year Voucher",
        required: true,
    })
    @Expose()
    name: string;

    @ApiProperty({
        description: "The price of the voucher.",
        example: 100,
        required: true,
    })
    @Expose()
    price: number;

    @ApiProperty({
        description: "Can be sold online or not | Boolean",
        example: false,
        required: true,
    })
    @Expose()
    isSellOnline: boolean;

    @ApiProperty({
        description: "Expired in days.",
        example: 30,
        required: true,
    })
    @Expose()
    expiredInDays: number;

    @ApiProperty({
        description: "Duration unit.",
        example: "days",
        required: true,
    })
    @Expose()
    durationUnit: DurationUnit;

    @ApiProperty({
        description: "Is the voucher active | Boolean",
        example: true,
        required: true,
    })
    @Expose()
    isActive: boolean;

    @ApiProperty({
        description: "Description of the voucher.",
        example: "New Year Voucher",
        required: false,
    })
    @Expose()
    description?: string;

    @ApiProperty({
        description: "Created by.",
        example: {
            _id: "659d268dee4b6081dacd41fd",
            name: "John Doe",
            firstName: "John",
            lastName: "Doe",
            email: "<EMAIL>",
        },
        required: false,
    })
    @Expose()
    @TransformNestedObjectId()
    createdBy?: any;

    @ApiProperty({
        description: "Created at.",
        example: "2023-11-20T06:50:15.000Z",
        required: false,
    })
    @Expose()
    createdAt?: Date;

    @ApiProperty({
        description: "Updated at.",
        example: "2023-11-20T06:50:15.000Z",
        required: false,
    })
    @Expose()
    updatedAt?: Date;
}
