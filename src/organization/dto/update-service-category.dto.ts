import { ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { ClassType } from "src/utils/enums/class-type.enum";

export class UpdateServiceCategoryDto {
    @ApiProperty({
        description: "The ID of the service category",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsMongoId({ message: "Required valid service category details" })
    serviceId: string;

    @ApiProperty({
        description: "The name of the service category.",
        example: "Animal Flow",
        required: true,
    })
    @IsOptional()
    @IsString({ message: "Invalid type of service category name" })
     @IsNotEmpty({ message: "Service category name cannot be empty" })
    name: string;

    @ApiProperty({
        description: "The description of the service category.",
        example: "Animal Flow",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Invalid type of service category description" })
    description: string;

    @ApiProperty({
        description: "The image of the service category.",
        example: "https://github.com/",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Invalid type of service category logo" })
    image: string;

    @ApiProperty({
        description: "The class type of the service category.",
        example: ClassType.BOOKINGS,
        required: true,
    })
    @IsEnum(ClassType, { message: "Invalid class type" })
    classType: ClassType;

    @ApiProperty({
        description: "The isFeatured of the service category.",
        example: false,
        required: false,
    })
    @IsOptional()
    isFeatured?: boolean = false;

    @ApiProperty({
        description: "This flag indicates whether the service category is available or not.",
        example: true,
        required: false,
    })
    @IsBoolean({ message: "Invalid flag value for isActive indicator" })
    @IsOptional()
    isActive?: Boolean;
}
