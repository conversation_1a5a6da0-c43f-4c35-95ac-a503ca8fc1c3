import { ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsEmail, IsMongoId, IsNotEmpty, IsObject, IsOptional, IsString, Length, Max, <PERSON>ength, Min, ValidateNested } from "class-validator";

class AddressDto {
    @ApiProperty({
        description: "The ID of the state where the organization is located.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsMongoId({ message: "State ID must be a valid ObjectId" })
    stateId: string;

    @ApiProperty({
        description: "The ID of the city where the organization is located.",
        example: "659e8032293375c3166a99a0",
        required: true,
    })
    @IsMongoId({ message: "City ID must be a valid ObjectId" })
    cityId: string;

    @ApiProperty({
        description: "The specific Business Address or address of the organization.",
        example: "Damar West Opposite to SBI Bank",
        minLength: 2,
        maxLength: 1000,
        required: true,
    })
    @Length(2, 1000, { message: "Business Address must be between 2 and 1000 characters" })
    addressLine1: string;

    @ApiProperty({
        description: "Please specify postal code of the city. || Must be a number",
        example: 122016,
        minLength: 6,
        maxLength: 6,
        required: true,
    })
    @Min(100000, { message: "Postal code must be a number" })
    @Max(999999, { message: "Invalid postal code" })
    postalCode: number;
}
export class OrganizationDto {
    @ApiProperty({
        description: "The name of the Organization.",
        example: "Knox",
        minLength: 2,
        maxLength: 50,
        required: true,
    })
    @Length(2, 50, { message: "Organization Name must be between 2 and 50 characters" })
    organizationName: string;

    @ApiProperty({
        description: "The mobile number of the owner of organization.",
        example: "**********",
        // minLength: 10,
        // maxLength: 10,
        required: true,
    })
    @IsNotEmpty({ message: "Mobile No. is required" })
    //@Length(10, 10, { message: "Mobile No. must be exactly 10 digits" })
    mobile: string;

    @ApiProperty({
        description: "The email address for the Organization.",
        example: "<EMAIL>",
        maxLength: 255,
        required: true,
    })
    @Transform(({ value }) => value.toLowerCase())
    @IsEmail({}, { message: "Invalid Email Format" })
    @IsNotEmpty({ message: "Email is required" })
    @MaxLength(255, { message: "Email must be less than 255 characters" })
    email: string;

    @ApiProperty({
        description: "The address of the organization.",
        example: AddressDto,
        required: true,
    })
    @ValidateNested({ message: "Address is invalid" })
    @Type(() => AddressDto)
    @IsNotEmpty({ message: "Address is required" })
    address: AddressDto;

    @ApiProperty({
        description: "Logo url of the organization.",
        example: "https://dnfkjsdfdlf.png",
        required: true,
    })
    @IsOptional()
    @IsString({ message: "Image must be a valid string" })
    // @IsNotEmpty({ message: "Image cannot be empty" })
    logo: string;

    @ApiProperty({
        description: "Contact person name.",
        example: "Abhay Gupta",
        minLength: 2,
        maxLength: 50,
        required: true,
    })
    @Length(2, 50, { message: "Required valid contacted person name" })
    associatedPerson: string;

    @ApiProperty({
        description: "Client onboarding details",
        example: "{}",
        required: true,
    })
    @IsOptional()
    @IsObject({ message: "Required client onboarding options" })
    clientOnboarding: object;

    @ApiProperty({
        description: "Staff onboarding details.",
        example: "{}",
        required: true,
    })
    @IsOptional()
    @IsObject({ message: "Required staff onboarding options" })
    staffOnboarding: object;
}
