import { ApiProperty } from "@nestjs/swagger";
import { IsInt, IsMongoId, IsNotEmpty, IsO<PERSON>al, IsS<PERSON>, <PERSON>, <PERSON> } from "class-validator";

export class ServicesFromPackageDto {

    @ApiProperty({
        description: "Package Id for getting required  services of the package.",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Package ID must be a valid MongoDB ObjectId." })
    packageId?: string;

    @ApiProperty({
        description: "Search keyword (optional). Must be a string.",
        example: "Knox",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Search must be a valid string." })
    search?: string;

    @ApiProperty({
        description: "Number of items per page.",
        example: 10,
        minimum: 1,
        maximum: 50,
        required: true,
    })
    @IsNotEmpty({ message: "PageSize is required" })
    @IsInt({ message: "PageSize must be an integer" })
    @Min(1, { message: "PageSize must be at least 1" })
    @Max(50, { message: "PageSize must be at most 50" })
    pageSize: number = 10;

    @ApiProperty({
        description: "Page number to jump to.",
        example: 1,
        minimum: 1,
        required: true,
    })
    @IsNotEmpty({ message: "Page is required" })
    @IsInt({ message: "Page must be an integer" })
    @Min(1, { message: "Page must be at least 1" })
    page: number = 1;
}
