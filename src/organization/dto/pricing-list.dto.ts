import { ApiProperty } from "@nestjs/swagger";
import { IsInt, <PERSON><PERSON>ptional, <PERSON>, <PERSON>, IsString, IsNotEmpty } from "class-validator";

export class PricingListDto {
    @ApiProperty({
        description: "Number of items per page.",
        example: 10,
        minimum: 1,
        maximum: 50,
        required: true,
    })
    @IsOptional()
    @IsInt()
    @Min(1)
    @Max(30)
    pageSize: number;

    @ApiProperty({
        description: "Page number to jump to.",
        example: 1,
        minimum: 1,
        required: true,
    })
    @IsOptional()
    @IsInt()
    @Min(1)
    page: number;

    @ApiProperty({
        description: "Search must be a string || Search is optional",
        example: "Yoga",
    })
    @IsOptional()
    @IsString({ message: "Search must be a string" })
    @IsNotEmpty({ message: "Search must not be empty" })
    search: string;
}
