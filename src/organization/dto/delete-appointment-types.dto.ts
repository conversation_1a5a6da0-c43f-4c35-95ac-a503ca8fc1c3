import { ApiProperty } from "@nestjs/swagger";
import { IsMongoId } from "class-validator";

export class DeleteAppointmentTypesDto {
    @ApiProperty({
        description: "The ID of the service category",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsMongoId({ message: "Required valid service category details" })
    serviceId: string;

    @ApiProperty({
        description: "The ID of Appointment type of the service category",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsMongoId({ message: "Required valid appointment type details" })
    appointmentTypeId: string;
}
