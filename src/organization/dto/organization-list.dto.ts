import { ApiProperty } from "@nestjs/swagger";
import { <PERSON>Array, IsMongoId, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { PaginationDTO } from "src/users/dto/pagination.dto";

export class OrganizationListDto extends PaginationDTO {
    @ApiProperty({
        description: "Search must be a string || Search is optional",
        example: "Knox",
    })
    @IsOptional()
    @IsString({ message: "Search must be a string" })
    @IsNotEmpty({ message: "Search must not be empty" })
    search: string;

    @ApiProperty({
        description: "Id of the selected state",
        example: "66cecb432351713ae4447a6b",
        required: false,
    })
    @IsOptional()
    @IsMongoId({ message: "Required valid state details" })
    stateId: string;

    @ApiProperty({
        description: "Id of the selected city",
        example: ["66cecb432351713ae4447a6b"],
        required: false,
    })
    @IsOptional()
    @IsArray({ message: "Cities can be multiple" })
    @IsMongoId({ each: true, message: "Required valid city details" })
    cityId: Array<string>;
}
