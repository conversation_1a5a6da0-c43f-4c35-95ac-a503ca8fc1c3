import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsBoolean, IsObject, IsOptional } from "class-validator";

export class OrganizationSettingsDto {
    @ApiProperty({
        description: "Client onboarding details",
        example: "{}",
        required: true,
    })
    @IsOptional()
    clientOnboarding?: object;

    @ApiProperty({
        description: "Staff onboarding details.",
        example: "{}",
        required: true,
    })
    @IsOptional()
    staffOnboarding?: object;

    @ApiProperty({
        description: "Is the organization inclusive of GST for all pricing setting",
        example: true,
        required: true,
    })
    @IsOptional()
    @Type(() => Boolean)
    isInclusiveofGst?: boolean;
}
