import { ApiProperty } from "@nestjs/swagger";
import { IsBoolean, IsInt, IsMongoId, IsNotEmpty, IsString } from "class-validator";

export class UpdateAppointmentTypeStatusDto {
    @ApiProperty({
        description: "The ID of the service category",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsMongoId({ message: "Required valid service category details" })
    @IsNotEmpty({ message: "Required service category details" })
    serviceId: string;

    @ApiProperty({
        description: "The ID of Appointment type of the service category",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsNotEmpty({ message: "Required appointment type details" })
    @IsMongoId({ message: "Required valid appointment type details" })
    appointmentTypeId: string;

    @ApiProperty({
        description: "Updated status of the appointment type",
        example: true,
        required: true,
    })
    @IsNotEmpty({ message: "Required status of appointment type" })
    @IsBoolean({ message: "Required valid isActive" })
    isActive: boolean;
}
