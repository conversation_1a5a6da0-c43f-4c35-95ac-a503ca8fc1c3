import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, SchemaTypes } from "mongoose";

export type ServiceCategoryPricingDocument = HydratedDocument<ServiceCategoryPricing>;

@Schema({ timestamps: true })
export class ServiceCategoryPricing {
    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Organization" })
    organizationId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Services" })
    serviceCategoryId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Service" })
    appointmentTypeId: string;

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "Pricing" })
    pricingId: string;

    @Prop()
    createdAt: Date;

    @Prop()
    updatedAt: Date;
}

export const ServiceCategoryPricingSchema = SchemaFactory.createForClass(ServiceCategoryPricing);
