import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, HydratedDocument, SchemaTypes } from "mongoose";
import { ClassType } from "src/utils/enums/class-type.enum";

@Schema({
    timestamps: false,
})
export class AppointmentType {
    @Prop({ type: String, required: true })
    name: string;

    @Prop({ type: Number, required: true })
    durationInMinutes: number;

    @Prop({ type: Boolean, required: true })
    onlineBookingAllowed: boolean;

    @Prop({ type: Boolean, required: false, default: true })
    isActive: boolean;

    @Prop({ type: String, required: false, default: "" })
    image?: string;

    @Prop({ type: Boolean, required: false, default: false })
    isFeatured?: boolean;
}
export const AppointmentTypeSchema = SchemaFactory.createForClass(AppointmentType);
export type AppointmentTypeDocument = HydratedDocument<AppointmentType>;


@Schema({ timestamps: true })
export class Services {
    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    organizationId: string;

    @Prop({ type: String, required: true })
    name: string;

    @Prop({ type: String, required: false, default: "" })
    description: string;

    @Prop({ type: String, required: false })
    image: string;

    @Prop({ type: String, required: true, enum: ClassType })
    classType: string;

    @Prop({ type: Boolean, required: true, default: true })
    isActive: Boolean;

    @Prop({
        type: [AppointmentTypeSchema],
        required: false,
        default: [],
    })
    appointmentType: AppointmentType[];

    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    createdBy: string;

    @Prop({ type: Boolean, required: false })
    isFeatured?: boolean;

    @Prop()
    createdAt: Date;

    @Prop()
    updatedAt: Date;
}

export const ServiceSchema = SchemaFactory.createForClass(Services);
export type ServiceDocument = HydratedDocument<Services>;