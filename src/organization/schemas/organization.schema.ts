import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, HydratedDocument, SchemaTypes } from "mongoose";

export type OrganizationDocument = HydratedDocument<Organizations>;

class Assessment {
    @Prop({ type: Boolean, default: false })
    weight: boolean;

    @Prop({ type: Boolean, default: false })
    measurement: boolean;
}
@Schema()
class PolicyItem {
    @Prop({ type: String, required: true })
    name: string;

    @Prop({ type: Boolean, default: false })
    required: boolean;

    @Prop({ type: Boolean, default: false })
    isShown: boolean;

    // @Prop({ type: Number, default: null })
    // expiry: number | null;

    // @Prop({ type: String, enum: ['Days', 'Months', 'Years'], default: 'Months' })
    // expiryType: 'Days' | 'Months' | 'Years';
}
export const PolicyItemSchema = SchemaFactory.createForClass(PolicyItem);
@Schema()
class PoliciesItemsWrapper {
    @Prop({ type: [PolicyItem], default: [] })
    items: PolicyItem[];
}

export const PoliciesItemsWrapperSchema = SchemaFactory.createForClass(PoliciesItemsWrapper);
class ProficiencyLevelItem {
    @Prop({ type: Number })
    id: number;

    @Prop({ type: String })
    value: string;
}

@Schema()
class ClientOnboarding {
    @Prop({ type: Boolean, default: false })
    showPolicies: boolean;
    @Prop({ type: Boolean, default: false })
    showProficiencyLevel: boolean;
    @Prop({ type: Boolean, default: false })
    showAssessment: boolean;

    @Prop({ type: Assessment })
    assessment: Assessment;


    @Prop({ type: PoliciesItemsWrapper, default: {} })
    policies: PoliciesItemsWrapper;

    @Prop({ type: Boolean, default: false })
    notes: boolean;

    @Prop({ type: [ProficiencyLevelItem], default: [] })
    proficiencyLevel: ProficiencyLevelItem[];

    @Prop({ type: Boolean, default: false })
    sharePass: boolean;
}

@Schema()
class StaffOnboarding {
    @Prop({ type: Boolean, default: false })
    pin: boolean;

    @Prop({ type: Boolean, default: false })
    skillsExperience: boolean;

    @Prop({ type: Boolean, default: false })
    payRates: boolean;

    @Prop({ type: Number, default: 0 })
    pinStandBy: number;
}

@Schema({ timestamps: false })
class RevenueCategory extends Document {
    @Prop({ type: String, required: true, minLength: 2, maxLength: 50 })
    name: string;

    @Prop({ type: String, required: false, default: "" })
    description: string;

    @Prop({ type: Boolean, required: false, default: true })
    isActive: boolean;
}

@Schema({ timestamps: true })
export class Organizations {
    @Prop({ type: SchemaTypes.ObjectId, required: true, ref: "User" })
    userId: string;

    @Prop({ type: String, required: false })
    logo: string;

    @Prop({
        type: {
            state: { type: SchemaTypes.ObjectId, ref: "State" },
            city: { type: SchemaTypes.ObjectId, ref: "City" },
            addressLine1: { type: String },
            postalCode: { type: Number },
        },
        required: true,
    })
    address: { state: string; city: string; addressLine1: string; postalCode: number };

    @Prop({ type: String, required: true })
    associatedPerson: string;

    @Prop({ type: Object, required: false, default: {} })
    gymAttributes: Object;

    @Prop({ type: ClientOnboarding, default: {} })
    clientOnboarding: ClientOnboarding;

    @Prop({ type: StaffOnboarding, default: {} })
    staffOnboarding: StaffOnboarding;

    @Prop({ type: Boolean, required: false, default: true })
    isInclusiveofGst: boolean; // Is the organization inclusive of GST setting

    @Prop({ type: [RevenueCategory], default: [] })
    revenueCategory?: RevenueCategory[];

    @Prop()
    createdAt: Date;

    @Prop()
    updatedAt: Date;
}

export const OrganizationSchema = SchemaFactory.createForClass(Organizations);
