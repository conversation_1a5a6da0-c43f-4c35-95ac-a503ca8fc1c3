import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Types } from 'mongoose';
import { VoucherService } from './voucher.service';
import { Pricing } from '../schemas/pricing.schema';
import { Purchase } from 'src/users/schemas/purchased-packages.schema';
import { PaginationService } from 'src/common/pagination/services/pagination.service';
import { TransactionService } from 'src/common/database/services/database.transaction.service';
import { VoucherResponseDto } from '../dto/detail-voucher.response.dto';
import { plainToInstance } from 'class-transformer';

describe('VoucherService - ObjectId Transformation', () => {
  let service: VoucherService;
  let mockPricingModel: any;
  let mockPurchaseModel: any;

  const mockObjectId = new Types.ObjectId();
  const mockOrgId = new Types.ObjectId();
  const mockUserId = new Types.ObjectId();

  const mockVoucherDocument = {
    _id: mockObjectId,
    organizationId: mockOrgId,
    name: 'Test Voucher',
    price: 100,
    isSellOnline: true,
    expiredInDays: 30,
    isActive: true,
    description: 'Test Description',
    createdBy: {
      _id: mockUserId,
      name: 'John Doe',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>'
    },
    createdAt: new Date(),
    updatedAt: new Date(),
    toObject: jest.fn().mockReturnValue({
      _id: mockObjectId,
      organizationId: mockOrgId,
      name: 'Test Voucher',
      price: 100,
      isSellOnline: true,
      expiredInDays: 30,
      isActive: true,
      description: 'Test Description',
      createdBy: {
        _id: mockUserId,
        name: 'John Doe',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
      },
      createdAt: new Date(),
      updatedAt: new Date()
    })
  };

  beforeEach(async () => {
    const mockQuery = {
      sort: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      populate: jest.fn().mockReturnThis(),
      lean: jest.fn().mockResolvedValue(mockVoucherDocument)
    };

    mockPricingModel = {
      find: jest.fn().mockReturnValue(mockQuery),
      findOne: jest.fn().mockReturnValue(mockQuery),
      countDocuments: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue(1) }),
      create: jest.fn(),
      findByIdAndUpdate: jest.fn()
    };

    mockPurchaseModel = {
      aggregate: jest.fn()
    };

    const mockPaginationService = {
      orderFormat: jest.fn().mockReturnValue({ createdAt: 1 })
    };

    const mockTransactionService = {};

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VoucherService,
        {
          provide: getModelToken(Pricing.name),
          useValue: mockPricingModel,
        },
        {
          provide: getModelToken(Purchase.name),
          useValue: mockPurchaseModel,
        },
        {
          provide: PaginationService,
          useValue: mockPaginationService,
        },
        {
          provide: TransactionService,
          useValue: mockTransactionService,
        },
      ],
    }).compile();

    service = module.get<VoucherService>(VoucherService);
  });

  describe('voucherList', () => {
    it('should transform ObjectIds to strings in voucher list', async () => {
      // Mock the find method to return array of documents
      mockPricingModel.find.mockReturnValue({
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        populate: jest.fn().mockResolvedValue([mockVoucherDocument])
      });

      const filter = { organizationId: mockOrgId };
      const order = {
        order: { createdAt: 1 },
        paging: { offset: 0, limit: 10 }
      };

      const result = await service.voucherList(filter, order);

      expect(result.list).toHaveLength(1);
      expect(result.list[0]).toBeInstanceOf(VoucherResponseDto);
      
      // Verify that ObjectIds are converted to strings
      expect(typeof result.list[0]._id).toBe('string');
      expect(result.list[0]._id).toBe(mockObjectId.toString());
      expect(typeof result.list[0].organizationId).toBe('string');
      expect(result.list[0].organizationId).toBe(mockOrgId.toString());
      
      // Verify nested ObjectId transformation
      if (result.list[0].createdBy) {
        expect(typeof result.list[0].createdBy._id).toBe('string');
        expect(result.list[0].createdBy._id).toBe(mockUserId.toString());
      }
    });
  });

  describe('voucherDetails', () => {
    it('should transform ObjectIds to strings in voucher details', async () => {
      const result = await service.voucherDetails(mockObjectId, mockOrgId);

      expect(result).toBeInstanceOf(VoucherResponseDto);
      
      // Verify that ObjectIds are converted to strings
      expect(typeof result._id).toBe('string');
      expect(result._id).toBe(mockObjectId.toString());
      expect(typeof result.organizationId).toBe('string');
      expect(result.organizationId).toBe(mockOrgId.toString());
      
      // Verify nested ObjectId transformation
      if (result.createdBy) {
        expect(typeof result.createdBy._id).toBe('string');
        expect(result.createdBy._id).toBe(mockUserId.toString());
      }
    });
  });

  describe('plainToInstance behavior', () => {
    it('should preserve string IDs when using plainToInstance with transformed objects', () => {
      const mockData = {
        _id: mockObjectId.toString(),
        organizationId: mockOrgId.toString(),
        name: 'Test Voucher',
        price: 100,
        createdBy: {
          _id: mockUserId.toString(),
          name: 'John Doe'
        }
      };

      const result = plainToInstance(VoucherResponseDto, mockData, { excludeExtraneousValues: true });

      expect(typeof result._id).toBe('string');
      expect(result._id).toBe(mockObjectId.toString());
      expect(typeof result.organizationId).toBe('string');
      expect(result.organizationId).toBe(mockOrgId.toString());
      
      if (result.createdBy) {
        expect(typeof result.createdBy._id).toBe('string');
        expect(result.createdBy._id).toBe(mockUserId.toString());
      }
    });
  });
});
