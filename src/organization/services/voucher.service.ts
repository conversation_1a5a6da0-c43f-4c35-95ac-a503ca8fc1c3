import { BadRequestException, Injectable, InternalServerErrorException, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Document } from "mongoose";
import { Pricing, PricingDocument } from "../schemas/pricing.schema";
import { Model, Types } from "mongoose";
import { TransactionService } from "src/utils/services/transaction.service";
import { ActiveTimeFrameService } from "src/utils/services/active-time-frame.service";
import { CreatePricingDto } from "../dto/create-pricing.dto";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { VoucherCreateDto } from "../dto/create-voucher.dto";
import { VoucherUpdateDto } from "../dto/update-voucher.dto";
import { Order } from "aws-sdk/clients/mediaconvert";
import { IDatabaseFindAllOptions } from "src/common/database/interfaces/database.interface";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { ENUM_PRODUCT_ITEM_TYPE } from "src/promotions/enums/item-type.enum";
import { Purchase } from "src/users/schemas/purchased-packages.schema";
import { VoucherResponseDto } from "../dto/detail-voucher.response.dto";
import { plainToInstance } from "class-transformer";
import { transformMongoDocument } from "src/common/transformers/objectid.transformer";
import { PipelineStage } from "mongoose";

@Injectable()
export class VoucherService {
    constructor(
        @InjectModel(Pricing.name) private PricingModel: Model<Pricing>,
        @InjectModel(Purchase.name) private PurchaseModel: Model<Purchase>,
        private readonly paginationService: PaginationService,
        private readonly transactionService: TransactionService,
    ) { }

    async createVoucher(body: VoucherCreateDto, userId: IDatabaseObjectId, organizationId: IDatabaseObjectId): Promise<VoucherResponseDto> {
        const isPricingNameExist = await this.PricingModel.findOne({ name: body?.name, organizationId: organizationId })
        if (isPricingNameExist) {
            throw new BadRequestException("voucher.error.alreadyExists");
        }

        let data = {
            organizationId: organizationId,
            createdBy: userId,
            itemType: ENUM_PRODUCT_ITEM_TYPE.VOUCHER,
            name: body.name,
            price: body.price,
            isSellOnline: body.isSellOnline,
            expiredInDays: body.expiredInDays,
            durationUnit: body.durationUnit,
            isActive: true,
            description: body.description
        }
        const addPricing = new this.PricingModel(data);
        await addPricing.save();
        await addPricing.populate("createdBy", "name firstName lastName email");
        return plainToInstance(VoucherResponseDto, addPricing, { excludeExtraneousValues: true });

    }

    async updateVoucher(body: VoucherUpdateDto, userId: IDatabaseObjectId, organizationId: IDatabaseObjectId): Promise<any> {
        const isPricingNameExist = await this.PricingModel.findOne({ name: body?.name, organizationId: organizationId, _id: { $ne: body.voucherId } })
        if (isPricingNameExist) {
            throw new BadRequestException("voucher.error.alreadyExists");
        }

        let data = {
            name: body.name,
            price: body.price,
            isSellOnline: body.isSellOnline,
            expiredInDays: body.expiredInDays,
            durationUnit: body.durationUnit,
            description: body.description
        }
        const addPricing = await this.PricingModel.findOneAndUpdate(
            { _id: body.voucherId },
            { $set: data },
            { new: true },
        ).populate("createdBy", "name firstName lastName email");
        return plainToInstance(VoucherResponseDto, addPricing, { excludeExtraneousValues: true })
    }

    async voucherList(filter: Record<string, any>, order: IDatabaseFindAllOptions): Promise<{ list: VoucherResponseDto[], count: number }> {
        const list = await this.PricingModel.find(filter, "_id createdBy itemType name organizationId price isSellOnline expiredInDays durationUnit isActive description createdAt updatedAt")
            .sort(this.paginationService.orderFormat(order.order))
            .skip(order.paging.offset)
            .limit(order.paging.limit)
            .populate("createdBy", "_id name firstName lastName email")
        const count = await this.PricingModel.countDocuments(filter).lean();
        return {
            list: list.map((item) => {
                // Use utility function to transform MongoDB document
                // Enable debug in development to troubleshoot date issues
                const transformedObject = transformMongoDocument(item, process.env.NODE_ENV === 'development');
                return plainToInstance(VoucherResponseDto, transformedObject, { excludeExtraneousValues: true });
            }),
            count
        };
    }

    async usersVoucherList(filter: Record<string, any>, order: IDatabaseFindAllOptions): Promise<{ list: VoucherResponseDto[], count: number }> {
        const match = {
            organizationId: new Types.ObjectId(filter.organizationId),
            userId: new Types.ObjectId(filter.userId),
            itemType: filter.itemType,
            isExpired: false,
            isActive: filter.isActive,
            $expr: {
                $gt: [
                    {
                        $subtract: [
                            "$voucherAmount",
                            "$amountConsumed"
                        ]
                    },
                    0
                ]
            }
        }
        let priceMatch = {
            itemType: filter.itemType,
        }

        if (filter.search) {
            priceMatch = {
                ...priceMatch,
                ...filter.search
            };
        }

        const pipeline: PipelineStage[] = [
            { $match: match },
            {
                $lookup: {
                    from: "pricings",
                    localField: "packageId",
                    foreignField: "_id",
                    as: "voucherDetails",
                    pipeline: [
                        {
                            $match: priceMatch
                        }
                    ]
                },
            },
            {
                $unwind: {
                    path: "$voucherDetails",
                    preserveNullAndEmptyArrays: false,
                },
            },
            {
                $facet: {
                    list: [
                        { $sort: this.paginationService.orderFormat(order.order) },
                        { $skip: order.paging.offset },
                        { $limit: order.paging.limit },
                        {
                            $project: {
                                purchaseId: '$_id',
                                organizationId: 1,
                                name: "$voucherDetails.name",
                                userId: 1,
                                packageId: 1,
                                price: '$voucherAmount',
                                amountConsumed: 1,
                                isExpired: 1,
                                isActive: 1,
                                startDate: 1,
                                endDate: 1,
                                remainingAmount: {
                                    $subtract: ["$voucherAmount", "$amountConsumed"]
                                },
                            }
                        }
                    ],
                    total: [
                        { $count: "total" }
                    ]
                }
            }
        ];
        const list = await this.PurchaseModel.aggregate(pipeline);
        const count = list[0]?.total[0]?.total || 0;
        return {
            list: list[0]?.list,
            count
        };
    }

    async voucherDetails(voucherId: IDatabaseObjectId, organizationId: IDatabaseObjectId): Promise<VoucherResponseDto> {
        const result = await this.PricingModel.findOne({
            _id: voucherId,
            organizationId: organizationId,
            itemType: ENUM_PRODUCT_ITEM_TYPE.VOUCHER
        }).populate("createdBy", "name firstName lastName email").lean();
        if (!result) {
            throw new NotFoundException('voucher.error.notFound');
        }
        // Use utility function to transform MongoDB document
        const transformedResult = transformMongoDocument(result);
        return plainToInstance(VoucherResponseDto, transformedResult, { excludeExtraneousValues: true });
    }

    async voucherStatusUpdate(voucherId: IDatabaseObjectId, status: boolean, organizationId: IDatabaseObjectId): Promise<any> {
        const result = await this.PricingModel.findOneAndUpdate({
            _id: voucherId,
            organizationId: organizationId,
            itemType: ENUM_PRODUCT_ITEM_TYPE.VOUCHER
        }, { $set: { isActive: status } }, { new: true }).lean();
        if (!result) {
            throw new NotFoundException('voucher.error.notFound');
        }
        return result;
    }

    async voucherDelete(voucherId: IDatabaseObjectId, organizationId: IDatabaseObjectId): Promise<any> {
        const isPricingUsed = await this.PurchaseModel.findOne({ packageId: voucherId });
        if (isPricingUsed) {
            throw new BadRequestException("voucher.error.alreadyUsedCannotDelete");
        }
        const result = await this.PricingModel.findOneAndDelete({
            _id: voucherId,
            organizationId: organizationId,
            itemType: ENUM_PRODUCT_ITEM_TYPE.VOUCHER
        }).lean();
        if (!result) {
            throw new NotFoundException("voucher.error.notFound");
        }
        return result;
    }

}
