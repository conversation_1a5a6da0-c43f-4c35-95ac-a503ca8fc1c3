import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import { SchemaTypes, Document, Types } from "mongoose";

@Schema({ timestamps: true })
export class Feature extends Document {
    @Prop({ type: String, required: true })
    name: string;

    @Prop({ type: Boolean, default: true })
    isActive: boolean;

    @Prop({
        type: [
            {
                packageId: { type: SchemaTypes.ObjectId, required: true, ref: "Pricing" },
                color: { type: String, required: false },
                sorting: { type: Number, required: false },
            },
        ],
        default: [],
    })
    packageList: Array<{
        packageId: Types.ObjectId;
        color?: string;
        sorting?: number;
    }>;

    @Prop({ type: SchemaTypes.ObjectId, ref: "User", required: true, index: true })
    organizationId: Types.ObjectId;
}

export const FeatureSchema = SchemaFactory.createForClass(Feature);
