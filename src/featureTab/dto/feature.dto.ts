import { 
  IsBoolean, 
  IsNotEmpty, 
  IsOptional, 
  IsString, 
  IsArray, 
  ValidateNested, 
  IsMongoId, 
  IsNumber 
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

class PackageDto {
  @ApiProperty({
    description: 'The ID of the package',
    type: String,
    required: true,
    example: '64f5e8d3b6374d25f0e6f8b2',
  })
  @IsMongoId()
  @IsNotEmpty()
  packageId: string;

  @ApiProperty({
    description: 'The color HEX code associated with the package ',
    type: String,
    required: false,
    example: '#fffff',
  })
  @IsOptional()
  @IsString()
  color?: string;

  @ApiProperty({
    description: 'The sorting order of the package',
    type: Number,
    required: false,
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  sorting?: number;
}

export class CreateFeatureDto {
  @ApiProperty({
    description: 'The name of the feature',
    type: String,
    required: true,
    example: 'Premium Subscription Feature',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Whether the feature is active',
    type: Boolean,
    required: false,
    default: true,
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    description: 'List of associated packages',
    type: [PackageDto],
    required: false,
    default: [],
    example: [
      {
        packageId: '64f5e8d3b6374d25f0e6f8b2',
        color: '#ffffff',
        sorting: 2,
      },
      {
        packageId: '64f5e8d3b6374d25f0e6f8b3',
        color: '#ffffff',
        sorting: 1,
      },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PackageDto)
  packageList: PackageDto[];

}

export class featureListDto {
  @ApiProperty({
    description: 'Search keyword for filtering features by name',
    type: String,
    required: false,
    example: 'Premium',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Filter features by active status',
    type: Boolean,
    required: false,
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    description: 'Number of items to skip for pagination',
    type: Number,
    required: false,
    example: 0,
  })
  @IsOptional()
  @IsNumber()
  skip: number = 0;

  @ApiProperty({
    description: 'Number of items to return for pagination',
    type: Number,
    required: false,
    example: 10,
  })
  @IsOptional()
  @IsNumber()
  limit: number = 10;
}

