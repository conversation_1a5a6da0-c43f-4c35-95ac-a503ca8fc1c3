import { Body, Controller, Post, Get, Param, Patch, Delete } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { CreateFeatureDto, featureListDto } from "../dto/feature.dto";
import { FeatureService } from "../service/feature.service";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { PolicyAbilityProtected } from "src/policy/decorators/policy.decorator";
import { ENUM_PERMISSION_TYPE } from "src/policy/enums/policy.permissions.enum";


@ApiTags("feature")
@ApiBearerAuth()
@Controller("feature")
export class featureController {
    constructor(private readonly featureService: FeatureService) { }
    @Post('/create')
    @ApiOperation({ summary: "Create a new Feature" })
    @PolicyAbilityProtected()
    @AuthJwtAccessProtected()
    async createFeature(@Body() createFeatureDto: CreateFeatureDto, @GetUser() user: any,): Promise<{ message: String; data: any }> {
        let output = await this.featureService.createFeature(createFeatureDto, user);
        return {
            message: "Feature created",
            data: output,
        };
    }
    /* get list of the Feature according to the user */
    @Post("/list")
    @PolicyAbilityProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get all the Room of the User" })
    async featureList(@GetUser() user, @Body() featureListDto: featureListDto): Promise<any> {
        let allFeatureList = await this.featureService.getFeaturesForUser(user, featureListDto)
        return allFeatureList
    }
    @Get(":featureId")
    @PolicyAbilityProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get Feature  by ID" })
    async getFeature(@GetUser() user, @Param("featureId") featureId: string) {
        let featureDetail = await this.featureService.getFeatureById(user, featureId)
        return featureDetail;
    }
    @Patch("/update/:featureId")
    @PolicyAbilityProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Update Feature" })
    async roomUpdate(@Param("featureId") featureId: string, @GetUser() user, @Body() updateFeatureDto: CreateFeatureDto): Promise<{ message: String; data: any }> {
        let output = await this.featureService.updateFeature(updateFeatureDto, featureId, user);
        return {
            message: "Feature update Successfully",
            data: output,
        };
    }
    @Delete(":featureId")
    @PolicyAbilityProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Delete the Feature" })
    async deleteRoom(@Param("featureId") featureId: string) {
        return await this.featureService.deleteFeature(featureId);
    }

    @Patch("/status/:featureId")
    @PolicyAbilityProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Update the Feature Status" })
    async roomStatusUpdate(@Param("featureId") featureId: string, @GetUser() user, @Body() status) {
        const featureStatusUpdate = await this.featureService.updateFeatureStatus(status, featureId);

    }
}