import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { JwtModule } from "@nestjs/jwt";
import { PassportModule } from "@nestjs/passport";
import { AuthModule } from "src/auth/auth.module";
import { MailModule } from "src/mail/mail.module";
import { UtilsModule } from "src/utils/utils.module";
import { MongooseModule } from "@nestjs/mongoose";
import { FeatureService } from "./service/feature.service";
import { Feature, FeatureSchema } from "./schema/feature.schema";
import { featureController } from "./controller/feature.controller";
import { StaffProfileDetails, StaffSchema } from "src/staff/schemas/staff.schema";
import { DATABASE_PRIMARY_CONNECTION_NAME } from "src/common/database/constants/database.constant";

@Module({

    imports: [
        AuthModule,
        UtilsModule,
        MailModule,
        PassportModule.register({
            defaultStrategy: "jwt",
        }),
        JwtModule.registerAsync({
            imports: [ConfigModule],
            useFactory: async (configService: ConfigService) => ({
                global: true,
                secret: configService.get<string>("JWT_SECRET"),
                signOptions: { expiresIn: "24h" },
            }),
            inject: [ConfigService],
        }),
        MongooseModule.forFeature([
            { name: Feature.name, schema: FeatureSchema },
            { name: StaffProfileDetails.name, schema: StaffSchema },
        ],
        DATABASE_PRIMARY_CONNECTION_NAME),
    ],
    controllers: [featureController],
    providers: [FeatureService],
    exports: [FeatureService], // Exporting the service for use in other modules if needed
})
export class featureModule { }