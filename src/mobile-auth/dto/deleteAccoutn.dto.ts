import { MaxLength, IsEnum, ValidateIf, IsEmail, Length, IsNotEmpty, IsObject, IsOptional, IsMongoId } from "class-validator";
import { AuthTypes } from "../../utils/enums/auth.enum";
import { Transform } from "class-transformer";
import { ApiProperty } from "@nestjs/swagger";

export class DeleteAccountDto {
    @ApiProperty({
        required:true,
        description:"UserId of the user to be deleted",
        example:"507f191e810c19729de860ea",
    })
    @IsNotEmpty({ message: "UserId is required." })
    @IsMongoId({ message: "UserId must be a valid MongoDB ObjectId." })
    userId: string;

    @ApiProperty({
        required:true,
        description:"Role of the user to be deleted",
        example:"trainer,Customer",
    })
    @IsNotEmpty({ message: "Role is required." })
    role: string;
}