import { IsEmail, <PERSON><PERSON>num, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Not<PERSON>mpty, <PERSON><PERSON><PERSON>al,IsMongoId } from "class-validator";
import { AuthTypes } from "../../utils/enums/auth.enum";
import { Transform } from "class-transformer";
import { ApiProperty } from "@nestjs/swagger";

export class RequestOtpDto {
  @ApiProperty({
    description: 'The method of authentication for OTP request. Can be either EMAIL or MOBILE.',
    enum: AuthTypes,
    example: AuthTypes.EMAIL,
  })
  @IsNotEmpty({ message: "Authentication type is required. It can be either EMAIL or MOBILE." })
  @IsEnum([AuthTypes.EMAIL, AuthTypes.MOBILE], { message: "Authentication type must be either EMAIL or MOBILE." })
  type: string;

  @ApiProperty({
    description: 'The user email address. Required if the authentication type is EMAIL.',
    example: '<EMAIL>',
    maxLength: 255,
    required: false,
  })
  @ValidateIf(req => req.type === AuthTypes.EMAIL)
  @Transform(param => param.value.toLowerCase())
  @IsEmail({}, { message: "Email must be a valid email address." })
  @MaxLength(255, { message: "Email must be at most 255 characters long." })
  email: string;

  @ApiProperty({
    description: 'The user mobile number. Required if the authentication type is MOBILE.',
    example: '9876543210',
    //minLength: 10,
    //maxLength: 10,
    required: false,
  })
  @IsOptional()
  @ValidateIf(req => req.type === AuthTypes.MOBILE)
  //@Length(10, 10, { message: "Mobile number must be exactly 10 digits long." })
  mobile: string;
    @ApiProperty({
          description: "OrgazinationId of the selected orgazination.",
          example: "507f191e810c19729de860ea",
      })
      @IsOptional()
      @IsMongoId({ message: "Organization ID must be a valid MongoDB ObjectId." })
      organizationId: string;
}