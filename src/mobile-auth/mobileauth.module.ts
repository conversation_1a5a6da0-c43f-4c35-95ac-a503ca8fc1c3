import { AuthModule } from "src/auth/auth.module";
import { UtilsModule } from "src/utils/utils.module";
import { MailModule } from "src/mail/mail.module";
import { MongooseModule } from "@nestjs/mongoose";
import { User, UserSchema } from "src/users/schemas/user.schema";
import { Organizations, OrganizationSchema } from "src/organization/schemas/organization.schema";
import { Otp, OtpSchema } from "src/auth/schemas/otp.schema";
import { forwardRef, Module } from "@nestjs/common";
import { AppAuthController } from "./controller/appAuth.controller";
import { MobileAuthService } from "./services/appAuth.service";
import { Clients, ClientSchema } from "src/users/schemas/clients.schema";
import { PassportModule } from "@nestjs/passport";
import { JwtModule } from "@nestjs/jwt";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { StaffProfileDetails, StaffSchema } from "src/staff/schemas/staff.schema";
import { Facility, FacilitySchema } from "src/facility/schemas/facility.schema";
import { Msg91Module } from "src/message/message.module";
import { RoleModule } from "src/role/role.module";

@Module({
    imports: [
        PassportModule.register({
            defaultStrategy: "jwt",
        }),

        JwtModule.registerAsync({
            imports: [ConfigModule],
            useFactory: async (configService: ConfigService) => ({
                global: true,
                secret: configService.get<string>("JWT_SECRET"),
                signOptions: { expiresIn: "24h" },
            }),
            inject: [ConfigService],
        }),
        UtilsModule,
        MailModule,
        AuthModule,
        Msg91Module,
        RoleModule,
        MongooseModule.forFeature([
            { name: User.name, schema: UserSchema },
            { name: Organizations.name, schema: OrganizationSchema },
            { name: Otp.name, schema: OtpSchema },
            { name: Clients.name, schema: ClientSchema },
            { name: StaffProfileDetails.name, schema: StaffSchema },
            { name: Facility.name, schema: FacilitySchema },

        ]),
    ],
    controllers: [AppAuthController],
    providers: [MobileAuthService],
    exports: [MobileAuthService],
})
export class MobileAuthModule { }
