import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Msg91Service } from './service/msg91.service';

@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: Msg91Service,
      useFactory: (configService: ConfigService) => {
        return new Msg91Service(
          configService.getOrThrow<string>('MSG91_AUTH_KEY'),
          configService.getOrThrow<string>('MSG91_OTP_TEMPLATE_ID'),
          configService.getOrThrow<string>('MSG91_BASE_URL'),
        );
      },
      inject: [ConfigService],
    },
  ],
  exports: [Msg91Service],
})
export class Msg91Module {}
