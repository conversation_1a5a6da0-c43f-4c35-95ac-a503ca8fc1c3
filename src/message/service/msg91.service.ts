// src/msg91/services/msg91.service.ts
import axios from 'axios';
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class Msg91Service {
  private readonly logger = new Logger(Msg91Service.name);

  constructor(
    private readonly authKey: string,
    private readonly templateId: string,
    private readonly BASE_URL: string,
  ) {}

  async sendOtp(mobile: string,otp:string): Promise<void> {
    const url = this.BASE_URL;
    const formattedMobile = mobile.startsWith('+91') ? mobile : `+91${mobile}`;
    const params = {
      otp: otp,
      otp_length: '6',
      otp_expiry: '5', // in minutes
      template_id: this.templateId,
      mobile: `${formattedMobile}`,
      realTimeResponse: '1',
    };
    const headers = {
      'Content-Type': 'application/json',
      authkey: this.authKey,
    };
    try {
      
      const response = await axios.post(url, {}, { headers, params });
  
      if (response.data.type !== 'success') {
        throw new Error(response.data.message || 'Failed to send OTP');
      }
  
    } catch (error) {
      this.logger.error('MSG91 sendOtp error', error.response?.data || error.message);
      throw new Error(error.response?.data?.message || 'Error sending OTP');
    }
  }
  

async verifyOtp(mobile: string, otp: string): Promise<boolean> {
    const url = `${this.BASE_URL}/verify`;
    const formattedMobile = mobile.startsWith('+91') ? mobile : `+91${mobile}`;
    const params = {
      otp,
      mobile: `${formattedMobile}`,
    };

    const headers = {
      authkey: this.authKey,
    };

    try {
      const response = await axios.get(url, { params, headers });

      const isVerified = response.data?.message === 'OTP verified success';

      return isVerified;
    } catch (error) {
      const errorMsg = error.response?.data?.message || error.message;
      this.logger.error(`MSG91 verifyOtp error for ${mobile}: ${errorMsg}`);
      return false;
    }
  }
  
}
