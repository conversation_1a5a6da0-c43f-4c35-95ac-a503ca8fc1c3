import { Injectable } from '@nestjs/common';
import { SettingsOptions } from '../schemas/setting-options.schema';
import { Model, Types } from 'mongoose';
import { CreateSettingOptionsDto } from '../dto/create-setting-options.dto';
import { InjectModel } from '@nestjs/mongoose';
import { GetOrganizationOptions } from '../dto/get-organization-options.dto';
import { OrganizationSubSettings } from 'src/organizationSettings/schemas/organization-sub-settings.schema';

@Injectable()
export class SettingOptionsService {
  constructor(
    @InjectModel(SettingsOptions.name) private readonly SettingOptionsModel: Model<SettingsOptions>,
    @InjectModel(OrganizationSubSettings.name) private readonly OrganizationSubSettingsModel: Model<OrganizationSubSettings>
  ) { }

  async create(createSettingsOptionDto: CreateSettingOptionsDto[], user: any) {
    const settingOptions = await this.SettingOptionsModel.insertMany(createSettingsOptionDto);
    return true;
  }

  async getOrganizationSettings(body: GetOrganizationOptions) {
    const pipe = [
      {
        $lookup: {
          from: 'organizationsettings',
          localField: '_id',
          foreignField: 'settingOptionId',
          as: 'organizationsettings',
          pipeline: [
            {
              $match: {
                organizationId: new Types.ObjectId(body.organizationId)
              }
            }
          ]
        }
      },
      {
        $set: {
          isEnabled: {
            $cond: [
                {$arrayElemAt: ['$organizationsettings.isEnabled', 0]}, 
                true,
                false
            ]
            // $arrayElemAt: ['$organizationsettings.isEnabled', 0]
          },
        }
      },
      {
        $unset: 'organizationsettings'
      }
    ]
    const data = await this.SettingOptionsModel.aggregate(pipe);

    function getAllSubSettingKeys(data) {
      let allKeys = [];
      data.forEach(item => {
        if (item.subSettings && item.subSettings.length > 0) {
          item.subSettings.forEach(subSetting => {
            allKeys.push(subSetting.key);
          });
        }
      });
      return allKeys;
    }

    const subKeys = getAllSubSettingKeys(data);

    const allOrganizationSubSettings = await this.OrganizationSubSettingsModel.find({ organizationId: body.organizationId, key: { $in: subKeys } }, { key: 1, isActive: 1, isEnabled: 1 });

    function addIsEnabledToSubSettings(data, allOrganizationSubSettings) {
      function processSubSettings(subSettings) {
        return subSettings.map(subSetting => {
          const matchingOrgSubSetting = allOrganizationSubSettings.find(orgSubSetting => orgSubSetting.key === subSetting.key);
          const updatedSubSetting = matchingOrgSubSetting
            ? { ...subSetting, isEnabled: matchingOrgSubSetting.isEnabled }
            : subSetting;
          
          if (updatedSubSetting.subSettings && updatedSubSetting.subSettings.length > 0) {
            updatedSubSetting.subSettings = processSubSettings(updatedSubSetting.subSettings);
          }
          
          return updatedSubSetting;
        });
      }

      return data.map(item => {
        if (item.subSettings && item.subSettings.length > 0) {
          item.subSettings = processSubSettings(item.subSettings);
        }
        return item;
      });
    }
    // function addIsEnabledToSubSettings(data, allOrganizationSubSettings) {
    //   return data.map(item => {
    //     if (item.subSettings && item.subSettings.length > 0) {
    //       item.subSettings = item.subSettings.map(subSetting => {
    //         const matchingOrgSubSetting = allOrganizationSubSettings.find(orgSubSetting => orgSubSetting.key === subSetting.key);
    //         if (matchingOrgSubSetting) {
    //           return { ...subSetting, isEnabled: matchingOrgSubSetting.isEnabled };
    //         }
    //         return subSetting;
    //       });
    //     }
    //     return item;
    //   });
    // }

    const newdata = addIsEnabledToSubSettings(data, allOrganizationSubSettings);

    return { data: newdata };
  }


}
