

import { <PERSON>S<PERSON>, IsBoolean, IsNotEmpty, IsOptional, IsEnum, ValidateNested, IsArray, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { SettingOptionsKeyEnums, SubSettingOptionsKeyEnums } from '../enums/setting-options.enum';
import { SettingOptionDto } from './create-sub-setting-options.dto';


// Main DTO for the root level settings
export class GetOrganizationOptions {
  @ApiProperty({
    description: 'Organization ID',
    example: "643232323232323232323232"
  })
  @IsString()
  @IsNotEmpty({message: "Please select organization"})
  organizationId: string;
}
