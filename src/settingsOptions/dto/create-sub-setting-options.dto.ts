import { IsString, IsBoolean, IsNotEmpty, IsOptional, IsEnum, ValidateNested, IsArray, Validate, ValidatorConstraint, ValidatorConstraintInterface, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { SettingOptionsKeyEnums, SubSettingOptionsKeyEnums } from '../enums/setting-options.enum';


@ValidatorConstraint({ name: 'StartWithString' })
export class StartWithString implements ValidatorConstraintInterface {
  validate(values: string = ''): boolean {
    if (values.length) {
      return values.startsWith('/')
    }
    return false
  }
}

// Recursive DTO that can handle any level of nesting
export class SettingOptionDto {

  @ApiProperty({
    description: 'Setting ID',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  @Type(()=> Number)
  order?: number = 0;

  @ApiProperty({
    description: 'Setting ID',
    required: false,
  })
  @IsString()
  @IsOptional()
  settingId?: string;

  @ApiProperty({
    description: 'Unique key for the setting option',
    example: SubSettingOptionsKeyEnums.CLIENT_ONBOARDING_ASSESSMENT
  })
  @IsString()
  @IsNotEmpty()
  @IsEnum({ ...SettingOptionsKeyEnums, ...SubSettingOptionsKeyEnums })
  key: string;

  @ApiProperty({
    description: 'Unique key for the parent setting option',
    example: '',
    required: false
  })
  @IsString()
  @IsOptional()
  groupKey?: string;

  @ApiProperty({
    description: 'Display name of the setting option',
    example: 'Assessments'
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Detailed description of the setting option',
    example: 'Enable/disable assessment'
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Active status for the setting option',
    example: false,
    required: false
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({
    description: 'Default value for the setting option',
    example: false,
    required: false
  })
  @IsBoolean()
  @IsOptional()
  default?: boolean;

  @ApiProperty({
    description: 'Redirect url',
    example: false,
    required: false
  })
  @IsString()
  @IsOptional()
  @Validate(StartWithString, { message: 'Href url should start with `/` (root path)' })
  href?: boolean;

  @ApiProperty({
    type: [SettingOptionDto],
    required: false,
    description: 'Array of sub settings options',
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SettingOptionDto)
  subSettings?: SettingOptionDto[] = [];
}
