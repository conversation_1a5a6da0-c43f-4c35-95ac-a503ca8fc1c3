// import { IsString, IsBoolean, IsNotEmpty, Is<PERSON><PERSON>al, IsE<PERSON>, ValidateNested } from 'class-validator';
// import { CreateSubSettingOptionsDto } from './create-sub-setting-options.dto';
// import { Type } from 'class-transformer';
// import { ApiProperty, OmitType } from '@nestjs/swagger';
// import { SettingOptionsKeyEnums, SubSettingOptionsKeyEnums } from '../enums/setting-options.enum';

// export class CreateSettingOptionsDto extends OmitType(CreateSubSettingOptionsDto, ['groupKey']) {
//   @ApiProperty({
//     description: 'Unique key for the setting option',
//     example: SettingOptionsKeyEnums.CLIENT_ONBOARDING
//   })
//   @IsString()
//   @IsNotEmpty()
//   @IsEnum(SettingOptionsKeyEnums)
//   key: string;

//   @ApiProperty({
//     type: [CreateSubSettingOptionsDto],
//     required: false,
//     example: [{
//       key: SubSettingOptionsKeyEnums.CLIENT_ONBOARDING_WIGHT,
//       groupKey: SubSettingOptionsKeyEnums.CLIENT_ONBOARDING_ASSESSMENT,
//       name: 'Measurement',
//       description: 'Enable/disable measurement',
//       default: false
//     }],
//     // examples: [CreateSubSettingOptionsDto],
//     description: 'Array of sub settings options',
//   })
//   @IsOptional()
//   @ValidateNested({ each: true })
//   @Type(() => CreateSubSettingOptionsDto)
//   subSettings?: CreateSubSettingOptionsDto[] = []

// }


import { IsString, IsBoolean, IsNotEmpty, IsOptional, IsEnum, ValidateNested, IsArray } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { SettingOptionsKeyEnums, SubSettingOptionsKeyEnums } from '../enums/setting-options.enum';
import { SettingOptionDto } from './create-sub-setting-options.dto';


// Main DTO for the root level settings
export class CreateSettingOptionsDto extends SettingOptionDto {
  @ApiProperty({
    description: 'Unique key for the root setting option',
    example: SettingOptionsKeyEnums.CLIENT_ONBOARDING
  })
  @IsString()
  @IsNotEmpty()
  @IsEnum(SettingOptionsKeyEnums)
  key: string;

  // Override subSettings to add more specific Swagger documentation
  @ApiProperty({
    type: [SettingOptionDto],
    required: false,
    example: [{
      key: SubSettingOptionsKeyEnums.CLIENT_ONBOARDING_ASSESSMENT,
      groupKey: '',
      name: 'Assessments',
      description: 'Enable/disable measurement',
      isActive: false,
      subSettings: []
    }],
    description: 'Array of sub settings options',
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SettingOptionDto)
  subSettings?: SettingOptionDto[] = [];
}
