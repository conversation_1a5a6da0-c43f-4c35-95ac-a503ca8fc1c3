import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { IsBoolean, IsDate, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { Document, Schema as MongooseSchema } from 'mongoose';

// Recursive schema for sub-settings
@Schema({ timestamps: true, _id: true })
export class SubSettingsOptions extends Document {

    @Prop({ required: false, index: true, type: Number, default: 0 })
    @IsNumber()
    @IsNotEmpty()
    order: number;

    @Prop({ required: true, unique: true, type: String })
    @IsString()
    @IsNotEmpty()
    key: string;

    @Prop({ required: false, index: true, type: String, default: "" })
    @IsString()
    @IsOptional()
    groupKey: string;
  
    @Prop({ required: true, type: String })
    @IsString()
    @IsNotEmpty()
    name: string;
  
    @Prop({ type: String, default: null })
    @IsString()
    @IsOptional()
    description?: string;
  
    @Prop({ type: Boolean, default: false })
    @IsBoolean()
    isActive: boolean;

    @Prop({ type: Boolean, default: false })
    @IsBoolean()
    @IsOptional()
    default?: boolean;

    // @Prop({ type: String, required: false })
    // @IsString()
    // @IsOptional()
    // organizationId?: string;

    @Prop({ type: String, required: false })
    @IsString()
    @IsOptional()
    settingId?: string;
  
    @Prop({ type: [MongooseSchema.Types.Mixed], default: [] })
    subSettings?: SubSettingsOptions[];
  
    @Prop({ type: String, default: "", required: false })
    href?: "";
  
    @Prop({type: Date, index: true, default: undefined, required: false })
    @IsDate()
    @IsOptional()
    deletedAt?: Date;
}

export const SubSettingsOptionsSchema = SchemaFactory.createForClass(SubSettingsOptions);
export type SubSettingsOptionsDocument = SubSettingsOptions & Document;
