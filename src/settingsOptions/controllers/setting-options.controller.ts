import { Controller, Get, Post, Body, Patch, Param, Delete, Query, UseGuards, HttpCode, HttpStatus, Put, BadRequestException } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, <PERSON>pi<PERSON><PERSON>Property } from "@nestjs/swagger";
import { Types } from "mongoose";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { AuthGuard } from "@nestjs/passport";
import { Roles } from "src/auth/decorators/roles.decorator";
import { RolesGuard } from "src/auth/roles.guard";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { SettingOptionsService } from "../services/setting-options.service";
import { CreateSettingOptionsDto } from "../dto/create-setting-options.dto";
import { GetOrganizationOptions } from "../dto/get-organization-options.dto";

@ApiTags("Settings Options")
@ApiBearerAuth()
@Controller('settings-options')
export class SettingsController {
    constructor(private readonly SettingOptionsService: SettingOptionsService) {}

    @Post("/")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN, ENUM_ROLE_TYPE.ORGANIZATION)
    @ApiOperation({ summary: "Create a new settings option" })
    @ApiResponse({ status: 201, description: "Settings option created successfully" })
    async create(@Body() createSettingsOptionDto: CreateSettingOptionsDto[], @GetUser() user: any) {
        const data = await this.SettingOptionsService.create(createSettingsOptionDto, user);
        return {
            message: "Settings option created successfully",
            data: data,
        };
    }

    @Post("/organization-settings")
    @UseGuards(AuthGuard(), RolesGuard)
    @Roles(ENUM_ROLE_TYPE.SUPER_ADMIN, ENUM_ROLE_TYPE.ORGANIZATION)
    @ApiOperation({ summary: "Create a new settings option" })
    @ApiResponse({ status: 201, description: "Settings option created successfully" })
    async getOrganizationSettings(@Body() body: GetOrganizationOptions) {
        const data = await this.SettingOptionsService.getOrganizationSettings(body);
        return {
            message: "Settings option created successfully",
            data: data,
        };
    }
}
