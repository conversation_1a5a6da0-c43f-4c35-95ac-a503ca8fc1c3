import { Request } from 'express';
import { ResponsePagingMetadataPaginationRequestDto } from 'src/common/response/dtos/response.paging.dto';
import { IRequestSession } from './request.session.interface';
import { Facility } from 'src/facility/schemas/facility.schema';
import { ENUM_PERMISSION_TYPE } from 'src/policy/enums/policy.permissions.enum';
import { IUserDocument } from 'src/users/interfaces/user.interface';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';
import { AuthJwtAccessPayloadDto } from 'src/auth/dto/jwt/auth.jwt.access-payload.dto';

/**
 * Application request interface.
 * Extends express request. Contains additional properties for application use
 */
export interface IRequestApp extends Request {
    /**
     * Express session
     * @type {IRequestSession}
     */
    session: IRequestSession;

    /**
     * Session data
     * @type {string}
     */
    __sessionId?: string;
    __isSessionActive?: boolean;
    __delegateSessionId?: string;
    __isDelegateSessionActive?: boolean;

    /**
     * User who is accessing the system using JWT
     * Populated based on AuthJwtPayload
     * @type {AuthJwtAccessPayloadDto}
     */
    user?: AuthJwtAccessPayloadDto;

    /**
     * Organization ID of the logged in user
     * Populated based on AuthJwtPayload
     */
    __organizationId?: any;

    /**
     * User who is accessing the system using JWT
     * Populated based on AuthJwtPayload
     *  @type {IUserDocument}
     */
    __user?: IUserDocument;

    /**
     * User who is accessing the system using PIN
     * Populated based on SessionActiveGuard
     *  @type {IUserDocument}
     */
    __delegateUser?: IUserDocument;

    /**
     * Set of permissions based on user or delegate user
     * Populated based on user role and assigned policies
     * @type {Set<ENUM_PERMISSION_TYPE | ENUM_ROLE_TYPE>}
     */
    __permissionSet: Set<ENUM_PERMISSION_TYPE | ENUM_ROLE_TYPE>;

    /**
     * List of facilities accessible to the user
     * Populated based on user role and staff profile
     * @type {Facility[]}
     */
    __facilities?: Facility[],

    // System
    __language: string;
    __version: string;
    __pagination?: ResponsePagingMetadataPaginationRequestDto;
}
