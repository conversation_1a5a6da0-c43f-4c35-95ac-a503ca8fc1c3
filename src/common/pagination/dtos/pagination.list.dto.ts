import { ApiHideProperty, ApiProperty } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';
import { Transform, Type } from 'class-transformer'
import { ENUM_PAGINATION_ORDER_DIRECTION_TYPE } from 'src/common/pagination/enums/pagination.enum';
import { IPaginationOrder } from 'src/common/pagination/interfaces/pagination.interface';

export class PaginationListDto {
    @ApiHideProperty()
    _search: Record<string, any>;

    @ApiHideProperty()
    _limit: number;

    @ApiHideProperty()
    _offset: number;

    @ApiHideProperty()
    _order: IPaginationOrder;

    @ApiHideProperty()
    _availableOrderBy: string[];

    @ApiHideProperty()
    _availableOrderDirection: ENUM_PAGINATION_ORDER_DIRECTION_TYPE[];

    @ApiProperty({
        required: false,
        description: 'Search keyword',
        example: 'Search',
    })
    @IsOptional()
    search?: string;

    @ApiHideProperty()
    @IsOptional()
    @Type(() => Number)
    perPage?: number = this.pageSize;

    @ApiProperty({
        required: false,
        description: "Per page data count",
        example: 20,
    })
    @IsOptional()
    @Type(() => Number)
    pageSize?: number = 20;

    @ApiProperty({
        required: false,
        description: 'Page number',
        example: 1,
    })
    @IsOptional()
    page?: number = 1;

    @ApiProperty({
        required: false,
        description: 'Order by',
        example: 'createdAt',
    })
    @IsOptional()
    orderBy?: string;

    @ApiProperty({
        required: false,
        description: 'Order direction',
        example: ENUM_PAGINATION_ORDER_DIRECTION_TYPE.DESC,
        enum: ENUM_PAGINATION_ORDER_DIRECTION_TYPE,
        default: ENUM_PAGINATION_ORDER_DIRECTION_TYPE.DESC,
    })
    @IsOptional()
    orderDirection?: ENUM_PAGINATION_ORDER_DIRECTION_TYPE;
}
