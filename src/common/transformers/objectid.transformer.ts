import { Transform } from 'class-transformer';
import { Types } from 'mongoose';

/**
 * Debug utility to log object structure for troubleshooting
 * @param obj Object to debug
 * @param label Label for the debug output
 */
export function debugObjectStructure(obj: any, label: string = 'Object'): void {
    if (process.env.NODE_ENV === 'development') {
        console.log(`\n=== ${label} Debug ===`);
        console.log('Type:', typeof obj);
        console.log('Constructor:', obj?.constructor?.name);
        console.log('Is Array:', Array.isArray(obj));
        console.log('Is Date:', obj instanceof Date);
        console.log('Is ObjectId:', obj instanceof Types.ObjectId);
        console.log('Keys:', obj && typeof obj === 'object' ? Object.keys(obj) : 'N/A');
        console.log('Sample values:');
        if (obj && typeof obj === 'object' && !Array.isArray(obj)) {
            Object.entries(obj).slice(0, 5).forEach(([key, value]) => {
                console.log(`  ${key}:`, typeof value, value instanceof Date ? '(Date)' : '', value instanceof Types.ObjectId ? '(ObjectId)' : '');
            });
        }
        console.log('===================\n');
    }
}

/**
 * Transform decorator to convert MongoDB ObjectId to string
 * Use this decorator on properties that should be converted from ObjectId to string
 */
export function TransformObjectId() {
    return Transform(({ value }) => {
        if (value instanceof Types.ObjectId) {
            return value.toString();
        }
        if (typeof value === 'object' && value !== null && value._id) {
            return value._id.toString();
        }
        return value?.toString() || value;
    });
}

/**
 * Transform decorator for nested objects with ObjectId
 * Use this for populated fields that contain ObjectId properties
 */
export function TransformNestedObjectId() {
    return Transform(({ value }) => {
        if (!value) return value;

        if (Array.isArray(value)) {
            return value.map(item => {
                if (typeof item === 'object' && item !== null) {
                    const transformed = { ...item };
                    if (transformed._id instanceof Types.ObjectId) {
                        transformed._id = transformed._id.toString();
                    }
                    return transformed;
                }
                return item;
            });
        }

        if (typeof value === 'object' && value !== null) {
            const transformed = { ...value };
            if (transformed._id instanceof Types.ObjectId) {
                transformed._id = transformed._id.toString();
            }
            return transformed;
        }

        return value;
    });
}

/**
 * Utility function to transform MongoDB document to plain object with string IDs
 * @param doc MongoDB document or plain object
 * @param debug Enable debug logging (only in development)
 * @returns Plain object with ObjectIds converted to strings
 */
export function transformMongoDocument(doc: any, debug: boolean = false): any {
    if (!doc) return doc;

    if (debug) {
        debugObjectStructure(doc, 'Input Document');
    }

    // Convert to plain object if it's a Mongoose document
    const plainObject = doc.toObject ? doc.toObject() : doc;

    if (debug) {
        debugObjectStructure(plainObject, 'Plain Object');
    }

    // Transform the object recursively
    const transformed = transformObjectIds(plainObject);

    if (debug) {
        debugObjectStructure(transformed, 'Transformed Object');
    }

    return transformed;
}

/**
 * Recursively transform ObjectIds to strings in an object
 * @param obj Object to transform
 * @returns Transformed object with string IDs
 */
function transformObjectIds(obj: any): any {
    if (!obj || typeof obj !== 'object') {
        return obj;
    }

    if (obj instanceof Types.ObjectId) {
        return obj.toString();
    }

    // Preserve Date objects as-is (handle both Date instances and date-like objects)
    if (obj instanceof Date) {
        return obj;
    }

    // Handle date-like objects that might come from MongoDB/Mongoose
    if (obj && typeof obj === 'object' && obj.constructor && obj.constructor.name === 'Date') {
        return obj;
    }

    // Handle MongoDB ISODate objects (they might appear as objects with specific structure)
    if (obj && typeof obj === 'object' && (obj as any).$date) {
        return new Date((obj as any).$date);
    }

    // Handle Mongoose date objects that might be serialized differently
    if (obj && typeof obj === 'object' && (obj as any)._bsontype === 'Date') {
        return new Date((obj as any).value || obj);
    }

    // Handle ISODate strings that should remain as Date objects
    if (typeof obj === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(obj)) {
        return new Date(obj);
    }

    // Handle timestamp numbers
    if (typeof obj === 'number' && obj > 1000000000000 && obj < 9999999999999) {
        return new Date(obj);
    }

    // Handle arrays
    if (Array.isArray(obj)) {
        return obj.map(item => transformObjectIds(item));
    }

    // Handle plain objects
    const transformed: any = {};
    for (const [key, value] of Object.entries(obj)) {
        if (value instanceof Types.ObjectId) {
            transformed[key] = value.toString();
        } else if (value instanceof Date) {
            // Preserve Date objects
            transformed[key] = value;
        } else if (value && typeof value === 'object' && value.constructor && value.constructor.name === 'Date') {
            // Handle date-like objects from MongoDB/Mongoose
            transformed[key] = value;
        } else if (value && typeof value === 'object' && (value as any).$date) {
            // Handle MongoDB ISODate objects
            transformed[key] = new Date((value as any).$date);
        } else if (value && typeof value === 'object' && (value as any)._bsontype === 'Date') {
            // Handle Mongoose date objects
            transformed[key] = new Date((value as any).value || value);
        } else if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value)) {
            // Handle ISO date strings
            transformed[key] = new Date(value);
        } else if (typeof value === 'number' && value > 1000000000000 && value < 9999999999999) {
            // Handle timestamp numbers
            transformed[key] = new Date(value);
        } else if (typeof value === 'object' && value !== null) {
            transformed[key] = transformObjectIds(value);
        } else {
            transformed[key] = value;
        }
    }

    return transformed;
}
