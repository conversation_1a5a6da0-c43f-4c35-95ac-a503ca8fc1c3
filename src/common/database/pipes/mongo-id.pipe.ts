import { PipeTransform, Injectable, BadRequestException } from '@nestjs/common';
import { Types } from 'mongoose';
import { IDatabaseObjectId } from '../interfaces/database.objectid.interface';


@Injectable()
export class MongoIdPipeTransform implements PipeTransform<string, IDatabaseObjectId> {
    transform(value: string): Types.ObjectId {
        if (!Types.ObjectId.isValid(value)) {
            throw new BadRequestException('Invalid id');
        }

        return new Types.ObjectId(value);
    }
}

@Injectable()
export class MongoIdPipeTransformOrNull implements PipeTransform<string, IDatabaseObjectId> {
    transform(value: string): Types.ObjectId {
        if (!Types.ObjectId.isValid(value)) {
            return null
        }

        return new Types.ObjectId(value);
    }
}