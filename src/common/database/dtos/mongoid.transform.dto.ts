import { TransformFnParams } from "class-transformer";
import { BadRequestException} from "@nestjs/common";
import { Transform } from "class-transformer";
import { Types } from "mongoose";


/**
 * Transforms a string to MongoDB ObjectId
 * @param params Transform function parameters
 * @returns MongoDB ObjectId
 */
const MongoIdTransformer = (params: TransformFnParams): Types.ObjectId => {
    const value = params.value;

    // Handle undefined or null values
    if (value === undefined || value === null) {
        return undefined;
    }

    // Handle ObjectId instances
    if (value instanceof Types.ObjectId) {
        return value;
    }

    // Handle string values
    if (typeof value === 'string') {
        if (!Types.ObjectId.isValid(value)) {
            throw new BadRequestException('Invalid selected data');
        }
        return new Types.ObjectId(value);
    }

    // Handle other cases
    throw new BadRequestException('Cannot transform value to MongoDB ObjectId');
};

/**
 * Decorator that transforms a string to MongoDB ObjectId
 * Usage: @TransformMongoId()
 */
export function TransformMongoId() {
    return Transform(MongoIdTransformer);
}