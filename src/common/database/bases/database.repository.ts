import {
    Types,
    Model,
    PipelineStage,
    PopulateOptions,
    UpdateQuery,
    UpdateWithAggregationPipeline,
} from 'mongoose';
import {
    IDatabaseAggregateOptions,
    IDatabaseCreateManyOptions,
    IDatabaseCreateOptions,
    IDatabaseDeleteManyOptions,
    IDatabaseDeleteOptions,
    IDatabaseDocument,
    IDatabaseFindAllAggregateOptions,
    IDatabaseFindAllOptions,
    IDatabaseFindOneOptions,
    IDatabaseGetTotalOptions,
    IDatabaseOptions,
    IDatabaseSaveOptions,
    IDatabaseUpdateManyOptions,
    IDatabaseUpdateOptions,
} from 'src/common/database/interfaces/database.interface';
import { UpdateResult, DeleteResult, InsertManyR<PERSON>ult } from 'mongodb';
import { ENUM_PAGINATION_ORDER_DIRECTION_TYPE } from 'src/common/pagination/enums/pagination.enum';
import { DatabaseEntityBase } from 'src/common/database/bases/database.entity';
import { FacilityService } from 'src/facility/services/facility.service';

export class DatabaseRepositoryBase<
    Entity extends DatabaseEntityBase,
    EntityDocument extends IDatabaseDocument<Entity>,
> {
    protected readonly _repository: Model<Entity>;
    readonly _join?: PopulateOptions | (string | PopulateOptions)[];

    constructor(
        repository: Model<Entity>,
        options?: PopulateOptions | (string | PopulateOptions)[]
    ) {
        this._repository = repository;
        this._join = options;
    }

    // Find
    async findAll<T = EntityDocument>(
        find?: Record<string, any>,
        options?: IDatabaseFindAllOptions
    ): Promise<T[]> {

        if (!options?.withDeleted) {
            find.deletedAt = { $exists: false };
        }

        const repository = this._repository.find<T>(find);

        if (options?.select) {
            repository.select(options.select);
        }

        if (options?.paging) {
            repository.limit(options.paging.limit).skip(options.paging.offset);
        }

        if (options?.order) {
            repository.sort(options.order);
        }

        if (options?.join) {
            repository.populate(
                (typeof options.join === 'boolean' && options.join
                    ? this._join
                    : options.join) as
                | PopulateOptions
                | (string | PopulateOptions)[]
            );
        }

        if (options?.session) {
            repository.session(options.session);
        }

        return repository.exec();
    }

    async findOne<T = EntityDocument>(
        find: Record<string, any>,
        options?: IDatabaseFindOneOptions
    ): Promise<T> {

        if (!options?.withDeleted) {
            find.deletedAt = { $exists: false };
        }

        const repository = this._repository.findOne<T>(find);

        if (options?.select) {
            repository.select(options.select);
        }

        if (options?.join) {
            repository.populate(
                (typeof options.join === 'boolean' && options.join
                    ? this._join
                    : options.join) as
                | PopulateOptions
                | (string | PopulateOptions)[]
            );
        }

        if (options?.order) {
            repository.sort(options.order);
        }

        if (options?.session) {
            repository.session(options.session);
        }

        return repository.exec();
    }

    async findOneById<T = EntityDocument>(
        _id: Types.ObjectId,
        options?: IDatabaseFindOneOptions
    ): Promise<T> {
        const find: Record<string, any> = { _id };

        if (!options?.withDeleted) {
            find.deletedAt = { $exists: false };
        }

        const repository = this._repository.findOne<T>(find);

        if (options?.select) {
            repository.select(options.select);
        }

        if (options?.join) {
            repository.populate(
                (typeof options.join === 'boolean' && options.join
                    ? this._join
                    : options.join) as
                | PopulateOptions
                | (string | PopulateOptions)[]
            );
        }

        if (options?.order) {
            repository.sort(options.order);
        }

        if (options?.session) {
            repository.session(options.session);
        }

        return repository.exec();
    }

    async findOneAndLock<T = EntityDocument>(
        find: Record<string, any>,
        options?: IDatabaseFindOneOptions
    ): Promise<T> {

        if (!options?.withDeleted) {
            find.deletedAt = { $exists: false };
        }

        const repository = this._repository.findOneAndUpdate<T>(
            find,
            {
                new: true,
                useFindAndModify: false,
            }
        );

        if (options?.select) {
            repository.select(options.select);
        }

        if (options?.join) {
            repository.populate(
                (typeof options.join === 'boolean' && options.join
                    ? this._join
                    : options.join) as
                | PopulateOptions
                | (string | PopulateOptions)[]
            );
        }

        if (options?.order) {
            repository.sort(options.order);
        }

        if (options?.session) {
            repository.session(options.session);
        }

        return repository.exec();
    }

    async findOneByIdAndLock<T = EntityDocument>(
        _id: Types.ObjectId,
        options?: IDatabaseFindOneOptions
    ): Promise<T> {

        const find: Record<string, any> = { _id };

        if (!options?.withDeleted) {
            find.deletedAt = { $exists: false };
        }

        const repository = this._repository.findOneAndUpdate<T>(
            find,
            {
                new: true,
                useFindAndModify: false,
            }
        );

        if (options?.select) {
            repository.select(options.select);
        }

        if (options?.join) {
            repository.populate(
                (typeof options.join === 'boolean' && options.join
                    ? this._join
                    : options.join) as
                | PopulateOptions
                | (string | PopulateOptions)[]
            );
        }

        if (options?.order) {
            repository.sort(options.order);
        }

        if (options?.session) {
            repository.session(options.session);
        }

        return repository.exec();
    }

    async getTotal(
        find?: Record<string, any>,
        options?: IDatabaseGetTotalOptions
    ): Promise<number> {

        if (!options?.withDeleted) {
            find.deletedAt = { $exists: false };
        }

        const repository = this._repository.countDocuments(find);

        if (options?.join) {
            repository.populate(
                (typeof options.join === 'boolean' && options.join
                    ? this._join
                    : options.join) as
                | PopulateOptions
                | (string | PopulateOptions)[]
            );
        }

        if (options?.session) {
            repository.session(options.session);
        }

        return repository;
    }

    async exists(
        find: Record<string, any>,
        options?: IDatabaseOptions
    ): Promise<boolean> {

        if (!options?.withDeleted) {
            find.deletedAt = { $exists: false };
        }

        const repository = this._repository.exists(find);

        if (options?.join) {
            repository.populate(
                (typeof options.join === 'boolean' && options.join
                    ? this._join
                    : options.join) as
                | PopulateOptions
                | (string | PopulateOptions)[]
            );
        }

        if (options?.session) {
            repository.session(options.session);
        }

        const result = await repository;
        return result ? true : false;
    }

    async create<T extends Entity>(
        data: T,
        options?: IDatabaseCreateOptions
    ): Promise<EntityDocument> {
        const created = await this._repository.create([data], options);

        return created[0] as any;
    }

    // Action
    async update(
        find: Record<string, any>,
        data: UpdateQuery<Entity> | UpdateWithAggregationPipeline,
        options?: IDatabaseUpdateOptions
    ): Promise<EntityDocument> {

        if (!options?.withDeleted) {
            find.deletedAt = { $exists: false };
        }

        return this._repository.findOneAndUpdate(
            find,
            data,
            {
                ...options,
                new: true,
            }
        );
    }

    async delete(
        find: Record<string, any>,
        options?: IDatabaseDeleteOptions
    ): Promise<EntityDocument> {

        if (!options?.withDeleted) {
            find.deletedAt = { $exists: false }
        }

        return this._repository.findOneAndDelete(
            find,
            {
                ...options,
                new: false,
            }
        );
    }

    async save(
        repository: EntityDocument,
        options?: IDatabaseSaveOptions
    ): Promise<EntityDocument> {
        return repository.save(options);
    }

    async join<T = any>(
        repository: EntityDocument,
        joins: PopulateOptions | (string | PopulateOptions)[]
    ): Promise<T> {
        return repository.populate(joins);
    }

    // Soft delete
    async softDelete(
        repository: EntityDocument,
        options?: IDatabaseOptions
    ): Promise<EntityDocument> {
        repository.deletedAt = new Date();
        // repository.deleted = true;

        return repository.save(options);
    }

    async restore(
        repository: EntityDocument,
        options?: IDatabaseSaveOptions
    ): Promise<EntityDocument> {
        repository.deletedAt = undefined;
        // repository.deleted = false;

        return repository.save(options);
    }

    // Bulk
    async createMany<T = Entity>(
        data: T[],
        options?: IDatabaseCreateManyOptions
    ): Promise<InsertManyResult<Entity>> {
        return this._repository.insertMany(data as any, {
            ...options,
            rawResult: true,
        });
    }

    async updateMany<T = Entity>(
        find: Record<string, any>,
        data: T,
        options?: IDatabaseUpdateManyOptions
    ): Promise<UpdateResult<Entity>> {

        if (!options?.withDeleted) {
            find.deletedAt = { $exists: false };
        }

        return this._repository.updateMany(
            find,
            {
                $set: data,
            },
            { ...options, rawResult: true }
        );
    }

    async updateManyRaw(
        find: Record<string, any>,
        data: UpdateQuery<Entity> | UpdateWithAggregationPipeline,
        options?: IDatabaseUpdateManyOptions
    ): Promise<UpdateResult<Entity>> {

        if (!options?.withDeleted) {
            find.deletedAt = { $exists: false };
        }

        return this._repository.updateMany(
            find,
            data,
            { ...options, rawResult: true }
        );
    }

    async deleteMany(
        find: Record<string, any>,
        options?: IDatabaseDeleteManyOptions
    ): Promise<DeleteResult> {

        if (!options?.withDeleted) {
            find.deletedAt = { $exists: false };
        }

        return this._repository.deleteMany(
            find,
            { ...options, rawResult: true }
        );
    }

    async softDeleteMany(
        find: Record<string, any>,
        options?: IDatabaseOptions
    ): Promise<UpdateResult<Entity>> {

        if (!options?.withDeleted) {
            find.deletedAt = { $exists: false };
        }

        return this._repository.updateMany(
            find,
            {
                $set: {
                    deletedAt: new Date(),
                    deleted: true,
                },
            },
            { ...options, rawResult: true }
        );
    }

    async restoreMany(
        find: Record<string, any>,
        options?: IDatabaseOptions
    ): Promise<UpdateResult<Entity>> {

        if (!options?.withDeleted) {
            find.deletedAt = { $exists: false };
        }

        return this._repository.updateMany(
            find,
            {
                $set: {
                    deletedAt: undefined,
                    deleted: false,
                    // deletedBy: undefined,
                },
            },
            { ...options, rawResult: true }
        );
    }

    // Raw
    async aggregate<
        AggregatePipeline extends PipelineStage,
        AggregateResponse = any,
    >(
        pipelines: AggregatePipeline[],
        options?: IDatabaseAggregateOptions
    ): Promise<AggregateResponse[]> {
        if (!Array.isArray(pipelines)) {
            throw new Error('Must in array');
        }

        const newPipelines: PipelineStage[] = [
            ...pipelines,
        ];

        if (!options?.withDeleted) {
            newPipelines.unshift({
                $match: {
                    deletedAt: { $exists: false },
                },
            });
        }

        const aggregate =
            this._repository.aggregate<AggregateResponse>(newPipelines);

        if (options?.session) {
            aggregate.session(options?.session);
        }

        return aggregate;
    }

    async findAllAggregate<
        AggregatePipeline extends PipelineStage,
        AggregateResponse = any,
    >(
        pipelines: AggregatePipeline[],
        options?: IDatabaseFindAllAggregateOptions
    ): Promise<AggregateResponse[]> {
        if (!Array.isArray(pipelines)) {
            throw new Error('Must in array');
        }

        const newPipelines: PipelineStage[] = [
            ...pipelines,
        ];

        if (!options?.withDeleted) {
            newPipelines.unshift({
                $match: {
                    deletedAt: { $exists: false },
                },
            });
        }

        if (options?.order) {
            const keysOrder = Object.keys(options?.order);
            newPipelines.push({
                $sort: keysOrder.reduce(
                    (a, b) => ({
                        ...a,
                        [b]:
                            options?.order[b] ===
                                ENUM_PAGINATION_ORDER_DIRECTION_TYPE.ASC
                                ? 1
                                : -1,
                    }),
                    {}
                ),
            });
        }

        if (options?.paging) {
            newPipelines.push(
                {
                    $limit: options.paging.limit + options.paging.offset,
                },
                { $skip: options.paging.offset }
            );
        }

        const aggregate =
            this._repository.aggregate<AggregateResponse>(newPipelines);

        if (options?.session) {
            aggregate.session(options?.session);
        }

        return aggregate;
    }

    async getTotalAggregate<AggregatePipeline extends PipelineStage>(
        pipelines: AggregatePipeline[],
        options?: IDatabaseAggregateOptions
    ): Promise<number> {
        if (!Array.isArray(pipelines)) {
            throw new Error('Must in array');
        }

        const newPipelines: PipelineStage[] = [
            ...pipelines,
            {
                $group: {
                    _id: null,
                    count: { $sum: 1 },
                },
            },
        ];

        if (!options?.withDeleted) {
            newPipelines.unshift({
                $match: {
                    deletedAt: { $exists: false },
                },
            });
        }

        const aggregate = this._repository.aggregate(newPipelines);

        if (options?.session) {
            aggregate.session(options?.session);
        }

        const raw = await aggregate;
        return raw && raw.length > 0 ? raw[0].count : 0;
    }

    async distinct<T = any>(field: string): Promise<T[]> {
        return this._repository.distinct(field);
    }

    async model(): Promise<Model<Entity>> {
        return this._repository;
    }

    async startTransaction(): Promise<any> {
        return this._repository.startSession();
    }
}
