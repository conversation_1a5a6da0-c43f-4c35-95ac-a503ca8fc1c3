import { RedisClientType, RedisModules, RedisScripts } from '@redis/client';

export type RedisClient = RedisClientType<RedisModules, Record<string, never>>;

export type RedisPipelineCommand = 
    | ['set', string, any, SetCommandOptions?]
    | ['get', string]
    | ['del', string | string[]]
    | ['hset', string, string, any]
    | ['hget', string, string]
    | ['hmset', string, Record<string, any>]
    | ['hmget', string, string[]]
    | ['hdel', string, string | string[]]
    | ['hgetall', string]
    | ['hexists', string, string]
    | ['hkeys', string]
    | ['hvals', string]
    | ['hlen', string]
    | ['expire', string, number];

export interface SetCommandOptions {
    EX?: number;      // seconds
    PX?: number;      // milliseconds
    EXAT?: number;    // timestamp-seconds
    PXAT?: number;    // timestamp-milliseconds
    NX?: boolean;     // Only set if key does not exist
    XX?: boolean;     // Only set if key exists
    KEEPTTL?: boolean;// Retain the TTL associated with the key
}

export interface CacheOptions {
    ttl?: number;
    prefix?: string;
}

export interface HashOperationOptions {
    ttl?: number;
}

export interface BatchOperationOptions {
    pipeline?: boolean;
    ttl?: number;
}

// For structured return types
export interface CacheSetResult {
    success: boolean;
    error?: Error;
}

export interface CacheGetResult<T> {
    data: T | null;
    error?: Error;
}

export interface HashOperationResult<T> {
    data: T;
    error?: Error;
}
