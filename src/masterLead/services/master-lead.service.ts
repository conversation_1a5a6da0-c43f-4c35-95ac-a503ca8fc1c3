import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { MasterLead } from '../schemas/master-lead.schema';

@Injectable()
export class MasterLeadService {
  constructor(
    @InjectModel(MasterLead.name) private masterLeadModel: Model<MasterLead>,
  ) {}

  async create(mappedData: Partial<MasterLead>): Promise<MasterLead> {
    return this.masterLeadModel.create(mappedData);
  }

  async findAll(params: {
    page?: number;
    limit?: number;
    search?: string;
    fromDate?: string;
    toDate?: string;
  }): Promise<{
    current_page: number;
    total_pages: number;
    total_rows: number;
    data: MasterLead[];
  }> {
    const {
      page = 1,
      limit = 10,
      search = '',
      fromDate,
      toDate,
    } = params;
  
    const skip = (page - 1) * limit;
  
    const query: any = {};
  
    // Search by name, email, phone or source
    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { mobile: { $regex: search, $options: 'i' } },
        { source: { $regex: search, $options: 'i' } },
      ];
    }
  
    // Date range filter
    if (fromDate || toDate) {
      query.createdAt = {};
      if (fromDate) query.createdAt.$gte = new Date(fromDate);
      if (toDate) query.createdAt.$lte = new Date(toDate);
    }
  
    const [total_rows, data] = await Promise.all([
      this.masterLeadModel.countDocuments(query).exec(),
      this.masterLeadModel
        .find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
    ]);
  
    const total_pages = Math.ceil(total_rows / limit);
  
    return {
      current_page: page,
      total_pages,
      total_rows,
      data,
    };
  }
  
}
