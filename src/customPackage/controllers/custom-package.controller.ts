import { Body, Controller, Get, Param, <PERSON>, Post } from "@nestjs/common";
import { CustomPackageService } from "../services/custom-package.service";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { CreateCustomPackageDto, CustomPackageListDto, UpdateCustomPackageDto } from "src/customPackage/dto/custom-package.dto";
import { AuthJwtAccessProtected, AuthSessionProtected } from "src/auth/decorators/auth.jwt.decorator";
import { PolicyAbilityProtected } from "src/policy/decorators/policy.decorator";
import { ENUM_PERMISSION_TYPE } from "src/policy/enums/policy.permissions.enum";
import { GetOrganizationId } from "src/organization/decorators/organization.decorator";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { MongoIdPipeTransform } from "src/common/database/pipes/mongo-id.pipe";

@ApiTags("custom-package")
@ApiBearerAuth()
@Controller("custom-package")
export class CustomPackageController {
    constructor(private customPackageService: CustomPackageService) { }

    @Post("/create")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_WRITE)
    @AuthSessionProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Create Custom Package" })
    async createCourseScheduling(
        @Body() createCustomPackageDto: CreateCustomPackageDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const output = await this.customPackageService.createCustomPackage(createCustomPackageDto, organizationId);
        return output;
    }

    @Patch("/update")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_UPDATE)
    @AuthSessionProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Update Custom Package" })
    async updateCourseScheduling(
        @Body() updateCustomPackageDto: UpdateCustomPackageDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const output = await this.customPackageService.updateCustomPackage(updateCustomPackageDto, organizationId);
        return output;
    }

    @Post("/list")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Custom Package list" })
    async getSchedulingList(
        @Body() customPackageListDto: CustomPackageListDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        const output = await this.customPackageService.getCustomPackageList(organizationId, customPackageListDto);
        return output;
    }

    @Get("/details/:id")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.PRICING_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Custom Package Details" })
    async getSchedulingDetails(
        @GetUser() user: any,
        @Param("id", MongoIdPipeTransform) id: IDatabaseObjectId,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ): Promise<any> {
        let output = await this.customPackageService.getCustomPackageDetails(organizationId, id);
        return output;
    }
}
