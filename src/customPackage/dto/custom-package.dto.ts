import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsBoolean, IsEnum, IsMongoId, IsNotEmpty, IsNumber, IsOptional, IsString, Max, Min, ValidateIf, ValidateNested } from "class-validator";
import { DiscountType } from "src/utils/enums/discount.enum";

export class DiscountDto {
    @ApiProperty({
        description: "Type of discount",
        enum: DiscountType,
        example: DiscountType.FLAT,
        required: false,
    })
    @IsOptional()
    @IsEnum(DiscountType, { message: "Invalid discount type" })
    type?: DiscountType;

    @ApiProperty({
        description: "Value of the discount",
        example: 50,
        required: false,
    })
    @IsOptional()
    @IsNumber({}, { message: "Invalid discount value" })
    @Min(0, { message: "Discount value cannot be less than 0" })
    value?: number;
}

export class CreateCustomPackageDto {
    @ApiProperty({
        description: "The ID of the Facility.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsNotEmpty({ message: "Facility Id is required" })
    @IsMongoId({ message: "Facility details is Invalid" })
    facilityId: string;

    @ApiProperty({
        description: "The name of the custom package.",
        example: "Animal Flow Select",
        required: true,
    })
    @IsString({ message: "Invalid type of custom package name" })
    @IsNotEmpty({ message: "Custom package name cannot be empty" })
    name: string;

    @ApiProperty({
        description: "Quantity of sessions | Must be a number",
        example: 299,
        required: true,
    })
    @IsNumber({}, { message: "Invalid quantity type" })
    quantity: Number;

    @ApiProperty({
        description: "Price excluding GST | Must be a number",
        example: 299,
        required: true,
    })
    @IsNumber({}, { message: "Invalid price type" })
    unitPrice: Number;

    @ApiProperty({
        description: "Total Amount | Must be a number",
        example: 299,
        required: true,
    })
    @IsNumber({}, { message: "Invalid total type" })
    total: Number;

    @ApiProperty({
        description: "Discount details for this custom package",
        required: false,
    })
    @ValidateNested({ message: "Invalid discount object" })
    @Type(() => DiscountDto)
    @IsOptional()
    discount?: DiscountDto;

    @ApiProperty({
        description: "Package is taxable or not | Boolean",
        example: false,
        required: true,
    })
    @IsBoolean({ message: "Invalid value for isTaxable" })
    isTaxable: Boolean;

    @ApiProperty({
        description: "Tax on custom package | Must be a number",
        example: 12,
        required: true,
    })
    @IsNumber({}, { message: "Invalid tax type" })
    @Min(0, { message: "Tax percentage cannot be less than 0" })
    @Max(40, { message: "Tax percentage cannot be greater than 40" })
    tax: Number;

    @ApiProperty({
        description: "HSN/SAC code | Must be a string",
        example: "998314",
        required: false,
    })
    @ValidateIf((o) => o.tax > 0)
    @IsString({ message: "Invalid HSN/SAC code" })
    @IsNotEmpty({ message: "HSN/SAC code cannot be empty" })
    @IsOptional()
    hsnOrSacCode: string;
}

export class UpdateCustomPackageDto {
    @ApiProperty({
        description: "The ID of the Package.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsNotEmpty({ message: "Package Id is required" })
    @IsMongoId({ message: "Package details is Invalid" })
    packageId: string;

    @ApiProperty({
        description: "The ID of the Facility.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsNotEmpty({ message: "Facility Id is required" })
    @IsMongoId({ message: "Facility details is Invalid" })
    facilityId: string;

    @ApiProperty({
        description: "The name of the custom package.",
        example: "Animal Flow Select",
        required: true,
    })
    @IsString({ message: "Invalid type of custom package name" })
    @IsNotEmpty({ message: "Custom package name cannot be empty" })
    name: string;

    @ApiProperty({
        description: "Quantity of sessions | Must be a number",
        example: 299,
        required: true,
    })
    @IsNumber({}, { message: "Invalid quantity type" })
    quantity: Number;

    @ApiProperty({
        description: "Price excluding GST | Must be a number",
        example: 299,
        required: true,
    })
    @IsNumber({}, { message: "Invalid price type" })
    unitPrice: Number;

    @ApiProperty({
        description: "Total Amount | Must be a number",
        example: 299,
        required: true,
    })
    @IsNumber({}, { message: "Invalid total type" })
    total: Number;

    @ApiProperty({
        description: "Discount details for this custom package",
        required: false,
    })
    @ValidateNested({ message: "Invalid discount object" })
    @Type(() => DiscountDto)
    @IsOptional()
    discount?: DiscountDto;

    @ApiProperty({
        description: "Package is taxable or not | Boolean",
        example: false,
        required: true,
    })
    @IsBoolean({ message: "Invalid value for taxable" })
    isTaxable: Boolean;

    @ApiProperty({
        description: "Tax on custom package | Must be a number",
        example: 12,
        required: true,
    })
    @IsNumber({}, { message: "Invalid tax type" })
    @Min(0, { message: "Tax percentage cannot be less than 0" })
    @Max(40, { message: "Tax percentage cannot be greater than 40" })
    tax: Number;

    @ApiProperty({
        description: "HSN/SAC code | Must be a string",
        example: "998314",
        required: false,
    })
    @ValidateIf((o) => o.tax > 0)
    @IsString({ message: "Invalid HSN/SAC code" })
    @IsNotEmpty({ message: "HSN/SAC code cannot be empty" })
    @IsOptional()
    hsnOrSacCode: string;
}

export class CustomPackageListDto {
    @ApiProperty({
        description: "The ID of the facility.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsNotEmpty({ message: "facilityId is required" })
    @IsMongoId({ message: "Facility details is Invalid" })
    facilityId: string;
}
