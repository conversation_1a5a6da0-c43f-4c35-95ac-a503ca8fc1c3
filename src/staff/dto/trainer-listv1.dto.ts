import { ApiProperty } from "@nestjs/swagger";
import {
    IsEnum,
    IsMongoId,
    IsNotEmpty,
    IsString,
    Matches,
    IsISO8601,
    IsOptional,
} from "class-validator";
import { ClassType } from "src/utils/enums/class-type.enum";

export class TrainersListV1Dto {
    @ApiProperty({
        description: "Class type for the training session",
        enum: ClassType,
        example: ClassType.PERSONAL_APPOINTMENT,
        required: true,
    })
    @IsNotEmpty({ message: "Class type is required." })
    @IsEnum(ClassType, { message: "Invalid class type. Must be a valid ClassType enum value." })
    classType: ClassType;

    @ApiProperty({
        description: "Facility ID (MongoDB ObjectId)",
        example: "66cedf7a731d1269a4157a2d",
        required: true,
    })
    @IsNotEmpty({ message: "Facility ID is required." })
    @IsMongoId({ message: "Invalid Facility ID. Must be a valid MongoDB ObjectId." })
    facilityId: string;

    @ApiProperty({
        description: "Service category ID (MongoDB ObjectId)",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsNotEmpty({ message: "Service category ID is required." })
    @IsMongoId({ message: "Invalid Service category ID. Must be a valid MongoDB ObjectId." })
    serviceId: string;

    @ApiProperty({
        description: "Sub-type ID for the service category (MongoDB ObjectId)",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsNotEmpty({ message: "Sub-type ID is required." })
    @IsMongoId({ message: "Invalid Sub-type ID. Must be a valid MongoDB ObjectId." })
    subTypeId: string;

    @ApiProperty({
        description: "Date of scheduling (ISO 8601 format)",
        example: "2024-09-15T00:00:00Z",
        required: true,
    })
    @IsNotEmpty({ message: "Date is required." })
    @IsISO8601({ strict: true }, { message: "Invalid date format. Use ISO 8601 format (YYYY-MM-DD)." })
    date: string;

    @ApiProperty({
        description: "Start time in HH:mm format",
        example: "08:00",
        required: true,
    })
    @IsNotEmpty({ message: "Start time is required." })
    @IsString({ message: "Start time must be a string." })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "Invalid start time format. Use HH:mm (24-hour format)." })
    startTime: string;

    @ApiProperty({
        description: "End time in HH:mm format",
        example: "17:00",
        required: true,
    })
    @IsNotEmpty({ message: "End time is required." })
    @IsString({ message: "End time must be a string." })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: "Invalid end time format. Use HH:mm (24-hour format)." })
    endTime: string;
    @ApiProperty({
        description: "scheduleId of the schedule Appointment",
        example: "66cedf7a731d1269a4157a2d",
        required: true,
    })
    @IsOptional()
    @IsMongoId({ message: "Invalid Facility ID. Must be a valid MongoDB ObjectId." })
    scheduleId?: string;
}
