import { ApiProperty } from "@nestjs/swagger";
import { IsMongoId, IsNotEmpty, IsOptional, IsString } from "class-validator";

export class CancelOrder {
    @ApiProperty({
        description: "InvoiceId must be a string",
        example: true,
    })
    @IsString()
    @IsMongoId({ message: "InvoiceId is not valid" })
    invoiceId: string;

    @ApiProperty({
        description: "Reason to cancel/Refunded must be a string",
        example: true,
    })
    @IsString()
    @IsNotEmpty()
    reason: string;

    @ApiProperty({
        description: "Status of the payment cancel or Refunded",
        example: true,
    })
    @IsString()
    @IsNotEmpty()
    paymentStatus: string;

}
