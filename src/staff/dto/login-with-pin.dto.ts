import { ApiProperty } from "@nestjs/swagger";
import { IsMongoId, Matches } from "class-validator";

export class LoginWithPinDto {
    @ApiProperty({
        description: "The Id of the organization",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsMongoId({ message: "Invalid type of organization" })
    organizationId: string;

    @ApiProperty({
        description: "Pin for the user",
        example: "1234",
        minLength: 4,
        maxLength: 4,
        required: true,
    })
    @Matches(/^\d{4}$/, { message: "The pin must be exactly 4 digits." })
    pin: string;
}
