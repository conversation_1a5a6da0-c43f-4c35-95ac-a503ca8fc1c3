import { ApiProperty, OmitType } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsDateString, IsArray, ValidateNested, IsMongoId, IsEnum, IsString } from "class-validator";
import { Types } from "mongoose";
import { ClassType } from "src/utils/enums/class-type.enum";

export class ListStaffAvailabilityDto {
    @ApiProperty({
        description: "The IDs of the users",
        example: ["66cecb432351713ae4447a6b", "659d268dee4b6081dacd41ff"],
        required: false,
    })
    @IsOptional()
    @IsArray({ message: "UserID must be an array" })
    userIds?: Types.ObjectId[];

    @ApiProperty({
        description: "The IDs of the Branches where the trainer is assigned.",
        example: ["659d268dee4b6081dacd41fd", "659d268dee4b6081dacd41ff"],
        required: false,
    })
    @IsOptional()
    @IsArray({ message: "FacilityId must be an array" })
    facilityIds?: Types.ObjectId[];

    // @ApiProperty({
    //     description: "Array of ClassTypes for Availability",
    //     enum: ClassType,
    //     isArray: true,
    //     example: [ClassType.PERSONAL_APPOINTMENT, ClassType.CLASSES],
    //     required: true,
    // })
    // @IsArray({ message: "ClassType must be an array of valid enum values" }) 
    // @IsEnum(ClassType, { each: true, message: "Each ClassType must be a valid enum value" })
    // @IsNotEmpty({ message: "ClassType is required" })
    // classType: ClassType[];

    @ApiProperty({
        description: "Start date of availability",
        example: "2024-09-15T00:00:00Z",
    })
    @IsNotEmpty({ message: "Start date is required" })
    startDate: Date;

    @ApiProperty({
        description: "End date of availability",
        example: "2024-09-15T00:00:00Z",
    })
    @IsNotEmpty({ message: "End date is required" })
    endDate: Date;

    @ApiProperty({
        description: "Array of ClassTypes for Availability",
        enum: ClassType,
        example: ClassType.PERSONAL_APPOINTMENT,
        required: false,
    })
    @IsEnum(ClassType, { each: true, message: "Each ClassType must be a valid enum value" })
    @IsOptional()
    classType?: ClassType;

    @ApiProperty({
        description: "The IDs of the PayRates for the Staff",
        example: ["659d268dee4b6081dacd41fd", "659d268dee4b6081dacd41fe"],
        isArray: true,
        required: false,
    })
    @IsArray({ message: "payRateIds must be an array of ObjectIds" })
    @IsMongoId({ each: true, message: "Each payRate Id must be a valid ObjectId" })
    @IsOptional()
    payRateIds?: string[];
}
export class ListStaffAvailabilityByTypesDto extends OmitType(ListStaffAvailabilityDto, ['payRateIds']) {

    @ApiProperty({
        description: "Service category for filtering availability",
        example: new Types.ObjectId(),
        required: true
    })
    @IsNotEmpty({ message: "Service category is required" })
    @IsString({ message: "Service category must be a string" })
    serviceCategoryId: string;

    @ApiProperty({
        description: "Sub type for filtering availability",
        example: new Types.ObjectId(),
        required: true
    })
    @IsNotEmpty({ message: "Sub type is required" })
    @IsString({ message: "Sub type must be a string" })
    subTypeId: string;

}
