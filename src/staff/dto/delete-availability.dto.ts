import { ApiProperty } from "@nestjs/swagger";
import { IsDateString, IsEnum, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { DeleteStaffAvailability } from "src/utils/enums/delete-staff.enum";

export class DeleteStaffAvailabilityDto {
    @ApiProperty({
        description: "dateId of the Schedule",
        example: "66cecb432351713ae4447a6b",
    })
    @IsOptional()
    @IsString({ message: "dateId must be a string" })
    dateId: string;

    @ApiProperty({
        description: "userId of the Schedule",
        example: "66cecb432351713ae4447a6b",
    })
    @IsOptional()
    @IsString({ message: "userId must be a string" })
    userId: string;

    @ApiProperty({
        description: "facilityId of the Schedule",
        example: "66cecb432351713ae4447a6b",
    })
    @IsOptional()
    @IsString({ message: "facility id must be a string" })
    facilityId: string;

    @ApiProperty({
        description: "The ID of the Slot",
        example: "66cecb432351713ae4447a6b",
    })
    @IsOptional()
    @IsString({ message: "Slot ID must be a string" })
    slotId: string;

    @ApiProperty({
        description: "Start date of availability",
        example: "2024-12-03T00:00:00.000+00:00",
    })
    // @IsDateString()
    @IsOptional()
    startDate: Date;

    @ApiProperty({
        description: "End date of availability",
        example: "2024-12-04T00:00:00.000+00:00",
    })
    // @IsDateString()
    @IsOptional()
    endDate: Date;

    @ApiProperty({
        description: "Delete staff availability by type",
        enum: DeleteStaffAvailability,
        required: true,
        example: DeleteStaffAvailability.CUSTOM,
    })
    @IsNotEmpty({ message: "Type is required" })
    @IsEnum(DeleteStaffAvailability, { message: "DeleteStaffAvailability must be a valid enum value" })
    type: DeleteStaffAvailability;
}
