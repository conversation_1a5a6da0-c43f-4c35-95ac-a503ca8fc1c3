import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString } from "class-validator";
import { PaginationDTO } from "src/users/dto/pagination.dto";

export class FacilityClientListDto extends PaginationDTO {
    @ApiProperty({
        description: "Text on which you want to apply search | Working only on name",
        example: "Rvi",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Required valid search string" })
    search: string;
}
