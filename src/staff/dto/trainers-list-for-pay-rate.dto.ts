import { ApiProperty } from "@nestjs/swagger";
import { <PERSON><PERSON><PERSON>y, IsEnum, IsMongoId, IsNotEmpty, IsOptional,IsBoolean } from "class-validator";
import { ClassType } from "src/utils/enums/class-type.enum";

export class TrainersListForPayRateDto {
    @ApiProperty({
        description: "Type of the Service",
        example: ClassType.PERSONAL_APPOINTMENT,
        enum: ClassType,
        required: true,
    })
    @IsEnum(ClassType, { message: "Service Type must be a valid enum value" })
    @IsNotEmpty({ message: "Service Type is required" })
    serviceType: ClassType;

    @ApiProperty({
        description: "The ID of the Service category",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsMongoId({ message: "Service Category ID must be a valid ObjectId" })
    @IsNotEmpty({ message: "Service Category is required" })
    serviceCategory: string;

    @ApiProperty({
        description: "The ID of the Appointment Type",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    @IsMongoId({ message: "Appointment Type ID must be a valid ObjectId" })
    @IsOptional()
    appointmentType?: string;
    
    @ApiProperty({
        description: "Organization ID for the appointment",
        type: String,
        example: "615c2f8e2c1ae9123cbb3c1b",
    })
    @IsNotEmpty()
    organizationId: string;
}
