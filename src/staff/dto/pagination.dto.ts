import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { ArrayNotEmpty, IsArray, IsBoolean, IsEnum, IsInt, IsMongoId, IsNotEmpty, IsOptional, IsString, <PERSON>, <PERSON> } from "class-validator";
import { Types } from "mongoose";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";

export class StaffPaginationDTO {
    @ApiProperty({
        description: "Search query to filter results.",
        example: "Search",
        required: false,
    })
    @IsOptional()
    @IsString({ message: "Search must be a string" })
    search?: string;

    @ApiProperty({
        description: "Role of the Staff",
        example: [ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN],
        required: false,
    })
    @IsOptional()
    @IsArray({ message: "Role must be an array" })
    @ArrayNotEmpty({ message: "Role array must not be empty" }) // Optional: To ensure the array is not empty
    @IsEnum(ENUM_ROLE_TYPE, { each: true, message: "Each role must be a valid Staff enum value" })
    role?: ENUM_ROLE_TYPE[];

    @ApiProperty({
        description: "The IDs of the location where the Branch is located.",
        example: ["659d268dee4b6081dacd41fd", "659d268dee4b6081dacd41ff"],
        required: false,
    })
    @IsArray({ message: "locationId must be an array" })
    @IsOptional()
    locationId?: Types.ObjectId[];

    @ApiProperty({
        description: "Number of items per page.",
        example: 10,
        minimum: 1,
        maximum: 50,
        required: true,
    })
    @IsNotEmpty({ message: "PageSize is required" })
    @IsInt({ message: "PageSize must be an integer" })
    @Min(1, { message: "PageSize must be at least 1" })
    @Max(50, { message: "PageSize must be at most 50" })
    pageSize: number;

    @ApiProperty({
        description: "Page number to jump to.",
        example: 1,
        minimum: 1,
        required: true,
    })
    @IsNotEmpty({ message: "Page is required" })
    @IsInt({ message: "Page must be an integer" })
    @Min(1, { message: "Page must be at least 1" })
    page: number;


    @ApiProperty({
        description: "Get Only active docs",
        example: undefined,
        required: false,
    })
    @IsOptional()
    @Type(()=> Boolean)
    isActive?: boolean
}