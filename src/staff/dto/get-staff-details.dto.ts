import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsNotEmpty, IsString, Matches, IsEnum, ValidateIf } from "class-validator";
import { ClassType } from "src/utils/enums/class-type.enum";
import { DateRange } from "src/utils/enums/date-range-enum";
import { MarkAvailabilityType } from "src/utils/enums/mark-availabilityType.enum";
import { StaffAvailabilityEnum } from "src/utils/enums/staff-availability.enum";

export class GetStaffAvailabilityDto {
    @ApiProperty({
        description: "Id of the trainer",
        example: "66cecb432351713ae4447a6b",
        required: true,
    })
    @IsNotEmpty({ message: "trainerId is required" })
    @IsString()
    trainerId: string;

    @ApiProperty({
        description: "Id of the Branch where the trainer is assigned.",
        example: "659d268dee4b6081dacd41fd",
        required: true,
    })
    @IsNotEmpty({ message: "facilityId is required" })
    @IsString()
    facilityId: string;

    @ApiProperty({
        description: "Array of ClassTypes for Availability",
        enum: ClassType,
        example: ClassType.PERSONAL_APPOINTMENT,
        required: false, // conditional
    })
    @ValidateIf((o) => o.availabilityStatus === StaffAvailabilityEnum.AVAILABLE)
    @IsEnum(ClassType, { message: "ClassType must be a valid enum value" })
    @IsNotEmpty({ message: "ClassType is required when staff is AVAILABLE" })
    classType: string;

    @ApiProperty({
        description: "Start date of availability",
        example: "2024-09-15T00:00:00Z",
    })
    @IsNotEmpty({ message: "Start date is required" })
    startDate: Date;

    @ApiProperty({
        description: "End date of availability",
        example: "2024-09-15T00:00:00Z",
    })
    @ValidateIf((obj) => obj.markType === MarkAvailabilityType.CUSTOM)
    @IsNotEmpty({ message: "End date is required when markType is CUSTOM" })
    @Type(() => Date)
    endDate?: Date;

    @ApiProperty({
        description: 'Start time for availability on specified days (HH:mm)',
        example: '08:00',
        required: true
    })
    @IsNotEmpty({ message: 'From time is required' })
    @IsString({ message: 'From time must be a string' })
    @Matches(/^([0-1]\d|2[0-3]):([0-5]\d)$/, { message: 'From time must be in the format HH:mm' })
    from: string;

    @ApiProperty({
        description: "Date range type",
        enum: DateRange,
        default: DateRange.SINGLE,
        example: DateRange.SINGLE,
    })
    @IsEnum(DateRange, { message: "Date range must be a valid enum value" })
    @IsNotEmpty({ message: "Date range is required" })
    dateRange: DateRange;

    @ApiProperty({
        description: "The availability status of the staff",
        enum: StaffAvailabilityEnum,
        default: StaffAvailabilityEnum.AVAILABLE,
        example: StaffAvailabilityEnum.AVAILABLE,
    })
    @IsEnum(StaffAvailabilityEnum, { message: "Availability status must be a valid enum value" })
    @IsNotEmpty({ message: "Availability status is required" })
    availabilityStatus: string;

    @ApiProperty({
        description: "MarkType for Availability",
        enum: MarkAvailabilityType,
        example: MarkAvailabilityType.WEEKLY,
        required: false,
    })
    @ValidateIf((obj) => obj.dateRange === DateRange.MULTIPLE)
    @IsNotEmpty({ message: "MarkType is required" })
    @IsEnum(MarkAvailabilityType, { message: "MarkType must be a valid enum value" })
    markType?: MarkAvailabilityType;
}