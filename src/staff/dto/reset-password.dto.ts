import { ApiProperty } from '@nestjs/swagger';
import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsNotEmpty, IsStrongPassword } from 'class-validator';

export class ResetPasswordDto {
  @ApiProperty({
    description: "Enter Your Current Password",
    example: "Demo@123",
  })
  @IsString()
  @IsNotEmpty({ message: "Current Password is required." })
  oldPassword: string;

  @ApiProperty({
    description: "A new strong password for the user account. Must meet the strong password criteria.",
    example: "N3wStr0ngP@ss!",
  })
  @IsString()
  @IsNotEmpty({ message: "Password is required." })
  @IsStrongPassword({}, { message: "Password must meet the strong password criteria." })
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @MaxLength(20, { message: 'Password cannot exceed 20 characters' })
  newPassword: string;


  @ApiProperty({
    description: "Confirmation of the new strong password for the user account. Must meet the strong password criteria.",
    example: "N3wStr0ngP@ss!",
  })
  @IsString()
  @IsNotEmpty({ message: "Password is required." })
  @IsStrongPassword({}, { message: "Password must meet the strong password criteria." })
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @MaxLength(20, { message: 'Password cannot exceed 20 characters' })
  confirmPassword: string;
}
