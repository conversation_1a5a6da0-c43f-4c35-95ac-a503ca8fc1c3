import { ApiProperty } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsEnum, IsMongoId, IsNotEmpty, IsOptional, IsString, Length, ValidateNested } from "class-validator";
import { Types } from "mongoose";
import { Gender } from "src/utils/enums/gender.enum";

export class Address {
    @ApiProperty({
        description: "The ID of the state where the staff resides.",
        example: "659d268dee4b6081dacd41fd",
        required: false,
    })
    @IsMongoId({ message: "State ID must be a valid ObjectId" })
    @IsOptional()
    stateId?: Types.ObjectId;

    @ApiProperty({
        description: "The ID of the city where the staff resides.",
        example: "659e8032293375c3166a99a0",
        required: false,
    })
    @IsMongoId({ message: "City ID must be a valid ObjectId" })
    @IsOptional()
    cityId?: Types.ObjectId;

    @ApiProperty({
        description: "Street address of the staff.",
        example: "Damar West Opposite to SBI Bank",
        minLength: 2,
        maxLength: 1000,
        required: false,
    })
    @IsString({ message: "Street must be a string" })
    @Length(2, 1000, { message: "Street must be between 2 and 1000 characters" })
    @IsOptional()
    street?: string;

    @ApiProperty({
        description: "Country where the staff resides.",
        example: "India",
        minLength: 2,
        maxLength: 1000,
        required: false,
    })
    @IsString({ message: "Country must be a string" })
    @Length(2, 1000, { message: "Country must be between 2 and 1000 characters" })
    @IsOptional()
    country?: string;

}

export class CompleteStaffProfileDto {
    @ApiProperty({
        description: "First name of the staff.",
        example: "HKS",
        minLength: 2,
        maxLength: 50,
    })
    @Length(2, 50, { message: "First name must be between 2 and 50 characters long." })
    @IsOptional()
    firstName?: string;

    @ApiProperty({
        description: "Last name of the staff.",
        example: "Abhay",
        minLength: 2,
        maxLength: 50,
    })
    @Length(2, 50, { message: "Last name must be between 2 and 50 characters long." })
    @IsOptional()
    lastName?: string;

    @ApiProperty({ description: "Profile Picture of the staff", example: "https://my-bucket.s3.us-west-2.amazonaws.com/uploads/images/example.jpg" })
    @IsOptional()
    @IsString({ message: "Profile Picture must be a string" })
    profilePicture?: string;

    @ApiProperty({
        description: "Gender of the staff.",
        enum: Gender,
        example: Gender.MALE
    })
    @IsOptional()
    @IsEnum(Gender, { message: "Gender must be a valid Gender enum value" })
    gender?: Gender;

    @ApiProperty({
        description: "Date of birth of the staff.",
        example: new Date().toISOString()
    })
    @IsNotEmpty({ message: "Date of birth is required" })
    @Transform(({ value }) => new Date(value), { toClassOnly: true })
    @IsOptional()
    dateOfBirth?: Date;

    @ApiProperty({
        description: "The address of the gym.",
        example: Address,
        required: false,
    })
    @ValidateNested({ message: "Address is invalid" })
    @IsOptional()
    @Type(() => Address)
    address: Address;

    @ApiProperty({
        description: "Certifications of the staff.",
        type: String,
        required: false,
        example: "Gold medalist"
    })
    @IsOptional()
    @IsString({ message: "Certification must be a string" })
    certification?: string;

    @ApiProperty({
        description: "Experience of the staff.",
        type: String,
        required: false,
        example: "2.5 Years"
    })
    @IsOptional()
    @IsString({ message: "Experience must be a string" })
    experience?: string;

    @ApiProperty({
        description: "Description about the staff.",
        type: String,
        required: false,
        example: "Description about the staff"
    })
    @IsOptional()
    @IsString({ message: "Description must be a string" })
    description?: string;
}
