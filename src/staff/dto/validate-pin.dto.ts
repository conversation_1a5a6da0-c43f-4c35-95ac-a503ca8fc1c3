import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { Types } from 'mongoose';
import { IsNotEmpty, Matches } from 'class-validator';
import { RefreshSessionRequestDto } from 'src/auth/dto/refresh.session.request.dto';


export class ValidatePinDto extends RefreshSessionRequestDto {

    @ApiProperty({
        description: 'User ID',
        example: '659d268dee4b6081dacd41fd',
    })
    @IsNotEmpty({message: "User is required"})
    userId: string;

    @ApiProperty({
        description: 'Confirm PIN code',
        example: '1234',
    })
    @IsNotEmpty()
    @Type(() => String)
    confirmPin: string;
}
