import { ApiProperty } from "@nestjs/swagger";
import { <PERSON><PERSON><PERSON>y, IsEnum, IsMongoId, IsNotEmpty, IsO<PERSON>al, IsString } from "class-validator";
import { Types } from "mongoose";
import { ClassType } from "src/utils/enums/class-type.enum";
import { PayRates } from "src/utils/enums/pay-rate.type.enum";

export class AvailabilityServiceCategoryDto {
    @ApiProperty({
        description: "The ID of the Organization",
        example: "66cedf7a731d1269a4157a2d",
        required:true
    })
    @IsNotEmpty({ message: "Organization ID is required" })
    @IsMongoId({ message: "Organization ID must be a string" })
    organizationId: string;

    @ApiProperty({
        description: "The IDs of the Branches where the trainer is assigned.",
        example: ["659d268dee4b6081dacd41fd", "659d268dee4b6081dacd41ff"],
        required: false,
    })
    @IsOptional()
    @IsArray({ message: "FacilityId must be an array" })
    facilityIds?: string[];

    @ApiProperty({
        description: "The IDs of the Trainer",
        example: ["66cecb432351713ae4447a6b", "659d268dee4b6081dacd41ff"],
        required: false,
    })
    @IsOptional()
    @IsArray({ message: "Trainer Ids must be an array" })
    trainerIds?: Types.ObjectId[];

    @ApiProperty({
        description: "Array of ClassTypes for Availability",
        enum: ClassType,
        example: ClassType.PERSONAL_APPOINTMENT,
        required: false,
    })
    @IsEnum(ClassType, { each: true, message: "Each ClassType must be a valid enum value" })
    @IsOptional()
    classType?: ClassType;

    @ApiProperty({
        description: "Search must be a string || Search is optional",
        example: "Yoga",
    })
    @IsOptional()
    @IsString({ message: "Search must be a string" })
    search: string;
}
