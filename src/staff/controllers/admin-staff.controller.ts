import { BadRequestException, Body, Controller, Delete, ForbiddenException, Get, Headers, HttpCode, HttpStatus, NotFoundException, Param, Patch, Post, Put, UseGuards } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { CreateStaffDto } from "src/staff/dto/create-staff-profile.dto";
import { StaffService } from "src/staff/services/staff.service";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { updateStaffProfileDto } from "../dto/update-staff-profile.dto";
import { StaffPaginationDTO } from "../dto/pagination.dto";
import { ResetPasswordDto } from "../dto/reset-password.dto";
import { LoginWithPinDto } from "../dto/login-with-pin.dto";
import { ResetPinDto } from "../dto/reset-pin.dto";
import { Types } from "mongoose";
import { StaffAvailabilityDto } from "../dto/staff-availability.dto";
import { ListStaffAvailabilityByTypesDto, ListStaffAvailabilityDto } from "../dto/get-staff-availability.dto";
import { UpdateStaffAvailabilityDto } from "../dto/update-staff-availability.dto";
import { TrainersListDto } from "../dto/trainers-list.dto";
import { GetStaffAvailabilityDto } from "../dto/get-staff-details.dto";
import { DeleteStaffAvailabilityDto } from "../dto/delete-availability.dto";
import { AvailabilityServiceCategoryDto } from "../dto/avail-service-category.dto";
import { CheckMailorPhoneDto } from "../dto/checkMailorPhone.dto";
import { updateStaffStatusDto } from "src/staff/dto/update-staff-status.dto";
import { StaffAvailabilityByTypeDto } from "../dto/get-staff-availability-by-subtype.dto";
import { TrainersListV1Dto } from "../dto/trainer-listv1.dto";
import { TrainersListForPayRateDto } from "src/staff/dto/trainers-list-for-pay-rate.dto";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { AuthJwtAccessProtected, AuthSessionProtected } from "src/auth/decorators/auth.jwt.decorator";
import { PolicyAbilityProtected, PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";
import { ENUM_PERMISSION_TYPE } from "src/policy/enums/policy.permissions.enum";
import { GetOrganizationId } from "src/organization/decorators/organization.decorator";
import { IDatabaseObjectId } from "src/common/database/interfaces/database.objectid.interface";
import { FacilityProtected } from "src/facility/decorators/user.facility.decorator";
import { GetFacilities } from "src/facility/decorators/get.facility.decorators";
import { PolicyAllowIfSelfOrHasPermission, PolicyHasPermission } from "src/policy/decorators/allow-self-or-has-permission.decorator";
import { ENUM_POLICY_STATUS_CODE_ERROR } from "src/policy/enums/policy.status-code.enum";

@ApiTags("Admin-Staff")
@ApiBearerAuth()
@Controller("admin/staff")
export class AdminStaffController {
    constructor(private staffService: StaffService) { }

    @Post("/register")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.STAFF_WRITE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Register a new Staff" })
    async registerStaff(@Body() registerStaffDto: CreateStaffDto, @GetUser() user, @Headers("x-organization") organizationId: string): Promise<any> {
        return await this.staffService.adminRegisterStaff(registerStaffDto, user, user._id, organizationId);
    }

    @Post("/pinLogin")
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Login with pin" })
    async loginWithPin(@Body() loginWithPinDto: LoginWithPinDto, @GetUser() user): Promise<any> {
        let validatePin = await this.staffService.validatePin(loginWithPinDto, user);
        return {
            message: "Pin verified",
            data: validatePin,
        };
    }

    @Post("/reset/pin")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.STAFF_WRITE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Reset staff pin" })
    async resetPin(@Body() resetPinDto: ResetPinDto): Promise<any> {
        let pin = await this.staffService.resetPin(resetPinDto);
        return {
            message: "Pin reset",
            data: "",
        };
    }

    @Put("/update/Profile/:id")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.STAFF_WRITE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Update staff Profile details By ADMIN" })
    async updateStaffDetails(@Body() updateStaffDto: updateStaffProfileDto, @GetUser() user, @Param("id") id: string): Promise<any> {
        return await this.staffService.adminUpdateStaffDetails(updateStaffDto, id);
    }

    @Put("/updateStatus/:id")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.STAFF_WRITE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Update staff status By ADMIN" })
    async updateStaffStatus(@Body() updateStaffStatusDto: updateStaffStatusDto, @GetUser() user, @Param("id") id: string): Promise<any> {
        return await this.staffService.adminUpdateStaffStatus(updateStaffStatusDto, id);
    }

    @Post("/list")
    @FacilityProtected()
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.STAFF_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Fetch all staff list" })
    async getAllUserList(
        @Body() paginationDTO: StaffPaginationDTO,
        @GetFacilities('_id') facilityIds: IDatabaseObjectId[]
    ): Promise<any> {
        try {
            let locationId = [];
            if (paginationDTO?.locationId?.length > 0) {
                locationId = paginationDTO?.locationId.map((id) => new Types.ObjectId(id));
            }
            const list = await this.staffService.adminGetAllStaffList(paginationDTO, facilityIds, locationId);
            return list;
        } catch (error) {
            throw new Error(error.message);
        }
    }

    @Get("/details/:id")
    // @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.STAFF_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Fetch staff profile details by Id" })
    async adminGetUserDetailsById(@Param("id") id: string): Promise<any> {
        try {
            const user = await this.staffService.adminGetStaffDetailsById(id);
            return user;
        } catch (error) {
            throw new Error(error.message);
        }
    }

    @Patch("/reset-password/:id")
    // @UseGuards(AuthGuard(), RolesGuard)
    // @Roles(ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN)
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.STAFF_WRITE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Reset password of the staff member" })
    async resetPassword(@Param("id") id: string, @Body() resetPasswordDto: ResetPasswordDto) {
        const { oldPassword, newPassword, confirmPassword } = resetPasswordDto;
        return this.staffService.resetPassword(id, oldPassword, newPassword, confirmPassword);
    }

    @Delete("delete/:id")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.STAFF_DELETE)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Delete a staff member" })
    async deleteStaff(@Param("id") id: string): Promise<any> {
        return this.staffService.adminDeleteStaff(id);
    }

    @Post("/add/availability")
    @PolicyAbilityProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Create new staff availability" })
    async create(
        @PolicyAllowIfSelfOrHasPermission([ENUM_PERMISSION_TYPE.STAFF_AVAILABILITY_WRITE], 'userId') _: any,
        @Body() staffAvailabilityDto: StaffAvailabilityDto,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ) {
        const result = await this.staffService.createStaffAvailability(organizationId, staffAvailabilityDto);
        return {
            message: "Successfully Added Availability",
            data: result,
        };
    }

    @Post("/availability/list")
    @PolicyAbilityProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "List staff availability" })
    async listAvailability(
        @PolicyAllowIfSelfOrHasPermission([ENUM_PERMISSION_TYPE.STAFF_AVAILABILITY_READ], ["userIds"]) _: any,
        @Body() listStaffAvailabilityDto: ListStaffAvailabilityDto,
        @GetUser() user: any,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ) {
        let result = [];
        result = await this.staffService.listStaffAvailability(listStaffAvailabilityDto, organizationId, user);
        return {
            message: "Data fetched Successfully",
            data: result,
        };
    }

    @Post("/availability/list/by-type")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.STAFF_AVAILABILITY_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "List staff availability" })
    async listAvailabilityByTypes(@Body() bodyDto: ListStaffAvailabilityByTypesDto, @GetUser() user: any) {
        let role = user.role;
        let result = "";
        let access = false;
        switch (role.type) {
            case ENUM_ROLE_TYPE.TRAINER:
                access = bodyDto.userIds.toString().includes(user._id.toString()) && bodyDto.userIds.length == 1;
                if (!access) {
                    throw new ForbiddenException("Access denied");
                }
                result = await this.staffService.listStaffAvailabilityListByTypes(bodyDto, user);
                break;
            case ENUM_ROLE_TYPE.USER:
                result = await this.staffService.listStaffAvailabilityListByTypes(bodyDto, user);
                break;
            case ENUM_ROLE_TYPE.ORGANIZATION:
                result = await this.staffService.listStaffAvailabilityListByTypes(bodyDto, user);
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                result = await this.staffService.listStaffAvailabilityListByTypes(bodyDto, user);
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        return {
            message: "Data fetched Successfully",
            data: result,
        };
    }

    @Post("/getDetails")
    @PolicyAbilityProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get staff availability details" })
    async getStaffAvailabilityDetails(
        @PolicyAllowIfSelfOrHasPermission([ENUM_PERMISSION_TYPE.STAFF_AVAILABILITY_READ], 'trainerId') _: any,
        @Body() getStaffAvailabilityDto: GetStaffAvailabilityDto,
        @GetUser() user: any,
        @GetOrganizationId() organizationId: IDatabaseObjectId
    ) {
        const result = await this.staffService.StaffAvailability(getStaffAvailabilityDto, user);

        return {
            message: "Data fetched Successfully",
            data: result,
        };
    }

    // @Post("/availability/list/by-type")
    // @UseGuards(AuthGuard(), RolesGuard)
    // @Roles(ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.ORGANIZATION,ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.USER)
    // @ApiOperation({ summary: "List staff availability" })
    // async listAvailabilityByTypes(@Body() bodyDto: ListStaffAvailabilityByTypesDto, @GetUser() user: any) {
    //     let role = user.role;
    //     let result = "";
    //     let access = false;
    //     switch (role.type) {
    //         case ENUM_ROLE_TYPE.TRAINER:
    //             access = bodyDto.userIds.toString().includes(user._id.toString()) && bodyDto.userIds.length == 1;
    //             if (!access) {
    //                 throw new ForbiddenException("Access denied");
    //             }
    //             result = await this.staffService.listStaffAvailabilityListByTypes(bodyDto, user);
    //             break;
    //         case ENUM_ROLE_TYPE.USER:
    //             result = await this.staffService.listStaffAvailabilityListByTypes(bodyDto, user);
    //             break;
    //         case ENUM_ROLE_TYPE.ORGANIZATION:
    //             result = await this.staffService.listStaffAvailabilityListByTypes(bodyDto, user);
    //             break;
    //         case ENUM_ROLE_TYPE.WEB_MASTER:
    //         case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
    //             result = await this.staffService.listStaffAvailabilityListByTypes(bodyDto, user);
    //             break;
    //         default:
    //             throw new BadRequestException("Access denied");
    //     }
    //     return {
    //         message: "Data fetched Successfully",
    //         data: result,
    //     };
    // }

    @Post("/availability/by-type")
    @HttpCode(HttpStatus.OK)
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.STAFF_AVAILABILITY_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "List staff availability by type" })
    async listAvailabilityByType(@Body() staffAvailabilityDto: StaffAvailabilityByTypeDto, @GetUser() user: any) {
        let role = user.role;
        let result = "";
        let access = false;
        switch (role.type) {
            case ENUM_ROLE_TYPE.TRAINER:
                access = staffAvailabilityDto.userId.toString() == user._id.toString();
                if (!access) {
                    throw new ForbiddenException("Access denied");
                }
                result = await this.staffService.staffAvailabilityTypes(staffAvailabilityDto, user);
                break;
            case ENUM_ROLE_TYPE.USER:
                result = await this.staffService.staffAvailabilityTypes(staffAvailabilityDto, user);
                break;
            case ENUM_ROLE_TYPE.ORGANIZATION:
                result = await this.staffService.staffAvailabilityTypes(staffAvailabilityDto, user);
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                result = await this.staffService.staffAvailabilityTypes(staffAvailabilityDto, user);
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        return {
            message: "Data fetched Successfully",
            data: result,
        };
    }

    @Post("/availability/:id")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.STAFF_AVAILABILITY_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Staff availability details by Id" })
    async staffAvailability(@Param("id") id: string, @GetUser() user: any) {
        let data = await this.staffService.getStaffAvailability(id);
        if (!data) throw new BadRequestException("Invalid details");
        return {
            message: "Staff availability details",
            data: data,
        };
    }

    @Patch("/update/availability")
    @PolicyAbilityProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Update staff availability" })
    async updateAvailability(
        @PolicyAllowIfSelfOrHasPermission([ENUM_PERMISSION_TYPE.STAFF_AVAILABILITY_UPDATE], 'userId') _: any,
        @Body() updateStaffAvailabilityDto: UpdateStaffAvailabilityDto,
        @GetUser() user: any,
    ) {
        const result = await this.staffService.updateStaffAvailability(updateStaffAvailabilityDto, user);
        return {
            message: "Data updated Successfully",
            data: result,
        };
    }

    @Post("/trainers/list")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.STAFF_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Trainer's list" })
    async trainersList(@GetUser() user, @Body() trainerListDto: TrainersListDto): Promise<any> {
        let role = user.role;
        let data = "";
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                data = await this.staffService.trainersListOrg(trainerListDto, user._id);
                break;
            // case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
            //     data = await this.staffService.trainersListStaff(trainerListDto, user._id);
            //     break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
            case ENUM_ROLE_TYPE.TRAINER:
                data = await this.staffService.trainersListStaff(trainerListDto, user._id);
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        return {
            message: "Trainers list",
            data: data,
        };
    }

    @Post("/trainersForPayRate/list")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.STAFF_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Trainer's list" })
    async trainersForPayRateList(
        @GetUser() user,
        @Body() trainerListDto: TrainersListForPayRateDto
    ): Promise<any> {
        let role = user.role;
        let data = "";
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
            case ENUM_ROLE_TYPE.TRAINER:
                data = await this.staffService.trainersListForPayRate(trainerListDto, user);
                break;
            default:
                throw new BadRequestException("Access denied");
        }
        return {
            message: "Trainers list",
            data: data,
        };
    }

    @Post("/delete/availability")
    @PolicyAbilityProtected()
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Delete staff availability" })
    async deleteAvailability(
        @PolicyHasPermission(ENUM_PERMISSION_TYPE.STAFF_AVAILABILITY_DELETE) hasPermissions: boolean,
        @Body() deleteStaffAvailabilityDto: DeleteStaffAvailabilityDto,
        @GetUser() user: IUserDocument,
    ) {
        const availability = await this.staffService.findStaffAvailabilityById(deleteStaffAvailabilityDto, user);

        if (availability?.length == 0) {
            throw new NotFoundException("Availability record not found");
        }

        if (!user._id.equals(availability[0].userId) && !hasPermissions) {
            new ForbiddenException({
                statusCode: ENUM_POLICY_STATUS_CODE_ERROR.ABILITY_FORBIDDEN,
                message: 'policy.error.abilityForbidden',
            });
        }

        const result = await this.staffService.deleteStaffAvailability(deleteStaffAvailabilityDto, user);

        return {
            message: "Successfully deleted availability",
            data: result,
        };
    }

    @Post("avail-service-category")
    // @UseGuards(AuthGuard(), RolesGuard)
    // @Roles(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.STAFF_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get all avail Service Category By StaffID Marked in availability" })
    async availServiceCategory(@Body() availabilityServiceCategoryDto: AvailabilityServiceCategoryDto, @GetUser() user: any) {
        return await this.staffService.availServiceCategory(availabilityServiceCategoryDto, user);
    }

    @Post("checkMailorMobile")
    @ApiOperation({ summary: "Check if email or mobile already exists" })
    async checkMailorMobile(@Body() checkMailorPhoneDto: CheckMailorPhoneDto, @Headers("x-organization") organizationId: string): Promise<any> {
        return await this.staffService.checkMailorMobile(checkMailorPhoneDto, organizationId);
    }

    @Post("/trainers/list/v1")
    @PolicyAbilityProtected(ENUM_PERMISSION_TYPE.STAFF_READ)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "This Api Will fetch Trainer Who are available based on filter" })
    async trainersListV1(@GetUser() user, @Body() trainerListV1Dto: TrainersListV1Dto): Promise<any> {
        return await this.staffService.trainersListV1(trainerListV1Dto, user);
    }

    @Post("app/availability/list/by-type")
    @ApiOperation({ summary: "List staff availability" })
    async listAvailabilityByTypesForApp(@Body() bodyDto: ListStaffAvailabilityByTypesDto) {
        let result = "";
        result = await this.staffService.listStaffAvailabilityListByTypesForApp(bodyDto);
        return {
            message: "Data fetched Successfully",
            data: result,
        };
    }

    @Post("app/availability/by-type")
    @HttpCode(HttpStatus.OK)
    @ApiOperation({ summary: "List staff availability by type" })
    async listAvailabilityByTypeForApp(@Body() staffAvailabilityDto: StaffAvailabilityByTypeDto) {
        let result = "";
        result = await this.staffService.staffAvailabilityTypesForApp(staffAvailabilityDto);
        return {
            message: "Data fetched Successfully",
            data: result,
        };
    }
}
