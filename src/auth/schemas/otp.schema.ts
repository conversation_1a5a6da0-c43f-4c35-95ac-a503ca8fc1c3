import { JwtService } from "@nestjs/jwt";
import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import * as bcrypt from "bcrypt";
import * as crypto from "crypto";
import { HydratedDocument, SchemaTypes } from "mongoose";
import { UserDocument } from "src/users/schemas/user.schema";

export type OtpDocument = HydratedDocument<Otp>;

@Schema({ timestamps: true })
export class Otp {

    @Prop()
    otp: number;

    @Prop({ required: true, type: String, lowercase: true })
    for: string;

    @Prop()
    createdAt: Date;

    @Prop()
    updatedAt: Date;

    @Prop({ type: SchemaTypes.ObjectId, required: false, ref: "User" })
    organizationId: string;


    @Prop()
    token?: string;

    @Prop({ type: [SchemaTypes.ObjectId], required: false, ref: "User" })
    users?: string[];

    @Prop()
    expireAt?: Date;

    validateToken: (token: string) => Promise<boolean>;
    static generateToken = async function (user: any): Promise<string> {
        const jwtService = new JwtService({
            secret: process.env.JWT_SECRET,
            signOptions: { expiresIn: '15m' }
        });
        const token = jwtService.sign({
            userId: user._id,
            user: user._id,
            sub: user._id,
            email: user.email,
            mobile: user.mobile,
            timestamp: Date.now(),
            random: Math.random().toString(36).substring(7)
        });
        return token;
    }

}

export const OtpSchema = SchemaFactory.createForClass(Otp);

OtpSchema.methods.validateToken = async function (token: string): Promise<boolean> {
    return await bcrypt.compare(token, this.token);
};

OtpSchema.pre("save", async function (next) {
    if (!this.isModified("token") || !this.token) {
        return next();
    }
    const hash = await bcrypt.hash(this.token, 12);
    this.token = hash;
    next();
});
