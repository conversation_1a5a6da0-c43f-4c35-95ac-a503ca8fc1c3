import { ExecutionContext, createParamDecorator } from "@nestjs/common";
import { IRequestApp } from "src/common/request/interfaces/request.interface";
import { IRoleDocument } from "src/role/interfaces/role.interface";
import { IUserDocument } from "src/users/interfaces/user.interface";

export const GetUser = createParamDecorator(
    <T = IUserDocument>(data: keyof T | undefined, ctx: ExecutionContext): T | IUserDocument => {
        const request: IRequestApp = ctx.switchToHttp().getRequest();
        return data ? (request.__user as any)?.[data] : request.__user;
    });

export const GetDelegatedUser = createParamDecorator(
    <T = IUserDocument>(data: keyof T | undefined, ctx: ExecutionContext): T | IUserDocument => {
        const request: IRequestApp = ctx.switchToHttp().getRequest();
        const user = data ? (request.__delegateUser as any)?.[data] : request.__delegateUser;
        if (!user) {
            throw new Error("Delegated user not found");
        }
        return user;
    }
);

export const GerUserRole = createParamDecorator(
    (data: keyof IRoleDocument | undefined, ctx: ExecutionContext): any => {
        const request: IRequestApp = ctx.switchToHttp().getRequest();
        return data ? request.__user?.role?.[data] : request.__user?.role;
    }
);