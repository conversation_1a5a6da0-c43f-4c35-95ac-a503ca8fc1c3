import { Injectable, UnauthorizedException } from '@nestjs/common';
import { Response } from "express";
import { BadRequestException, ForbiddenException, InternalServerErrorException, NotFoundException } from "@nestjs/common/exceptions";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import { RequestOtpDto } from "../dto/request-otp.dto";
import { Otp } from "../schemas/otp.schema";
import * as bcrypt from "bcrypt";
import { RegisterUserDto } from "../dto/register-user.dto";
import { User, UserDocument } from "src/users/schemas/user.schema";
import { LoginDto, LoginWithProfileDto } from "../dto/login.dto";
import { VerifyOtpDto } from "../dto/verify-otp.dto";
import { JwtService } from "@nestjs/jwt";
import { ResetPasswordDto } from "../dto/reset-password.dto";
import { AuthTypes } from "../../utils/enums/auth.enum";
import { TransactionService } from "src/utils/services/transaction.service";
import { MailService } from "src/mail/services/mail.service";
import * as path from "path";
import { SetPasswordDto } from "../dto/set-password.dto";
import * as jwt from "jsonwebtoken";
import { plainToInstance } from 'class-transformer';
import { GeneralService } from "src/utils/services/general.service";
import { Clients } from "src/users/schemas/clients.schema";
import { Facility } from "src/facility/schemas/facility.schema";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { ChangePasswordDto } from "../dto/change-password.dto";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
import { IUserDocument } from "src/users/interfaces/user.interface";
import { HelperDateService } from "src/common/helper/services/helper.date.service";
import { AuthJwtAccessPayloadDto } from "../dto/jwt/auth.jwt.access-payload.dto";
import { HelperEncryptionService } from "src/common/helper/services/helper.encryption.service";
import { ConfigService } from "@nestjs/config";
import { RoleService } from "src/role/services/role.service";
import { UserService } from "src/users/services/user.service";
import { log } from "console";
import { SessionService } from "src/session/services/session.service";
import { IRequestApp } from "src/common/request/interfaces/request.interface";
import { IDatabaseObjectId } from 'src/common/database/interfaces/database.objectid.interface';
import { Msg91Service } from "src/message/service/msg91.service";
import { RELATION_ENUM } from "src/utils/enums/relation.enum";
import { Gender } from "src/utils/enums/gender.enum";
import { Organizations } from "src/organization/schemas/organization.schema";

@Injectable()
export class AuthService {
    // jwt
    private readonly jwtAccessTokenSecretKey: string;
    private readonly jwtAccessTokenExpirationTime: number;
    private readonly jwtPrefix: string;
    private readonly jwtAudience: string;
    private readonly jwtIssuer: string;

    constructor(
        @InjectModel(Otp.name) private OtpModel: Model<Otp>,
        @InjectModel(User.name) private UserModel: Model<User>,
        @InjectModel(Clients.name) private ClientModel: Model<Clients>,
        @InjectModel(Facility.name) private FacilityModel: Model<Facility>,
        @InjectModel(StaffProfileDetails.name) private StaffProfileModel: Model<StaffProfileDetails>,
        @InjectModel(Organizations.name) private organizationModel: Model<Organizations>,
        private readonly configService: ConfigService,
        private JwtService: JwtService,
        private readonly transactionService: TransactionService,
        private readonly mailService: MailService,
        private readonly generalService: GeneralService,
        private readonly msg91Service: Msg91Service,
        private readonly helperDateService: HelperDateService,
        private readonly helperEncryptionService: HelperEncryptionService,
        private readonly roleService: RoleService,
        private readonly userService: UserService,
        private readonly sessionService: SessionService,
    ) {
        this.jwtAccessTokenSecretKey = this.configService.get<string>(
            'auth.jwt.accessToken.secretKey'
        );
        this.jwtAccessTokenExpirationTime = this.configService.get<number>(
            'auth.jwt.accessToken.expirationTime'
        );
        this.jwtPrefix = this.configService.get<string>('auth.jwt.prefix');
        this.jwtAudience = this.configService.get<string>('auth.jwt.audience');
        this.jwtIssuer = this.configService.get<string>('auth.jwt.issuer');

    }

    async getOrganizationId(user: IUserDocument): Promise<any> {
        const { role } = user;
        let organizationId: any;
        switch (role.type) {
            case ENUM_ROLE_TYPE.ORGANIZATION:
                organizationId = user._id;
                break;
            case ENUM_ROLE_TYPE.WEB_MASTER:
            case ENUM_ROLE_TYPE.TRAINER:
            case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                const staff = await this.StaffProfileModel.findOne({ userId: user.id }).exec();
                organizationId = staff.organizationId;
                break;
            case ENUM_ROLE_TYPE.USER:
                const client = await this.ClientModel.findOne({ userId: user.id }).exec();
                organizationId = client.organizationId;
                break;
            default:
                throw new BadRequestException("Unable to determine your role. Please contact support.");
        }
        if (organizationId) {
            return organizationId;
        }
        throw new BadRequestException("Unable to determine your organization. Please contact support.");
    }

    async createToken(
        user: IUserDocument,
        organization?: Types.ObjectId | string
    ): Promise<{
        tokenType: string,
        roleType: ENUM_ROLE_TYPE,
        loginDate: Date,
        expiresIn: number,
        accessToken: string,
    }> {
        const loginDate = this.helperDateService.create();
        const roleType = user.role.type;

        const payloadAccessToken: AuthJwtAccessPayloadDto =
            await this.createPayloadAccessToken(
                user,
                loginDate,
                organization?.toString()
            );
        const accessToken: string = await this.createAccessToken(
            user._id.toString(),
            payloadAccessToken
        );

        return {
            tokenType: this.jwtPrefix,
            roleType,
            loginDate,
            expiresIn: this.jwtAccessTokenExpirationTime,
            accessToken,
        };
    }

    async createPayloadAccessToken(
        data: IUserDocument,
        loginDate: Date,
        organization: Types.ObjectId | string
    ): Promise<AuthJwtAccessPayloadDto> {
        return plainToInstance(AuthJwtAccessPayloadDto, {
            user: data._id.toString(),
            userId: data._id.toString(),
            type: data.role.type,
            role: data.role._id.toString(),
            roleIsActive: data.role.isActive,
            email: data.email?.toString(),
            loginDate: loginDate,
            organization: organization
        } as Partial<AuthJwtAccessPayloadDto>);
    }

    async createAccessToken(
        subject: string,
        payload: AuthJwtAccessPayloadDto
    ): Promise<string> {
        return this.helperEncryptionService.jwtEncrypt(
            { ...payload },
            {
                secretKey: this.jwtAccessTokenSecretKey,
                expiredIn: this.jwtAccessTokenExpirationTime,
                audience: this.jwtAudience,
                issuer: this.jwtIssuer,
                subject,
            }
        );
    }

    private async sendMail(email: string, template: string, context?: object, attachments?: any) {
        try {
            await this.mailService.sendMail({
                to: email?.toString(),
                subject: "OTP for email verification",
                template: template,
                context: context,
                attachments: attachments,
            });
            return "OTP Sent.Please check your mail!";
        } catch (emailError) {
            console.error("Failed to send OTP email:", emailError.message);
            throw new Error("Failed to send OTP email. Please try again.");
        }
    }
    async createOtp(requestOtpDto: RequestOtpDto, organizationId: string): Promise<void | Number> {
        try {
            return await this.generateOTP(requestOtpDto, organizationId);
        } catch (error) {
            throw new Error(error.message);
        }
    }

    async forgetPasswordOtp(requestOtpDto: RequestOtpDto, organizationId: string): Promise<void | Number> {
        try {
            const user = await this.UserModel.findOne({ [requestOtpDto.type]: requestOtpDto[requestOtpDto.type],  $or: [{ _id: organizationId }, { organizationId }]  }).exec();

            if (!user) throw new BadRequestException(`Account with this ${requestOtpDto.type} does not exist.`);

            return await this.generateOTP(requestOtpDto, organizationId);
        } catch (error) {
            throw new Error(error.message);
        }
    }

    private async generateOTP(requestOtpDto: RequestOtpDto, organizationId: String): Promise<any> {
        try {
            const identifier = requestOtpDto[requestOtpDto.type].toLowerCase();
            let userName: any = "User";
            const user: any = await this.UserModel.findOne({
                [requestOtpDto.type]: identifier,
                 $or: [{ _id: organizationId }, { organizationId }] ,
            }).exec();

            if (user) {
                await user.populate('role')
                userName = (user?.firstName && user?.lastName ? `${user.firstName} ${user.lastName}` : user?.name);
                if (user && user?.role?.type !== ENUM_ROLE_TYPE.USER && requestOtpDto.customerApp) {
                    throw new BadRequestException("Account exists with a different role.");
                }
            }
            const record = await this.OtpModel.findOne({
                for: requestOtpDto[requestOtpDto.type].toLowerCase(),
            }).exec();

            const now = new Date();
            const tenMinutesAgo = new Date(now.getTime() - 10 * 60 * 1000);
            let otp: any;

            if (record) {
                if (record.updatedAt < tenMinutesAgo || !record.otp) {
                    otp = this.generalService.randomOTP();
                    record.otp = otp;
                    await record.save();
                } else {
                    otp = record.otp;
                }
            } else {
                otp = this.generalService.randomOTP();
                const createdOtp = new this.OtpModel({ otp, for: identifier });
                await createdOtp.save();
            }

            if (requestOtpDto.type === AuthTypes.EMAIL) {
                return this.sendMail(
                    requestOtpDto?.email,
                    "request-otp-without-image",
                    { otp, name: userName },
                    // [
                    //     {
                    //         filename: "otp-image.png",
                    //         path: path.resolve(__dirname, "../../../image/otp-image.png"),
                    //         cid: "verificationImage",
                    //     },
                    // ]
                );
            } else if (requestOtpDto.type === AuthTypes.MOBILE) {
                await this.msg91Service.sendOtp(identifier, otp.toString());
            }

            return otp;
        } catch (error) {
            throw new Error(error.message);
        }
    }

    async registerUser(
        registerUserDto: RegisterUserDto,
        organizationId: string,
    ): Promise<{ user: IUserDocument; roleType: string; accessToken: string; tokenType: string; loginDate: Date; organizationId: string | null }> {
        const session = await this.transactionService.startTransaction();
        try {
            const user = await this.UserModel.findOne({ [registerUserDto.type]: registerUserDto[registerUserDto.type],  $or: [{ _id: organizationId }, { organizationId }]  }).exec();

            if (user) throw new BadRequestException(`An account with this ${registerUserDto.type} already exists.`);
            const role = await this.roleService.findOneByType(ENUM_ROLE_TYPE.USER)
            if (!role) {
                throw new InternalServerErrorException("Unable to define role. Please contact to support")
            }
            // if (registerUserDto.password !== registerUserDto.confirmPassword) {
            //     throw new BadRequestException(`Password does not match`);
            // }
            // const salt = await bcrypt.genSalt();
            // const password = await this.generalService.hashPassword(registerUserDto.password, salt);
            let userDetails = {
                name: registerUserDto.name || "",
                firstName: registerUserDto.firstName || "",
                lastName: registerUserDto.lastName || "",
                // password,
                // salt,
                role: role._id,
                isActive: true,
                isUserAcceptTerms: !!registerUserDto.isUserAcceptTerms,
                organizationId,
            };
            const emailRequired = registerUserDto.type === AuthTypes.EMAIL;
            const mobileRequired = registerUserDto.type === AuthTypes.MOBILE;

            if (emailRequired) {
                if (!registerUserDto[AuthTypes.EMAIL]) {
                    throw new Error("Email is required");
                }
                userDetails[AuthTypes.EMAIL] = registerUserDto[AuthTypes.EMAIL];

                if (!registerUserDto[AuthTypes.MOBILE]) {
                    throw new Error("Mobile number is required for registration");
                }
                let checkMobileNoExist = await this.UserModel.findOne({ mobile: registerUserDto[AuthTypes.MOBILE],  $or: [{ _id: organizationId }, { organizationId }]  }).select({ mobile: 1 }).exec();
                if (checkMobileNoExist) throw new BadRequestException(`An account with this ${registerUserDto.type} already exists.`);

                userDetails[AuthTypes.MOBILE] = registerUserDto[AuthTypes.MOBILE];
                userDetails["countryCode"] = registerUserDto?.countryCode ? registerUserDto.countryCode : "+91";
            } else if (mobileRequired) {
                if (!registerUserDto[AuthTypes.MOBILE]) {
                    throw new Error("Mobile number is required");
                }
                userDetails[AuthTypes.MOBILE] = registerUserDto[AuthTypes.MOBILE];
                userDetails["countryCode"] = registerUserDto?.countryCode ? registerUserDto.countryCode : "+91";

                // if (!registerUserDto[AuthTypes.EMAIL]) {
                //     throw new Error("Email is required for registration");
                // }

                if (registerUserDto[AuthTypes.EMAIL]) {
                    let checkEmailExist = await this.UserModel.findOne({ email: registerUserDto[AuthTypes.EMAIL],  $or: [{ _id: organizationId }, { organizationId }]  }).select({ email: 1 }).exec();
                    if (checkEmailExist) throw new BadRequestException(`An account with this ${registerUserDto.type} already exists.`);

                    userDetails[AuthTypes.EMAIL] = registerUserDto[AuthTypes.EMAIL];
                }
            } else {
                throw new Error("Invalid registration type specified");
            }
            const createdUser = new this.UserModel(userDetails);

            await createdUser.save({ session });

            const facilitiesDetailArray = await this.FacilityModel.find({ organizationId: registerUserDto.organizationId, });
            const clientDetails = {
                createdBy: createdUser["_id"],
                userId: createdUser["_id"],
                facilityId: facilitiesDetailArray.length > 0 ? facilitiesDetailArray[0]._id : undefined,
                organizationId: registerUserDto.organizationId,
                address: {
                    state: facilitiesDetailArray.length > 0 ? facilitiesDetailArray[0].address.state : undefined
                }
            };
            if (registerUserDto.facilityId) {
                let facilityValid = await this.FacilityModel.findOne({ organizationId: registerUserDto.organizationId, _id: registerUserDto.facilityId });
                if (!facilityValid) throw new BadRequestException("Facility not found");
            }

            let saveClient = new this.ClientModel(clientDetails);
            await saveClient.save({ session });
            createdUser.password = createdUser.salt = undefined;
            await session.commitTransaction()
            // const accessToken = this.JwtService.sign({ userId: createdUser._id });
            const newUser = await this.userService.findOneWithRoleAndPermissions({ _id: createdUser._id });
            const tokenData = await this.createToken(newUser, registerUserDto.organizationId);
            // return { user: createdUser, accessToken: accessToken };
            return { user: newUser, roleType: tokenData.roleType, accessToken: tokenData.accessToken, tokenType: tokenData.tokenType, loginDate: tokenData.loginDate, organizationId: null }
        } catch (error) {
            console.log(error)
            session.abortTransaction()
            throw new Error(error.message);
        } finally {
            session.endSession()
        }
    }

    async verifyOtp(verifyOtpDto: VerifyOtpDto, organizationId: string): Promise<any> {
        try {
            const identifier = verifyOtpDto[verifyOtpDto.type].toLowerCase();

            if (verifyOtpDto.type === AuthTypes.MOBILE) {
                const msg91Verified = await this.msg91Service.verifyOtp(identifier, verifyOtpDto.otp.toString());
                if (!msg91Verified) {
                    throw new BadRequestException("Invalid or expired OTP");
                }
            }

            const record = await this.OtpModel.findOne({
                for: identifier,
                otp: verifyOtpDto.otp,
            }).exec();

            if (!record) {
                throw new BadRequestException("Invalid OTP");
            }

            const expiredAt = new Date(Date.now() - 10 * 60 * 1000);
            if (record.updatedAt <= expiredAt) {
                throw new BadRequestException("OTP has expired");
            }
            const user = await this.UserModel.findOne<IUserDocument>({ [verifyOtpDto.type]: verifyOtpDto[verifyOtpDto.type].toLowerCase(),  $or: [{ _id: organizationId }, { organizationId }]  })
                .populate([{ path: "role", select: "type" }])
                .exec();
            if (!user) {
                record.otp = null;
                await record.save();
                return {
                    message: "OTP verified",
                    data: {
                        otpVerificationCode: record._id,
                        userExist: false,
                        forgotPasswordRequest: verifyOtpDto.forgotPasswordRequest,
                    },
                };
            } else {
                user.password = user.salt = undefined;
                const accessToken = this.JwtService.sign({ userId: user._id });
                if (user["role"].type === ENUM_ROLE_TYPE.USER) {
                    let imageDetails = await this.ClientModel.findOne({ userId: user._id }, { photo: 1 });
                    user["_doc"]["photo"] = imageDetails?.photo ? imageDetails.photo : "";
                }
                return {
                    message: "OTP verified",
                    data: {
                        otpVerificationCode: record._id,
                        userExist: true,
                        forgotPasswordRequest: verifyOtpDto.forgotPasswordRequest,
                        user: user,
                        accessToken: accessToken,
                    },
                };
            }
        } catch (error) {
            throw new Error(error.message);
        }
    }

    async verifyOtpV2(verifyOtpDto: VerifyOtpDto, organizationId: string): Promise<any> {
        try {
            const identifier = verifyOtpDto[verifyOtpDto.type].toLowerCase();

            if (verifyOtpDto.type === AuthTypes.MOBILE) {
                const msg91Verified = await this.msg91Service.verifyOtp(identifier, verifyOtpDto.otp.toString());
                if (!msg91Verified) {
                    throw new BadRequestException("Invalid or expired OTP");
                }
            }

            const record = await this.OtpModel.findOne({
                for: identifier,
                otp: verifyOtpDto.otp,
            }).exec();

            if (!record) {
                throw new BadRequestException("Invalid OTP");
            }

            const expiredAt = new Date(Date.now() - 10 * 60 * 1000);
            if (record.updatedAt <= expiredAt) {
                throw new BadRequestException("OTP has expired");
            }
            const user = await this.userService.findOneWithRoleAndPermissions({ [verifyOtpDto.type]: verifyOtpDto[verifyOtpDto.type], organizationId }, { password: 0, salt: 0 });
            const isPasswordSet = user?.password ? true : false;
            if (!user) {
                record.otp = null;
                await record.save();
                return {
                    message: "OTP verified",
                    data: {
                        otpVerificationCode: record._id,
                        userExist: false,
                        forgotPasswordRequest: verifyOtpDto.forgotPasswordRequest,
                    },
                };
            }

            const minor = await this.UserModel.find({ parent: user._id, isActive: true }, { password: 0, salt: 0 }, { hydrate: true });
            const accessData = await this.createToken(user, organizationId);
            record.token = accessData.accessToken;
            record.expireAt = new Date(Date.now() + 10 * 60 * 1000);
            record.users = [user._id as any, ...minor.map(doc => doc._id as any)];
            await record.save();

            const clientDetails = await this.ClientModel.find({ userId: { $in: [user._id, ...minor.map(doc => doc._id)] } }, { photo: 1, userId: 1, gender: 1, relation: 1 });
            const profiles = [
                {
                    _id: user._id,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    photo: clientDetails.find(client => client.userId.toString() === user._id.toString())?.photo || "",
                    gender: clientDetails.find(client => client.userId.toString() === user._id.toString())?.gender || "",
                    relation: RELATION_ENUM.PARENT
                },
                ...minor.map(doc => ({
                    _id: doc._id,
                    firstName: doc.firstName,
                    lastName: doc.lastName,
                    photo: clientDetails.find(client => client.userId.toString() === doc._id.toString())?.photo || "",
                    gender: clientDetails.find(client => client.userId.toString() === doc._id.toString())?.gender || "",
                    relation: clientDetails.find(client => client.userId.toString() === doc._id.toString())?.relation || ""
                })).sort((a, b) => a.firstName.localeCompare(b.firstName as any))
            ]
            return {
                message: "OTP verified",
                data: {
                    otpVerificationCode: record?._id,
                    userExist: true,
                    forgotPasswordRequest: verifyOtpDto.forgotPasswordRequest,
                    user,
                    profiles,
                    accessToken: accessData.accessToken,
                    isLoggedIn: false,
                    isPasswordSet: isPasswordSet,
                },
            };
        } catch (error) {
            throw new Error(error.message);
        }
    }

    async generateSignInToken(user: UserDocument): Promise<any> {
        try {
            const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, { expiresIn: "1h" });
            return token;
        } catch (error) {
            throw new Error(error.message);
        }
    }

    async getProfile(user: UserDocument): Promise<any> {
        try {
            let users = []

            if (user.parent) {
                // chile
                users = await this.UserModel.find({ $or: [{ _id: user.parent }, { parent: user.parent }] });
            } else {
                // parent
                users = await this.UserModel.find({ $or: [{ _id: user._id }, { parent: user._id }] });
            }

            const minor = users.filter(doc => doc.parent);
            const parent = users.find(doc => !doc.parent);

            const clientDetails = await this.ClientModel.find({ userId: { $in: [user._id, ...minor.map(doc => doc._id)] } }, { photo: 1, userId: 1, gender: 1, relation: 1 });
            const profiles = [
                {
                    _id: parent._id,
                    firstName: parent.firstName,
                    lastName: parent.lastName,
                    photo: clientDetails.find(client => client.userId.toString() === parent._id.toString())?.photo || "",
                    gender: clientDetails.find(client => client.userId.toString() === parent._id.toString())?.gender || "",
                    relation: RELATION_ENUM.PARENT
                },
                ...minor.map(doc => ({
                    _id: doc._id,
                    firstName: doc.firstName,
                    lastName: doc.lastName,
                    photo: clientDetails.find(client => client.userId.toString() === doc._id.toString())?.photo || "",
                    gender: clientDetails.find(client => client.userId.toString() === doc._id.toString())?.gender || "",
                    relation: clientDetails.find(client => client.userId.toString() === doc._id.toString())?.relation || ""
                })).sort((a, b) => a.firstName.localeCompare(b.firstName as any))
            ]
            return profiles;
        } catch (error) {
            throw new Error(error.message);
        }
    }

    async login(loginDto: LoginDto, allowedRoles: ENUM_ROLE_TYPE[], organizationId: string): Promise<any> {
        try {
            let profiles = [];
            const user = await this.validateUser(loginDto.type, loginDto[loginDto.type], loginDto.password, organizationId);
            if (!user) throw new BadRequestException("Invalid login details");

            if (allowedRoles && !allowedRoles.includes(user.role.type)) {
                throw new BadRequestException("You are not allowed to get access");
            }

            switch (user.role.type) {
                case ENUM_ROLE_TYPE.USER:
                    const clientDetails = await this.ClientModel.findOne(
                        { userId: user._id },
                        { photo: 1, organizationId: 1 }
                    );
                    if (!clientDetails) {
                        throw new BadRequestException("Client profile not found");
                    }
                    user["_doc"]["photo"] = clientDetails.photo || "";
                    // User profiles will set later
                    break;

                case ENUM_ROLE_TYPE.ORGANIZATION:
                    // Organization doesn't need additional checks
                    const organization = await this.organizationModel.findOne({ userId: user._id });
                    if (!organization) {
                        throw new BadRequestException("Organization profile not found");
                    }
                    user["_doc"]["photo"] = organization.logo || "";
                    profiles = [
                        {
                            _id: user._id,
                            firstName: user.firstName || user.name,
                            lastName: user.lastName,
                            photo: organization.logo || "",
                            gender: Gender.OTHER,
                            relation: RELATION_ENUM.OTHER,
                        },
                    ];
                    break;

                case ENUM_ROLE_TYPE.FRONT_DESK_ADMIN:
                case ENUM_ROLE_TYPE.WEB_MASTER:
                case ENUM_ROLE_TYPE.TRAINER:
                    const staffDetails = await this.StaffProfileModel.findOne(
                        { userId: user._id },
                        { organizationId: 1, photo: 1 }
                    );
                    if (!staffDetails) {
                        throw new BadRequestException("Staff profile not found");
                    }
                    let facilityIds = await this.StaffProfileModel.findOne({ userId: user._id }, { facilityId: 1, organizationId: 1 });
                    let facilityArray = facilityIds && facilityIds.facilityId ? facilityIds.facilityId.map((facility) => facility) : [];
                    let facilities = await this.FacilityModel.findOne(
                        {
                            _id: { $in: facilityArray },
                            // isActive: true,
                        },
                        {
                            organizationId: 1,
                        },
                    );
                    if (!facilities) throw new BadRequestException("Facility not found or Deactivated");
                    user["_doc"]["photo"] = staffDetails.profilePicture || "";
                    profiles = [
                        {
                            _id: user._id,
                            firstName: user.firstName || user.name,
                            lastName: user.lastName,
                            photo: staffDetails.profilePicture || "",
                            gender: staffDetails.gender || "",
                            relation: RELATION_ENUM.PARENT
                        },
                    ]
                    break;

                case ENUM_ROLE_TYPE.SUPER_ADMIN:
                    profiles = [
                        {
                            _id: user._id,
                            firstName: user.firstName || user.name,
                            lastName: user.lastName,
                            photo: "",
                            //gender: staffDetails.gender || "",
                            relation: RELATION_ENUM.PARENT
                        },
                    ]
                    break;

                default:
                    throw new BadRequestException("Invalid role type");
            }

            if (!organizationId && user.role.type !== ENUM_ROLE_TYPE.SUPER_ADMIN) {
                throw new BadRequestException("Unable to determine organization");
            }

            // const accessToken = this.JwtService.sign({ userId: user._id });
            const tokenData = await this.createToken(user, new Types.ObjectId(organizationId));
            // let allowedRoles = [ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.WEB_MASTER];
            // let allowedRoles = [ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER, ENUM_ROLE_TYPE.WEB_MASTER];
            // let organizationId = null;
            // if (allowedRoles.includes(user["role"].type)) {
            //     let facilityIds = await this.StaffProfileModel.findOne({ userId: user._id }, { facilityId: 1,organizationId:1 });
            //     let facilityArray = facilityIds && facilityIds.facilityId ? facilityIds.facilityId.map((facility) => facility) : [];
            //     let facilities = await this.FacilityModel.findOne(
            //         {
            //             _id: { $in: facilityArray },
            //            // isActive: true,
            //         },
            //         {
            //             organizationId: 1,
            //         },
            //     );
            //     //if(!facilities) throw new BadRequestException("Facility not found or Deactivated");
            //     organizationId = facilities ? facilities["organizationId"] : null;
            //     // if(organizationId) {
            //     //     let organization = await this.UserModel.findOne({ _id: organizationId, isActive: true });
            //     //     if(!organization) throw new BadRequestException("Can't login.Organization is currently Deactivated");
            //     // }
            // }
            // if(user["role"] === ENUM_ROLE_TYPE.USER){
            //     let organization = await this.UserModel.findOne({ _id: organizationId, isActive: true });
            //     if(!organization) throw new BadRequestException("Can't login.Organization is currently Deactivated");

            if (user["role"].type === ENUM_ROLE_TYPE.USER && !organizationId) throw new BadRequestException("Can't login. Organization is currently Deactivated");

            if (user["role"].type === ENUM_ROLE_TYPE.USER && !user.parent) {
                const minor = await this.UserModel.find({ parent: user._id, isActive: true }, { password: 0, salt: 0 }, { hydrate: true });
                const clientDetails = await this.ClientModel.find({ userId: { $in: [user._id, ...minor.map(doc => doc._id)] } }, { photo: 1, userId: 1, gender: 1, relation: 1 });
                profiles = [
                    {
                        _id: user._id,
                        firstName: user.firstName || user.name,
                        lastName: user.lastName,
                        photo: clientDetails.find(client => client.userId.toString() === user._id.toString())?.photo || "",
                        gender: clientDetails.find(client => client.userId.toString() === user._id.toString())?.gender || "",
                        relation: RELATION_ENUM.PARENT
                    },
                    ...minor.map(doc => ({
                        _id: doc._id,
                        firstName: doc.firstName || doc.name,
                        lastName: doc.lastName,
                        photo: clientDetails.find(client => client.userId.toString() === doc._id.toString())?.photo || "",
                        gender: clientDetails.find(client => client.userId.toString() === doc._id.toString())?.gender || "",
                        relation: clientDetails.find(client => client.userId.toString() === doc._id.toString())?.relation || ""
                    }))
                ]
            }

            return {
                user,
                profiles,
                roleType: tokenData.roleType,
                accessToken: tokenData.accessToken,
                tokenType: tokenData.tokenType,
                loginDate: tokenData.loginDate,
                organizationId: organizationId
            }
        } catch (error) {
            if (error instanceof BadRequestException) {
                throw new BadRequestException(error.message);
            }
            if (error instanceof NotFoundException) {
                throw new NotFoundException(error.message);
            }
            if (error instanceof ForbiddenException) {
                throw new ForbiddenException(error.message);
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async startDelegateSession(request: IRequestApp, delegateUser: IUserDocument): Promise<any> {
        try {
            // Create session document in database
            const session = await this.sessionService.create(
                request,
                {
                    user: delegateUser._id,
                }
            );

            // Store user data in Redis
            await this.sessionService.cacheLoginSession(delegateUser, session);

            // Initialize session if it doesn't exist
            if (!request.session) {
                request.session = {} as any;
            }

            // Set session data
            request.session.userDelegatedId = delegateUser._id.toString();
            request.session.userDelegatedSessionId = session._id.toString();

            // Save session
            await new Promise<void>((resolve, reject) => {
                request.session.save((err: any) => {
                    if (err) {
                        reject(err);
                    }
                    resolve();
                });
            });

            return session._id;
        } catch (error) {
            // this.logger.error(`Failed to start new session: ${error.message}`);
            throw new ForbiddenException(`Session creation failed: ${error.message}`);
        }
    }

    async startNewSession(request: IRequestApp, user: IUserDocument): Promise<any> {
        try {
            // Create session document in database
            const session = await this.sessionService.create(
                request,
                {
                    user: user._id,
                }
            );

            // Store user data in Redis
            await this.sessionService.cacheLoginSession(user, session);

            // Initialize session if it doesn't exist
            if (!request.session) {
                request.session = {} as any;
            }

            // req.session.userData = { userId: 123, name: 'John' };
            // req.session.save((err) => {
            // if (err) console.error('Failed to save session:', err);
            // });

            // Set session data
            request.session.userId = user._id.toString();
            request.session.userSessionId = session._id.toString();

            // Save session
            await new Promise<void>((resolve, reject) => {
                request.session.save((err: any) => {
                    if (err) {
                        reject(err);
                    }
                    resolve();
                });
            });

            return session._id;
        } catch (error) {
            // this.logger.error(`Failed to start new session: ${error.message}`);
            throw new ForbiddenException(`Session creation failed: ${error.message}`);
        }
    }

    async setCookieData(
        res: Response,
        user: IUserDocument | UserDocument,
        userSessionId: string,
        tokenData: any,
        loginDate: Date,
        organizationId: string | null
    ): Promise<Response> {
        // Define cookie options with explicit secure and sameSite attributes
        // No maxAge specified - makes it a session cookie (expires when browser closes)
        const cookieOptions = {
            secure: this.configService.get<boolean>('auth.session.cookie.secure'),
            sameSite: this.configService.get<'lax' | 'strict' | 'none'>('auth.session.cookie.sameSite'),
            httpOnly: this.configService.get<boolean>('auth.session.cookie.httpOnly')
        };

        const cookieData = {
            userId: user._id.toString(),
            userSessionId,
            loginDate,
            roleType: tokenData.roleType,
            accessToken: tokenData.accessToken,
            tokenType: tokenData.tokenType,
            organizationId: organizationId
        };

        // Set cookies directly using headers to ensure proper attributes
        for (const [key, value] of Object.entries(cookieData)) {
            // Format the cookie string manually to ensure attributes are set correctly
            const cookieValue = encodeURIComponent(typeof value === 'object' ? JSON.stringify(value) : String(value));

            // Build cookie string with proper attributes (no Max-Age for session cookies)
            let cookieString = `${key}=${cookieValue}; Path=/`;

            if (cookieOptions.httpOnly) {
                cookieString += '; HttpOnly';
            }

            if (cookieOptions.secure) {
                cookieString += '; Secure';
            }

            if (cookieOptions.sameSite) {
                cookieString += `; SameSite=${cookieOptions.sameSite}`;
            }

            // Append the cookie to the response headers
            res.append('Set-Cookie', cookieString);
        }

        return res;
    }

    async changeUserRoleAuto<T>(user: UserDocument | IUserDocument): Promise<T> {
        const role = user.role;
        if (Types.ObjectId.isValid(user._id)) {
            await user.populate([
                {
                    path: "role",
                    populate: {
                        path: "policies",
                        populate: {
                            path: "permissions",
                            select: "-createdAt -updatedAt -__v",
                        },
                        select: "-createdAt -updatedAt -__v",
                    },
                },
                {
                    path: "assignedPolicies",
                    populate: {
                        path: "permissions",
                        select: "-createdAt -updatedAt -__v",
                    },
                    select: "-createdAt -updatedAt -__v",
                },
                {
                    path: "restrictedPolicies",
                    populate: {
                        path: "permissions",
                        select: "-createdAt -updatedAt -__v",
                    },
                    select: "-createdAt -updatedAt -__v",
                },
            ]);
            return user as T;
        }
        const roleDoc = await this.roleService.findOne({ type: role });
        if (!roleDoc) {
            throw new Error("Role not valid");
        }
        user.role = roleDoc._id;
        await user.save();
        await user.populate([
            {
                path: "role",
                populate: {
                    path: "policies",
                    populate: {
                        path: "permissions",
                        select: "-createdAt -updatedAt -__v",
                    },
                    select: "-createdAt -updatedAt -__v",
                },
            },
            {
                path: "assignedPolicies",
                populate: {
                    path: "permissions",
                    select: "-createdAt -updatedAt -__v",
                },
                select: "-createdAt -updatedAt -__v",
            },
            {
                path: "restrictedPolicies",
                populate: {
                    path: "permissions",
                    select: "-createdAt -updatedAt -__v",
                }
            },
        ]);
        return user as T;
    }

    // async loginWithProfile(loginDto: LoginWithProfileDto): Promise<any> {
    //     try {
    //         const token = await this.OtpModel.findOne({ token: { $exists: true }, users: loginDto.profile, expireAt: { $gt: new Date() } }).sort({ updatedAt: -1 }).exec();
    //         if (!token) throw new BadRequestException("Token has expired or Invalid");
    //         if (!token.validateToken(loginDto.token)) throw new BadRequestException("Token is invalid");

    //         const user = await this.userService.findOneWithRoleAndPermissions({ _id: loginDto.profile, isActive: true });
    //         if (!user) throw new BadRequestException("Invalid login details");

    async loginWithProfile(loginDto: LoginWithProfileDto, authorization?: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            let user = await this.userService.findOneWithRoleAndPermissions({ _id: loginDto.profile, isActive: true });
            if (!user) throw new BadRequestException("Profile not found");
            if (loginDto.token) {
                // First validate token as JWT
                const decodedToken = this.JwtService.verify(loginDto.token, {
                    secret: this.configService.get<string>("JWT_SECRET"),
                });

                // Then check in database
                const token = await this.OtpModel.findOne({
                    token: { $exists: true },
                    users: loginDto.profile,
                    expireAt: { $gt: new Date() }
                }).sort({ updatedAt: -1 }).exec();

                if (!token) throw new BadRequestException("Token has expired or Invalid");
                if (!token.validateToken(loginDto.token)) throw new BadRequestException("Token is invalid");

                token.token = undefined;
                token.expireAt = new Date();
                await token.save({ session });
            } else if (authorization && authorization?.startsWith("Bearer ")) {
                const payload: jwt.JwtPayload = this.JwtService.verify(authorization?.replace("Bearer ", ""), {
                    secret: this.configService.get<string>("JWT_SECRET"),
                });
                if (!payload?.user) {
                    throw new BadRequestException("Invalid login details");
                }
                const authUser = user._id.toString() === payload.user.toString() ? user : await this.UserModel.findOne({ _id: payload.user, isActive: true }).exec();
                if (!authUser) throw new BadRequestException("Invalid login details");

                if (authUser?._id?.toString() !== user?._id?.toString()) {
                    const parentId = authUser.parent ? authUser.parent : authUser._id;

                    if (user.parent?.toString() !== parentId.toString() && user._id.toString() !== parentId.toString()) {
                        throw new BadRequestException("Invalid login details");
                    }
                }
            } else {
                throw new BadRequestException("Authentication failed. Please try again.");
            }

            let organizationId = null;
            if (user["role"].type !== ENUM_ROLE_TYPE.USER) {
                let facilityIds = await this.StaffProfileModel.findOne({ userId: user._id }, { facilityId: 1, organizationId: 1 });
                let facilityArray = facilityIds && facilityIds.facilityId ? facilityIds.facilityId.map((facility) => facility) : [];
                let facilities = await this.FacilityModel.findOne(
                    {
                        _id: { $in: facilityArray },
                        // isActive: true,
                    },
                    {
                        organizationId: 1,
                    },
                );
                organizationId = facilities ? facilities["organizationId"] : null;
            }
            if (user["role"].type === ENUM_ROLE_TYPE.USER) {
                let client = await this.ClientModel.findOne({ userId: user._id });
                if (!client) throw new BadRequestException("Can't login. Unable to find client details. Please contact support.");
                organizationId = client.organizationId
            }
            if (organizationId) {
                let organization = await this.UserModel.findOne({ _id: organizationId, isActive: true });
                if (!organization) throw new BadRequestException("Can't login.Organization is currently Deactivated");
            } else {
                throw new BadRequestException("Can't login.Organization is currently Deactivated");
            }

            const tokenData = await this.createToken(user, new Types.ObjectId(organizationId));

            await this.transactionService.commitTransaction(session);
            return {
                user,
                accessToken: tokenData.accessToken,
                organizationId: organizationId,
                isLoggedIn: true
            };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            if (error.name === 'JsonWebTokenError') {
                throw new BadRequestException("Invalid token format");
            }
            if (error instanceof BadRequestException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        } finally {
            session.endSession();
        }
    }

    async validateUser(type: string, loginString: string, password: string, organizationId: string): Promise<IUserDocument> {
        try {
            const user = await this.userService.findOneWithRoleAndPermissions({
                [type]: loginString,
                $or: [{ parent: { $exists: false } }, { parent: null }],
            });
            if (!user) throw new BadRequestException("Invalid username or password");
            const role = await this.roleService.findOneByType(ENUM_ROLE_TYPE.SUPER_ADMIN);
            if (
                user.role?._id.toString() !== role._id.toString() &&
                !(user._id?.toString() === organizationId?.toString() || user.organizationId?.toString() === organizationId?.toString())
            )
                throw new BadRequestException(`Account with this ${type} does not exist.`);

            if (!user?.salt && !user?.password) {
                throw new BadRequestException("You haven't set a password yet.");
            }
            if (user.isActive === false) {
                throw new ForbiddenException("User Account is disabled");
            }

            const passwordMatches = await user.validatePassword(password);
            if (!passwordMatches) {
                throw new BadRequestException("Invalid username or password");
            }

            user.password = user.salt = undefined;

            return user;
        } catch (error) {
            if (error instanceof BadRequestException) {
                throw new BadRequestException(error.message);
            }
            if (error instanceof NotFoundException) {
                throw new NotFoundException(error.message);
            }
            if (error instanceof ForbiddenException) {
                throw new ForbiddenException(error.message);
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async resetPassword(resetPasswordDto: ResetPasswordDto, organizationId: string): Promise<any> {
        try {
            let record = await this.OtpModel.findOne({ _id: resetPasswordDto.otpVerificationCode, for: resetPasswordDto[resetPasswordDto.type].toLowerCase() }).exec();

            if (!record) throw new BadRequestException("Invalid token");

            // const user = await this.UserModel.findOne({ [resetPasswordDto.type]: resetPasswordDto[resetPasswordDto.type] }).exec();
            const user = await this.userService.findOneWithRoleAndPermissions({ [resetPasswordDto.type]: resetPasswordDto[resetPasswordDto.type], $or: [{ _id: organizationId }, { organizationId }] });

            if (!user) throw new BadRequestException(`Account with this ${resetPasswordDto.type} does not exist.`);
            if (resetPasswordDto.password !== resetPasswordDto.confirmPassword) {
                throw new BadRequestException(`Password do not match`);
            }
            const salt = await bcrypt.genSalt();
            const password = await this.generalService.hashPassword(resetPasswordDto.password, salt);

            user.salt = salt;
            user.password = password;

            await user.save();
            await this.OtpModel.findByIdAndDelete(record._id);

            user.password = user.salt = undefined;

            // const accessToken = this.JwtService.sign({ userId: user._id });
            const tokenData = await this.createToken(user, null);

            return { user: user, accessToken: tokenData.accessToken };
        } catch (error) {
            throw new Error(error.message);
        }
    }

    async setPassword(setPasswordDto: SetPasswordDto, organizationId: string): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            let uid = setPasswordDto.uid;
            let userEmail = setPasswordDto.email;
            let checkValidUserDetails = await this.OtpModel.find({ otp: uid, for: userEmail.toLowerCase() });
            if (checkValidUserDetails.length === 0) throw new BadRequestException("Invalid uid");
            const salt = await bcrypt.genSalt();
            const password = await this.generalService.hashPassword(setPasswordDto.password, salt);
            let obj = {
                salt: salt,
                password: password,
            };
            let savePassword = await this.UserModel.findOneAndUpdate({ email: userEmail, organizationId }, { $set: obj }, { new: true, session: session });
            await this.OtpModel.deleteMany({ for: userEmail.toLowerCase() }, { session });
            await this.transactionService.commitTransaction(session);
            savePassword.password = savePassword.salt = undefined;
            return { user: savePassword };
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }

    async getById(id: string): Promise<any> {
        // const user = await this.userService.findOneById(new Types.ObjectId(id));
        const user = await this.userService.findOneWithRoleAndPermissions({ _id: new Types.ObjectId(id) });
        if (!user) {
            throw new NotFoundException("User not found");
        }
        await user.populate([
            { path: "role", select: "type isActive" },
        ]);
        if (user.isActive === false) {
            throw new BadRequestException("User Account is disabled");
        }
        user.password = user.salt = undefined;
        return user;
    }

    async getStaffByPin(pin: string, organizationId: IDatabaseObjectId): Promise<IUserDocument> {
        const staff = await this.StaffProfileModel.aggregate([
            {
                $match: {
                    organizationId: new Types.ObjectId(organizationId)
                }
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'userId',
                    foreignField: '_id',
                    as: 'user'
                }
            },
            {
                $unwind: '$user'
            },
            {
                $match: {
                    'user.pin': pin,
                }
            },
            {
                $limit: 10
            }
        ]);
        if (staff.length > 1) {
            throw new BadRequestException("Multiple users found with same pin, please contact administrator");
        }
        if (staff.length === 0) {
            return null;
        }
        const user = this.userService.findOneWithRoleAndPermissions({ _id: staff[0].userId });
        return user;
    }

    async updatePassword(changePasswordDto: ChangePasswordDto, user: Object): Promise<any> {
        const hash = await bcrypt.hash(changePasswordDto.oldPassword, user["salt"]);
        if (user["password"] === hash) {
            const salt = await bcrypt.genSalt();
            const password = await this.generalService.hashPassword(changePasswordDto.newPassword, salt);
            let updateUser = await this.UserModel.findByIdAndUpdate(
                user["id"],
                {
                    $set: {
                        salt: salt,
                        password: password,
                    },
                },
                {
                    new: true,
                },
            );
            return updateUser;
        } else {
            throw new BadRequestException("Incorrect password");
        }
    }

    async validateAndUpdateSession(
        request: IRequestApp,
        userId: IDatabaseObjectId,
        currentSessionId?: IDatabaseObjectId,
    ): Promise<{
        isValid: boolean;
        user: IUserDocument;
        session: any;
    }> {
        try {
            // 1. Validate user exists and is active
            const user = await this.userService.findOneWithRoleAndPermissions({
                _id: new Types.ObjectId(userId),
                isActive: true
            });

            if (!user) {
                throw new UnauthorizedException('User not found or inactive');
            }

            // 2. Check if there's an existing session
            if (currentSessionId) {
                const existingSession = await this.sessionService.findOneById(
                    new Types.ObjectId(currentSessionId)
                );

                if (existingSession) {
                    // Check if session is still valid
                    // const isSessionActive = await this.sessionService.checkSessionActivity(
                    //     new Types.ObjectId(currentSessionId)
                    // );

                    // if (!isSessionActive) {
                    //     // If session expired, revoke it
                    // }
                    await this.sessionService.updateRevoke(
                        existingSession
                    );
                }
            }

            // 3. Create new session if no valid session exists
            const newSession = await this.startNewSession(request, user);

            return {
                isValid: true,
                user,
                session: newSession,
            };

        } catch (error) {
            throw new BadRequestException(
                `Session validation failed: ${error.message}`
            );
        }
    }

    async refreshUserSession(
        request: IRequestApp,
        user: IUserDocument
    ): Promise<{
        user: IUserDocument;
        userSessionId: string;
        roleType: string;
    }> {
        const sessionData = await this.validateAndUpdateSession(
            request,
            user._id
        );

        if (!sessionData.isValid) {
            throw new UnauthorizedException('Invalid session');
        }

        // const loginDate = this.helperDateService.create();
        // Set new cookie data
        // await this.setCookieData(
        //     response,
        //     sessionData.user,
        //     sessionData.session._id.toString(),
        //     sessionData.accessToken,
        //     loginDate,
        //     sessionData.user.organizationId || null
        // );

        return {
            user: sessionData.user,
            userSessionId: sessionData.session._id,
            roleType: sessionData.user.role.type
        };
    }

    async encryptPin(pin: string, organizationId: IDatabaseObjectId | string): Promise<string> {
        const salt = `$2b$10$${organizationId.toString().padEnd(22, '.')}`;
        return bcrypt.hash(pin, salt);
    }

    async validatePin2(pin: string, organizationId: IDatabaseObjectId): Promise<any> {
        const encryptedPin = await this.encryptPin(pin, organizationId);
        const pinCheck = await this.StaffProfileModel.aggregate([
            {
                $match: {
                    organizationId: new Types.ObjectId(organizationId)
                }
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'userId',
                    foreignField: '_id',
                    as: 'user'
                }
            },
            {
                $unwind: '$user'
            },
            {
                $match: {
                    // 'user._id': new Types.ObjectId(user._id),
                    'user.pin': encryptedPin,
                }
            },
            {
                $limit: 1
            }
        ]);
        return pinCheck.length > 0 ? pinCheck[0] : null;
    }

    async setCustomerPassword(resetPasswordDto: ResetPasswordDto, organizationId: string): Promise<any> {
        try {
            let record = await this.OtpModel.findOne({ _id: resetPasswordDto.otpVerificationCode, for: resetPasswordDto[resetPasswordDto.type].toLowerCase() }).exec();
            if (!record) throw new BadRequestException("Invalid token");

            const user = await this.UserModel.findOne({ [resetPasswordDto.type]: resetPasswordDto[resetPasswordDto.type], parent: null,  $or: [{ _id: organizationId }, { organizationId }]  }).exec();

            if (!user) throw new BadRequestException(`${[resetPasswordDto.type]} not exists`);
            if (resetPasswordDto.password !== resetPasswordDto.confirmPassword) {
                throw new BadRequestException(`Password does not match`);
            }
            const salt = await bcrypt.genSalt();
            const password = await this.generalService.hashPassword(resetPasswordDto.password, salt);

            user.salt = salt;
            user.password = password;

            await user.save();
            await this.OtpModel.findByIdAndDelete(record._id);

            user.password = user.salt = undefined;

            const accessToken = this.JwtService.sign({ userId: user._id });

            return { user: user, accessToken: accessToken };
        } catch (error) {
            throw new Error(error.message);
        }
    }

}
