import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsStrongPassword } from "class-validator";

export class ChangePasswordDto {
    @ApiProperty({
        description: "Old password of the account",
        example: "N3wStr0ngP@ss!",
    })
    @IsNotEmpty({ message: "Password is required" })
    @IsStrongPassword({}, { message: "Invalid old password" })
    oldPassword: string;

    @ApiProperty({
        description: "A new strong password for the user account. Must meet the strong password criteria.",
        example: "N3wStr0ngP@ss!",
    })
    @IsNotEmpty({ message: "Password is required" })
    @IsStrongPassword({}, { message: "Password must be strong: include uppercase, lowercase, numbers, and special characters" })
    newPassword: string;

    @ApiProperty({
        description: "A strong password for the user account. Must meet the strong password criteria.",
        example: "N3wStr0ngP@ss!",
    })
    @IsNotEmpty({ message: "Confirm Password is required" })
    @IsStrongPassword({}, { message: "Confirm Password must be strong: include uppercase, lowercase, numbers, and special characters" })
    confirmPassword: string;
}
