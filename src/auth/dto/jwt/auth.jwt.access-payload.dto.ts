import { faker } from '@faker-js/faker';
import { ApiProperty } from '@nestjs/swagger';
import { ENUM_ROLE_TYPE } from 'src/role/enums/role.enum';

export class AuthJwtAccessPayloadDto {
    @ApiProperty({
        required: true,
    })
    user: string;

    @ApiProperty({
        required: true,
    })
    userId: string;

    @ApiProperty({
        required: true,
    })
    email: string;

    @ApiProperty({
        required: true,
    })
    organization: string;

    @ApiProperty({
        required: true,
    })
    role: string;

    @ApiProperty({
        required: true,
    })
    roleIsActive: boolean;

    @ApiProperty({
        required: true,
    })
    loginDate: Date;

    @ApiProperty({
        required: true,
    })
    type: ENUM_ROLE_TYPE;

    @ApiProperty({
        required: false,
    })
    iat?: number;

    @ApiProperty({
        required: false,
    })
    nbf?: number;

    @ApiProperty({
        required: false,
    })
    exp?: number;

    @ApiProperty({
        required: false,
    })
    aud?: string;

    @ApiProperty({
        required: false,
    })
    iss?: string;

    @ApiProperty({
        required: false,
    })
    sub?: string;
}
