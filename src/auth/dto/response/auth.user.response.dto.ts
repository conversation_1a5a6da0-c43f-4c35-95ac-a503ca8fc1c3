
import { ApiProperty, PickType } from "@nestjs/swagger";
import { IPolicyDocument } from "src/policy/interfaces/policy.interface";
import { IRoleDocument } from "src/role/interfaces/role.interface";
import { Types } from 'mongoose';


export class AuthUserResponseDto{

    @ApiProperty({
        description: "The user's unique identifier",
        required: true,
        example: "659d268dee4b6081dacd41fd",
    })
    _id: Types.ObjectId;

    @ApiProperty({
        description: "The user's name",
        required: false,
        example: "<PERSON>",
    })
    name: string;
    
    @ApiProperty({
        description: "The user's first name",
        required: false,
        example: "<PERSON>",
    })
    firstName: string;

    @ApiProperty({
        description: "The user's last name",
        required: false,
        example: "<PERSON>e",
    })
    lastName: string;

    @ApiProperty({
        description: "The user's mobile number",
        required: false,
        example: "9876543210",
    })
    mobile: string;

    @ApiProperty({
        description: "The user's country code",
        required: false,
        example: "+91",
    })
    countryCode: string;

    @ApiProperty({
        description: "The user's email address",
        required: false,
        example: "<EMAIL>",
    })
    email: string;

    @ApiProperty({
        description: "The user's active status",
        required: true,
        example: true,
    })
    isActive: boolean;

    @ApiProperty({
        description: "The user's new user status",
        required: true,
        example: true,
    })
    newUser: boolean;

    @ApiProperty({
        description: "The user's role",
        required: true,
        example: "user",
    })
    role: IRoleDocument;

    @ApiProperty({
        description: "The user's assigned policies",
        required: false,
        example: ["policy1", "policy2"],
    })
    assignedPolicies: IPolicyDocument[];

    @ApiProperty({
        description: "The user's restricted policies",
        required: false,
        example: ["policy3", "policy4"],
    })
    restrictedPolicies: IPolicyDocument[];

    @ApiProperty({
        description: "The user's creation date",
        required: true,
        example: "2023-08-01T12:00:00.000Z",
    })
    createdAt: Date;

    @ApiProperty({
        description: "The user's last update date",
        required: true,
        example: "2023-08-01T12:00:00.000Z",
    })
    updatedAt: Date;

}
