import { Injectable, NotFoundException, InternalServerErrorException, BadRequestException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { ConfigService } from "@nestjs/config";
import { BookDemoDto } from "../dto/book-demo.dto";
import { BookDemo } from 'src/website-api/schema/book-demo.schema';
import { MailService } from "src/mail/services/mail.service";
import { DATABASE_PRIMARY_CONNECTION_NAME } from "src/common/database/constants/database.constant";

@Injectable()
export class BookDemoService {
    private readonly contactEmail: string;
    constructor(
        @InjectModel(BookDemo.name, DATABASE_PRIMARY_CONNECTION_NAME) private readonly BookDemoModel: Model<BookDemo>,
        private readonly mailService: MailService,
        private readonly configService: ConfigService

    ) {
        this.contactEmail = this.configService.getOrThrow<string>("CONTACT_US_MAIL");
    }

    async createDemoRequest(bookDemoDto: BookDemoDto) {
        try {
            if (!bookDemoDto || Object.keys(bookDemoDto).length === 0) {
                throw new BadRequestException('Invalid request data.');
            }

            const newBookDemo = new this.BookDemoModel(bookDemoDto);
            await newBookDemo.save();
            this.mailService.sendMail({
                to: this.contactEmail,
                subject: `New Demo Booking Receivied`,
                template: "book-demo",
                context: bookDemoDto,
            });
            return newBookDemo;
        } catch (error) {
            console
            if (error.name === 'ValidationError') {
                throw new BadRequestException(error.message);
            }
            console.error('Error creating demo request:', error);
            throw new InternalServerErrorException('Could not process your request. Please try again later.');
        }
    }
}
