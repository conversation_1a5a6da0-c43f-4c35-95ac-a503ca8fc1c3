import { Modu<PERSON> } from '@nestjs/common';
import { BookDemoService } from './service/book-demo.service';
import { ContactUsService } from './service/contact-us.service'
import { BookDemoController } from './controller/book-demo.controller';
import { <PERSON><PERSON>cusController } from './controller/contactUs.controller';
import { MongooseModule } from "@nestjs/mongoose";
import { BookDemo, BookDemoSchema } from './schema/book-demo.schema';
import { ContactUs, ContactUsSchema } from './schema/contact-us.schema'
import { MailModule } from "src/mail/mail.module";
import { DATABASE_PRIMARY_CONNECTION_NAME } from 'src/common/database/constants/database.constant';


@Module({
  imports: [
    MailModule,
    MongooseModule.forFeature([
      { name: BookDemo.name, schema: BookDemoSchema },
      { name: ContactUs.name, schema: ContactUsSchema }
    ], DATABASE_PRIMARY_CONNECTION_NAME)
  ],
  controllers: [BookDemoController, ContatcusController],
  providers: [BookDemoService,ContactUsService],
  exports: [BookDemoService,ContactUsService]
})
export class BookDemoModule { }
