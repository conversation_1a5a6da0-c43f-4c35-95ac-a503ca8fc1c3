import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { ObjectId, SchemaTypes, Document,Types } from 'mongoose';

@Schema({ timestamps: true })
export class BookDemo extends Document {
    @Prop({ type: String, required: true })
    name: string;
    @Prop({ type: String, required: true })
    email: string;
    @Prop({ type: String, default: true })
    phoneNo: string;
}

export const BookDemoSchema = SchemaFactory.createForClass(BookDemo);
