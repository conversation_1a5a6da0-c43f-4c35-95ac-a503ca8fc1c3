import { BadRequestException, Body, Controller, Post, UseGuards, Get, Param, NotFoundException, Patch, Delete } from "@nestjs/common";
import { ApiBearerAuth, ApiOperation, ApiTags } from "@nestjs/swagger";
import { AuthGuard } from "@nestjs/passport";
import { RolesGuard } from "src/auth/roles.guard";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { GetUser } from "src/auth/decorators/get-user.decorator";
import { Roles } from "src/auth/decorators/roles.decorator";
import { MembershipService } from "../service/membership.service"
import { CreateMembershipDto, MembershipListDto } from "../dto/membership.dto";
import { AuthJwtAccessProtected } from "src/auth/decorators/auth.jwt.decorator";
import { PolicyAbilityRoleProtected } from "src/policy/decorators/policy.decorator";
@ApiTags("membership")
@ApiBearerAuth()
@Controller("membership")
export class membershipController {
    constructor(private readonly membershipService: MembershipService) { }
    @Post('/create')
    @ApiOperation({ summary: "Create a new Membership" })
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN)
    @AuthJwtAccessProtected()
    async createMember(@Body() createMembershipDto: CreateMembershipDto, @GetUser() user: any,): Promise<{ message: String; data: any }> {
        let output = await this.membershipService.createMembership(createMembershipDto, user);
        return {
            message: "Membership created successfully",
            data: output,
        };
    }
    @Post("/list")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN, ENUM_ROLE_TYPE.TRAINER)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get all the Membership of the User" })
    async membershipList(@GetUser() user, @Body() membershiplistdto: MembershipListDto): Promise<any> {
        let allMembershipList = await this.membershipService.getMembershipList(membershiplistdto, user)
        return allMembershipList;
    }
    @Get(":membershipId")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Get Membership  by ID" })
    async getMembership(@GetUser() user, @Param("membershipId") membershipId: string) {
        let membershipDetail = await this.membershipService.getMembershipById(user, membershipId)
        return membershipDetail;
    }
    @Patch("/status/:membershipId")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION, ENUM_ROLE_TYPE.WEB_MASTER, ENUM_ROLE_TYPE.FRONT_DESK_ADMIN)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Update the membership Status" })
    async membershipStatusUpdate(@Param("membershipId") membershipId: string, @GetUser() user, @Body() status): Promise<any> {
        const membershipStatusUpdate = await this.membershipService.updateMembershipStatus(status, membershipId);
        return {
            message: 'Status Update Successfully',
        }
    }
    @Patch("/counter/:membershipId")
    @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION)
    @AuthJwtAccessProtected()
    @ApiOperation({ summary: "Update the membership counter by the membership Id" })
    async membershipCounterUpdate(@Param("membershipId") membershipId: string, @GetUser() user, @Body() counter): Promise<any> {
        const membershipCounterUpdate = await this.membershipService.updateMembershipCounter(counter, membershipId);
        return {
            message: 'Counter Update Successfully',
        }
    }
 @Patch("/update/:membershipId")
 @PolicyAbilityRoleProtected(ENUM_ROLE_TYPE.ORGANIZATION)
 @AuthJwtAccessProtected()
  @ApiOperation({ summary: "Membership Update" })
  async roomUpdate(@Param("membershipId") membershipId: string, @GetUser() user, @Body() updateMembershipDto: CreateMembershipDto): Promise<{ message: String; data: any }> {
    let organizationId = user._id;
    let output = await this.membershipService.updateMembership(updateMembershipDto, membershipId, organizationId);
    return {
      message: "Room update Successfully",
      data: output,
    };
  }

}