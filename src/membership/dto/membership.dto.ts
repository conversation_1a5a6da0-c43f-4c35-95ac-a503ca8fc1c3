import {
    IsBoolean,
    IsNotEmpty,
    IsOptional,
    IsString,
    IsArray,
    ValidateNested,
    IsMongoId,
    IsNumber
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class CreateMembershipDto {
    @ApiProperty({
        description: 'The name of the Membership',
        type: String,
        required: true,
        example: 'Gold',
    })
    @IsString()
    @IsNotEmpty()
    @Type(() => String)
    name: string;

    @ApiProperty({
        description: 'The prefix of the Membership',
        type: String,
        required: true,
        example: 'GMW',
    })
    @IsString()
    @IsNotEmpty()
    @Type(() => String)
    prefix: string;

    @ApiProperty({
        description: 'Whether the Membership is active',
        type: Boolean,
        required: false,
        default: true,
        example: true,
    })
    @IsOptional()
    @IsBoolean()
    @Type(() => Boolean)
    isActive?: boolean;

    @ApiProperty({
        description: 'The counter to be from where Membership Started ',
        type: Number,
        required: true,
        example: 1002,
    })
    @IsNumber()
    @IsNotEmpty()
    @Type(() => Number)
    counter: number;
}
export class MembershipListDto {
    @ApiProperty({
        description: 'Search keyword for filtering features by name',
        type: String,
        required: false,
        example: 'Premium',
      })
      @IsOptional()
      @IsString()
      search?: string;
    
      @ApiProperty({
        description: 'Filter features by active status',
        type: Boolean,
        required: false,
        example: true,
      })
      @IsOptional()
      @IsBoolean()
      isActive?: boolean;
    
      @ApiProperty({
        description: 'Number of items to skip for pagination',
        type: Number,
        required: false,
        example: 0,
      })
      @IsOptional()
      @IsNumber()
      skip: number = 0;
    
      @ApiProperty({
        description: 'Number of items to return for pagination',
        type: Number,
        required: false,
        example: 10,
      })
      @IsOptional()
      @IsNumber()
      limit: number = 10;
}