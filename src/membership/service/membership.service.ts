import { Injectable, BadRequestException, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, PipelineStage, Types } from "mongoose";
import { membership } from "../schema/membership.schema";
import { TransactionService } from "src/utils/services/transaction.service";
import { CreateMembershipDto, MembershipListDto } from "../dto/membership.dto";
import { ENUM_ROLE_TYPE } from "src/role/enums/role.enum";
import { StaffProfileDetails } from "src/staff/schemas/staff.schema";
@Injectable()
export class MembershipService {
    constructor(
        @InjectModel(membership.name) private readonly MembershipModel: Model<membership>,
        @InjectModel(StaffProfileDetails.name) private StaffProfileModel: Model<StaffProfileDetails>,
        
        private readonly transactionService: TransactionService, // Transaction service for managing transactions
    ) { }

    private async getOrganizationId(user: any): Promise<any> {
        const { role } = user;
        if(role.type == ENUM_ROLE_TYPE.ORGANIZATION){
            return user._id;
        }
        else if(role.type == ENUM_ROLE_TYPE.WEB_MASTER || role.type == ENUM_ROLE_TYPE.FRONT_DESK_ADMIN || role.type == ENUM_ROLE_TYPE.TRAINER){
            let staffDetails = await this.StaffProfileModel.findOne({ userId: user._id })
            return staffDetails.organizationId;
        }
        throw new BadRequestException("User not found");
    }
    async createMembership(createMembershipdto: CreateMembershipDto, user: any): Promise<any> {
        let organizationId = await this.getOrganizationId(user);
        const session = await this.transactionService.startTransaction();
        try {
            let data = {
                name: createMembershipdto.name,
                prefix: createMembershipdto.prefix,
                counter: createMembershipdto.counter,
                organizationId: organizationId
            };
            let createMemebership = new this.MembershipModel(data)
            let membershipDetail = await createMemebership.save({ session });
            await this.transactionService.commitTransaction(session);
            return membershipDetail
        } catch (error) {
            console.log(error)
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }
    async getMembershipList(membershipdto: MembershipListDto, user: any): Promise<any> {
        try {
            const { search, isActive, limit, skip } = membershipdto;
            const { role } = user;
            let query: any = {}
            if (role.type == ENUM_ROLE_TYPE.ORGANIZATION) {
                query["organizationId"] = user._id
            }
            else if (role.type == ENUM_ROLE_TYPE.WEB_MASTER || role.type == ENUM_ROLE_TYPE.FRONT_DESK_ADMIN || role.type == ENUM_ROLE_TYPE.TRAINER ) {
                let staffDetails = await this.StaffProfileModel.findOne({ userId: user._id })
                query["organizationId"] = staffDetails.organizationId
            }

            if (search) {
                query.name = { $regex: search, $options: 'i' }; // Case-insensitive search
            }

            if (typeof isActive === 'boolean') {
                query.isActive = isActive;
            }

            const memberShipList = await this.MembershipModel
                .find(query)
                .skip(skip)
                .limit(limit)
                .sort({ updatedAt: -1 })
                .exec();
            return memberShipList;
        } catch (error) {
            console.log(error)
            throw error;
        }

    }
    async getMembershipById(user: any, membershipId: any): Promise<any> {
        try {
            const query: any = { _id: membershipId };
            const { role } = user;
            if (role.type == ENUM_ROLE_TYPE.ORGANIZATION) {
                query["organizationId"] = user._id
            }
            else if (role.type == ENUM_ROLE_TYPE.WEB_MASTER || role.type == ENUM_ROLE_TYPE.FRONT_DESK_ADMIN || role.type == ENUM_ROLE_TYPE.TRAINER) {
                let staffDetails = await this.StaffProfileModel.findOne({ userId: user._id })
                query["organizationId"] = staffDetails.organizationId
            }
            const membershipDetail = await this.MembershipModel.findOne(query);
            return membershipDetail;
        } catch (error) {
            throw error;
        }
    }
    async updateMembershipStatus(isActive: any, membershipId: string) {
        const session = await this.transactionService.startTransaction();
        try {
            const updateMembershipStatus = await this.MembershipModel.updateOne({
                _id: membershipId
            },
                {
                    $set: { isActive: isActive.isActive || false },
                },
                {
                    session,
                },
            )
            if (!updateMembershipStatus) throw new BadRequestException("Membership not found");
            await this.transactionService.commitTransaction(session);
            return updateMembershipStatus;
        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            session.endSession();
        }
    }
    async updateMembershipCounter(counter: any, membershipId: string) {
        const session = await this.transactionService.startTransaction();
        try {

        } catch (error) {
            await this.transactionService.abortTransaction(session);
            throw error;
        }
        finally {
            session.endSession();
        }
    }
    async updateMembership(updateMembershipDto: CreateMembershipDto, membershipId: string, organizationId: any): Promise<any> {
        const session = await this.transactionService.startTransaction();
        try {
            // Fetch the current feature details
            const existingMembership = await this.MembershipModel.findOne({ _id: membershipId, organizationId });



            // Prepare data for update
            let dataToUpdate = {
                name: updateMembershipDto.name,
                isActive: existingMembership.isActive,
                prefix: updateMembershipDto.prefix,
                counter: updateMembershipDto.counter
            };
            // Find the feature by ID and update it
            const updatedFeature = await this.MembershipModel.findOneAndUpdate(
                { _id: membershipId, organizationId },
                { $set: dataToUpdate },
                { new: true, session } // Return the updated document
            );

            if (!updatedFeature) {
                throw new Error('Failed to update Membership.');
            }

            // Commit the transaction
            await this.transactionService.commitTransaction(session);
            return updatedFeature;
        } catch (error) {
            // Abort the transaction in case of an error
            await this.transactionService.abortTransaction(session);
            throw error;
        } finally {
            // End the session
            session.endSession();
        }
    }
}