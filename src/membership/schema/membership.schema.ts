import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import { ObjectId, SchemaTypes, Document,Types } from 'mongoose';

@Schema({ timestamps: true })
export class membership extends Document {
    @Prop({ type: String, required: true })
    name: string;
    @Prop({ type: String, required: true })
    prefix: string;
    @Prop({ type: Boolean, default: true })
    isActive: boolean;
    @Prop({ type: SchemaTypes.ObjectId, ref: "User", required: true, index: true })
    organizationId: Types.ObjectId;
    @Prop({ type: Number, required: true })
    counter: number;
    @Prop({ type: Number, required: false,default:0 })
    lastcounter?: number;
}

export const MembershipSchema = SchemaFactory.createForClass(membership);
