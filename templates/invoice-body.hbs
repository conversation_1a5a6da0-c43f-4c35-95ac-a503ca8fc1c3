<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f9f9f9;
        }

        .invoice-container {
            width: 90%;
            max-width: 800px;
            margin: 20px auto;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            overflow: hidden;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }

        .success-message {
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            color: #4CAF50;
            margin: 20px 0;
        }

        .info-section {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
        }

        .info-box {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            background: #f9f9f9;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }

        .table th {
            background: #8143D1;
            color: #fff;
        }

        .totals {
            text-align: right;
            margin-top: 20px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }

        .totals p {
            margin: 10px 0;
            font-size: 16px;
        }

        .totals .grand-total {
            font-size: 18px;
            font-weight: bold;
        }

        .Amount-In-Words {
            margin-top: 20px;
            font-weight: 700;
            color: #333;
        }
    </style>
</head>

<body>
    <div class="invoice-container">
        <!-- Header -->
        <div class="header">
            <h1>Order Confirmation</h1>
        </div>

        <!-- Message -->
        <div class="message">
            Thank you for your purchase! Please find attached the Invoice for your records. Below is the summary of your
            transaction:
        </div>
        <table class="table">
            <thead>
                <tr>
                    <th>Sr. No.</th>
                    <th>Item</th>
                    <th>QTY</th>
                    <th>Validity</th>
                    <th>Unit Price (₹)</th>
                    <th>Discount (₹)</th>
                    <th>HSN/SAC</th>
                    <th>GST%</th>
                </tr>
            </thead>
            <tbody>
                {{#each purchaseItems}}
                <tr>
                    <td>{{inc @index}}</td>
                    <td>{{this.packageName}}</td>
                    <td>{{this.quantity}}</td>
                    <td>{{this.expireIn}} {{this.durationUnit}}</td>
                    <td>
                        {{#if this.isInclusiveofGst}}
                        {{this.finalPrice}}
                        {{else}}
                        {{this.unitPrice}}
                        {{/if}}
                    </td>
                    <td>{{this.discountExcludeCart}}</td>
                    <td>
                        {{#if this.hsnOrSacCode }}
                        {{this.hsnOrSacCode}}
                        {{else}}
                        NA
                        {{/if}}

                    </td>
                    <td>{{this.tax}}</td>
                </tr>
                {{/each}}
                {{#each customPackageItems}}
                <tr>
                    <td>{{inc @index}}</td>
                    <td>{{this.packageName}}</td>
                    <td>{{this.quantity}}</td>
                    <td>-</td> <!-- No validity for products -->
                    <td>{{this.unitPrice}}</td>
                    <td>{{this.discountExcludeCart}}</td>
                    <td>
                        {{#if this.hsnOrSacCode }}
                        {{this.hsnOrSacCode}}
                        {{else}}
                        NA
                        {{/if}}
                    </td>
                    <td>{{this.tax}}</td>
                </tr>
                {{/each}}
                {{#each productItem}}
                <tr>
                    <td>{{inc @index}}</td>
                    <td>{{this.productName}}</td>
                    <td>{{this.quantity}}</td>
                    <td>-</td> <!-- No validity for products -->
                    <td>{{this.salePrice}}</td>
                    <td>{{this.discountExcludeCart}}</td>
                    <td>
                        {{#if this.hsnOrSacCode }}
                        {{showZero this.hsnOrSacCode}}
                        {{else}}
                        NA
                        {{/if}}
                    </td>
                    <td>{{showZero this.tax}}</td>
                </tr>
                {{/each}}
            </tbody>
        </table>

        <div class="totals">
            <p><span>Sub Total:</span> <span>₹ {{subTotal}}</span></p>
            <p><span>Item Discount:</span> <span>₹ {{itemDiscount}}</span></p>
            {{#if (eq cartDiscountType 'Flat')}}
            <p>
                <span>Cart Discount (₹{{cartDiscount}}):</span>
                <span>₹ {{cartDiscountAmount}}</span>
            </p>
            {{else}}
            <p>
                <span>Cart Discount ({{cartDiscount}}%):</span>
                <span>₹ {{cartDiscountAmount}}</span>
            </p>
            {{/if}}
            <p><span>Return Discount :</span> <span>₹ - {{returnDiscount}}</span></p>
            <p><span>GST:</span> <span>₹ {{totalGstValue}}</span></p>
            <p><span>Total:</span> <span>₹ {{totalAmountAfterGst}}</span></p>
            <p><span>Round Off:</span> <span>₹ - {{roundOff}}</span></p>
            <p class="grand-total"><span>Grand Total:</span> <span>₹ {{grandTotal}}</span></p>
        </div>

        <p class="Amount-In-Words"><span>Amount In Words:</span>
            <span>{{amountInWords}}</span>
        </p>

        <!-- Footer -->
        <div class="footer">
            If you have any questions, feel free to contact us. We appreciate your business and look forward to serving
            you again!
        </div>
    </div>
</body>

</html>