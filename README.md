<p align="center">
  <img src="https://nestjs.com/img/logo-small.svg" width="200" alt="Nest Logo" />
</p>

<p align="center">Gym Management System - A comprehensive fitness facility management platform</p>

<p align="center">
  <a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/v/@nestjs/core.svg" alt="NPM Version" /></a>
  <a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/l/@nestjs/core.svg" alt="Package License" /></a>
  <img src="https://img.shields.io/badge/node-%3E%3D22.0.0-brightgreen.svg" alt="Node Version" />
  <img src="https://img.shields.io/badge/typescript-%5E5.0.0-blue.svg" alt="TypeScript" />
</p>

## Description

A modern gym management system built with [NestJS](https://nestjs.com/) framework. This application provides comprehensive facility management, member tracking, payment processing, and reporting capabilities for fitness centers and gyms.

## Features

- 🏋️ **Facility Management** - Multi-facility support with location-based operations
- 👥 **Member Management** - Complete member lifecycle management
- 💳 **Payment Processing** - Multiple payment methods with reconciliation
- 📊 **Reporting** - Z-Out reports, invoice generation, and analytics
- 🔐 **Authentication & Authorization** - JWT-based auth with role-based permissions
- 📧 **Email System** - Automated notifications and communications
- 🎯 **Promotions** - Flexible promotion and pricing management
- 📱 **API Documentation** - Comprehensive Swagger documentation
- 🔄 **Real-time Updates** - WebSocket support for live updates

## Tech Stack

- **Framework**: NestJS (Node.js)
- **Database**: MongoDB with Mongoose
- **Authentication**: JWT with Passport
- **Documentation**: Swagger/OpenAPI
- **File Storage**: AWS S3
- **Email**: AWS SES / SMTP
- **Queue**: BullMQ with Redis
- **PDF Generation**: Puppeteer
- **Template Engine**: Handlebars
- **Validation**: Class Validator
- **Testing**: Jest

## Prerequisites

- Node.js (>= 22.0.0)
- MongoDB
- Redis (for queues)
- AWS Account (for S3 and SES)

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd gym-management-system

# Install dependencies
npm install
# or
yarn install

# Copy environment file
cp .env.example .env
```

## Environment Configuration

Create a `.env` file with the following variables:

```bash
# Application
NODE_ENV=development
PORT=3000
HOST_URL=http://localhost:3000
ADMIN_FRONTEND_APP_URL=http://localhost:3001

# Database
MONGODB_URI=mongodb://localhost:27017/gym-db

# JWT
JWT_SECRET=your-jwt-secret

# AWS Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET=your-s3-bucket
AWS_S3_REGION=us-east-1

# Email Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_FROM_ADDRESS=<EMAIL>
CONTACT_US_MAIL=<EMAIL>

# SMS Configuration (MSG91)
MSG91_AUTH_KEY=your-msg91-auth-key
MSG91_OTP_TEMPLATE_ID=your-template-id
MSG91_BASE_URL=https://api.msg91.com
```

## Running the Application

```bash
# Development mode
npm run start:dev

# Production mode
npm run build
npm run start:prod

# Debug mode
npm run start:debug
```

## Database Setup

```bash
# Seed permissions, policies, and roles
npm run migrate:seed:ppr

# Assign roles to users
npm run migrate:user:role
```

## API Documentation

Once the application is running, visit:

- **Swagger UI**: `http://localhost:3000/docs`
- **API Base URL**: `http://localhost:3000`

## Testing

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:cov

# Watch mode
npm run test:watch
```

## Project Structure

```
src/
├── common/           # Shared utilities and decorators
├── users/           # User management and services
├── role/            # Role-based access control
├── promotions/      # Promotion management
├── website-api/     # Public website APIs
├── utils/           # Utility services
├── migration/       # Database migrations and seeds
└── main.ts         # Application entry point

templates/           # Email and PDF templates
├── invoices.hbs
├── zout-report.hbs
├── facility-creation.hbs
└── organization-onboarding.hbs

image/              # Static assets
```

## Deployment

### Using PM2

```bash
# Build the application
npm run build

# Start with PM2
npm run start:staging
# or
npm run start:preprod
```

### Using Docker

```bash
# Build Docker image
docker build -t gym-management .

# Run container
docker run -p 3000:3000 gym-management
```

### GitHub Actions

The project includes automated deployment workflows:

- `.github/workflows/prod.deploy.yml` - Production deployment

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Code Quality

```bash
# Linting
npm run lint

# Code formatting
npm run format
```

## Support

For support and questions:

- Create an issue in the repository
- Contact: <<EMAIL>>

## License

This project is [UNLICENSED](LICENSE).

## Acknowledgments

- Built with [NestJS](https://nestjs.com/)
- Powered by [MongoDB](https://www.mongodb.com/)
- Deployed on AWS infrastructure
